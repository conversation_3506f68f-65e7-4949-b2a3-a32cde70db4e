from fastapi import APIRouter
from app.services.generate_potential_line_service import GeneratePotentialLineService
from app.models.generate_potential_line_model import GeneratePotentialLineRequest, GeneratePotentialLineResponse

router = APIRouter()
service = GeneratePotentialLineService()


@router.post("/llm/matrix/generate/potentialLine", response_model=GeneratePotentialLineResponse)
async def entity_extract_experience(request: GeneratePotentialLineRequest):
    return await service.generate_potential_line(request.text, request.llm_params)
