#!/bin/bash

# FunASR官方服务测试脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🧪 FunASR官方服务测试脚本${NC}"
echo "========================================"

# 加载配置
load_config() {
    if [ -f "config.env" ]; then
        source config.env
    else
        echo -e "${YELLOW}⚠️ 未找到config.env，使用默认配置${NC}"
        NGINX_PORT=8080
        BASE_PORT=10095
        GPU_COUNT=4
    fi
}

# 创建测试音频文件
create_test_audio() {
    echo -e "${YELLOW}📝 创建测试音频文件...${NC}"
    
    # 创建一个简单的测试音频文件 (静音WAV文件)
    test_file="/tmp/test_funasr.wav"
    
    # 生成1秒的静音WAV文件
    dd if=/dev/zero of="$test_file" bs=1024 count=44 2>/dev/null
    
    # 添加WAV文件头 (44字节)
    printf "RIFF\x28\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x40\x1f\x00\x00\x80\x3e\x00\x00\x02\x00\x10\x00data\x04\x00\x00\x00" > "$test_file.tmp"
    dd if=/dev/zero bs=1024 count=1 2>/dev/null >> "$test_file.tmp"
    mv "$test_file.tmp" "$test_file"
    
    echo -e "${GREEN}✅ 测试音频文件创建完成: $test_file${NC}"
}

# 基础连通性测试
basic_connectivity_test() {
    echo -e "${YELLOW}🔗 基础连通性测试...${NC}"
    
    nginx_port=${NGINX_PORT:-8080}
    
    # 测试负载均衡器健康检查
    echo -n "测试负载均衡器健康检查..."
    if curl -f -s http://localhost:$nginx_port/health > /dev/null; then
        echo -e " ${GREEN}✅${NC}"
    else
        echo -e " ${RED}❌${NC}"
        return 1
    fi
    
    # 测试集群健康检查
    echo -n "测试集群健康检查..."
    if curl -f -s http://localhost:$nginx_port/cluster/health > /dev/null; then
        echo -e " ${GREEN}✅${NC}"
    else
        echo -e " ${RED}❌${NC}"
        return 1
    fi
    
    # 测试各个实例
    base_port=${BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((base_port + i))
        echo -n "测试GPU-$i实例(端口$port)..."
        if curl -f -s http://localhost:$port/health > /dev/null; then
            echo -e " ${GREEN}✅${NC}"
        else
            echo -e " ${RED}❌${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ 基础连通性测试完成${NC}"
}

# 官方API测试
official_api_test() {
    echo -e "${YELLOW}🎯 官方API测试...${NC}"
    
    nginx_port=${NGINX_PORT:-8080}
    test_file="/tmp/test_funasr.wav"
    
    # 测试官方fileASR接口
    echo -n "测试官方fileASR接口..."
    response=$(curl -X POST -F "file=@$test_file" \
                   http://localhost:$nginx_port/fileASR \
                   -s -w "%{http_code}" -o /tmp/funasr_response.json)
    
    http_code=$(echo $response | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        echo -e " ${GREEN}✅${NC}"
        echo -e "${CYAN}响应内容:${NC}"
        cat /tmp/funasr_response.json | jq '.' 2>/dev/null || cat /tmp/funasr_response.json
        echo ""
    else
        echo -e " ${RED}❌ HTTP状态码: $http_code${NC}"
        cat /tmp/funasr_response.json 2>/dev/null || echo "无响应内容"
    fi
    
    # 测试兼容接口
    echo -n "测试兼容接口(/llm/asr/recognition)..."
    response=$(curl -X POST -F "file=@$test_file" \
                   http://localhost:$nginx_port/llm/asr/recognition \
                   -s -w "%{http_code}" -o /tmp/funasr_compat_response.json)
    
    http_code=$(echo $response | tail -c 4)
    
    if [ "$http_code" = "200" ]; then
        echo -e " ${GREEN}✅${NC}"
    else
        echo -e " ${RED}❌ HTTP状态码: $http_code${NC}"
    fi
    
    echo -e "${GREEN}✅ 官方API测试完成${NC}"
}

# 负载均衡测试
load_balance_test() {
    echo -e "${YELLOW}⚖️ 负载均衡测试...${NC}"
    
    nginx_port=${NGINX_PORT:-8080}
    test_file="/tmp/test_funasr.wav"
    
    echo "发送10个请求测试负载均衡..."
    
    for i in {1..10}; do
        echo -n "请求 $i: "
        response=$(curl -X POST -F "file=@$test_file" \
                       http://localhost:$nginx_port/fileASR \
                       -s -w "%{http_code}" -o /dev/null)
        
        if [ "$response" = "200" ]; then
            echo -e "${GREEN}✅${NC}"
        else
            echo -e "${RED}❌ ($response)${NC}"
        fi
        
        sleep 0.5
    done
    
    echo -e "${GREEN}✅ 负载均衡测试完成${NC}"
}

# 并发测试
concurrent_test() {
    echo -e "${YELLOW}🚀 并发测试...${NC}"
    
    nginx_port=${NGINX_PORT:-8080}
    test_file="/tmp/test_funasr.wav"
    concurrent_count=20
    
    echo "启动 $concurrent_count 个并发请求..."
    
    start_time=$(date +%s)
    success_count=0
    
    for i in $(seq 1 $concurrent_count); do
        (
            response=$(curl -X POST -F "file=@$test_file" \
                           http://localhost:$nginx_port/fileASR \
                           -s -w "%{http_code}" -o /dev/null 2>/dev/null)
            
            if [ "$response" = "200" ]; then
                echo "✅ 请求 $i 成功"
            else
                echo "❌ 请求 $i 失败 ($response)"
            fi
        ) &
    done
    
    # 等待所有后台任务完成
    wait
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    # 统计成功数量
    success_count=$(jobs -p | wc -l)
    
    echo -e "\n${CYAN}📊 并发测试结果:${NC}"
    echo "  并发请求数: $concurrent_count"
    echo "  测试时长: ${duration}秒"
    echo "  平均QPS: $(echo "scale=2; $concurrent_count / $duration" | bc 2>/dev/null || echo "N/A")"
    
    echo -e "${GREEN}✅ 并发测试完成${NC}"
}

# 直接实例测试
direct_instance_test() {
    echo -e "${YELLOW}🎯 直接实例测试...${NC}"
    
    base_port=${BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    test_file="/tmp/test_funasr.wav"
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((base_port + i))
        echo -n "测试GPU-$i实例(端口$port)直接访问..."
        
        response=$(curl -X POST -F "file=@$test_file" \
                       http://localhost:$port/fileASR \
                       -s -w "%{http_code}" -o /dev/null)
        
        if [ "$response" = "200" ]; then
            echo -e " ${GREEN}✅${NC}"
        else
            echo -e " ${RED}❌ ($response)${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ 直接实例测试完成${NC}"
}

# 清理测试文件
cleanup() {
    echo -e "${YELLOW}🧹 清理测试文件...${NC}"
    
    rm -f /tmp/test_funasr.wav
    rm -f /tmp/funasr_response.json
    rm -f /tmp/funasr_compat_response.json
    
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 完整测试
full_test() {
    echo -e "${BLUE}🔬 执行完整测试套件${NC}"
    echo ""
    
    load_config
    create_test_audio
    
    echo ""
    basic_connectivity_test
    
    echo ""
    official_api_test
    
    echo ""
    load_balance_test
    
    echo ""
    concurrent_test
    
    echo ""
    direct_instance_test
    
    echo ""
    cleanup
    
    echo ""
    echo -e "${GREEN}🎉 所有测试完成！${NC}"
}

# 主函数
main() {
    case $1 in
        "basic")
            load_config
            basic_connectivity_test
            ;;
        "api")
            load_config
            create_test_audio
            official_api_test
            cleanup
            ;;
        "balance")
            load_config
            create_test_audio
            load_balance_test
            cleanup
            ;;
        "concurrent")
            load_config
            create_test_audio
            concurrent_test
            cleanup
            ;;
        "direct")
            load_config
            create_test_audio
            direct_instance_test
            cleanup
            ;;
        "full"|"")
            full_test
            ;;
        *)
            echo "用法: $0 {basic|api|balance|concurrent|direct|full}"
            echo ""
            echo "  basic      - 基础连通性测试"
            echo "  api        - 官方API测试"
            echo "  balance    - 负载均衡测试"
            echo "  concurrent - 并发测试"
            echo "  direct     - 直接实例测试"
            echo "  full       - 完整测试套件 (默认)"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
