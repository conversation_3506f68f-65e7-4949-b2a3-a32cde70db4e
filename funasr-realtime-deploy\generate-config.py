#!/usr/bin/env python3
"""
FunASR实时转写配置生成器
根据config.env动态生成docker-compose.yml和nginx.conf
支持HTTP + WebSocket双服务架构
"""

import os
import sys
from pathlib import Path

current_dir = Path(__file__).resolve().parent
def load_config():
    """加载配置文件"""
    # 获取当前文件所在目录
    config = {}
    config_file = current_dir / "config.env"
    print(config_file.absolute())

    if not config_file.exists():
        print("❌ config.env文件不存在")
        sys.exit(1)

    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 处理多行字符串配置
    lines = content.split('\n')
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line and not line.startswith('#') and '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()

            # 如果值是单独的引号或以引号开始，说明是多行配置
            if value == '"' or (value.startswith('"') and not value.endswith('"')):
                # 收集多行内容
                multiline_value = []
                i += 1
                while i < len(lines):
                    next_line = lines[i].strip()
                    if next_line == '"':
                        # 找到结束引号
                        break
                    elif next_line and not next_line.startswith('#'):
                        multiline_value.append(next_line)
                    i += 1

                config[key] = '\n'.join(multiline_value)
            else:
                # 移除引号和行内注释
                value = value.strip('"')
                # 处理行内注释（以 # 开头的部分）
                if '#' in value:
                    value = value.split('#')[0].strip()
                config[key] = value
        i += 1

    return config


def parse_gpu_instances(gpu_instances_str):
    """解析GPU实例配置"""
    instances = []

    for line in gpu_instances_str.strip().split('\n'):
        line = line.strip()
        if line and not line.startswith('#'):
            parts = line.split(':')
            if len(parts) >= 6:
                instances.append({
                    'name': parts[0],
                    'gpu_id': parts[1],
                    'http_port': parts[2],
                    'wss_port': parts[3],
                    'memory': parts[4],
                    'cpu': parts[5]
                })

    return instances


def generate_docker_compose(config, instances):
    """生成docker-compose.yml"""
    
    compose_content = f"""version: '3.8'

# FunASR实时转写集群 - 动态生成配置
# 实例数量: {len(instances)}个 (HTTP + WebSocket)

services:
"""
    
    # 生成HTTP和WebSocket服务配置
    for i, instance in enumerate(instances):
        # HTTP服务
        compose_content += f"""  # HTTP服务 - {instance['name']}
  funasr-http-{instance['name']}:
    image: {config['FUNASR_IMAGE']}
    container_name: funasr-http-{instance['name']}
    ports:
      - "{instance['http_port']}:{instance['http_port']}"
    environment:
      - CUDA_VISIBLE_DEVICES={instance['gpu_id']}
      - WORKERS={config.get('WORKERS_PER_INSTANCE', '4')}
      - MAX_REQUESTS={config.get('MAX_REQUESTS', '1000')}
      - TIMEOUT={config.get('TIMEOUT', '300')}
      - LOG_LEVEL={config.get('LOG_LEVEL', 'info')}
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR HTTP服务 {instance['name']}...' &&
        python server.py --host 0.0.0.0 --port {instance['http_port']}
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['{instance['gpu_id']}']
              capabilities: [gpu]
        limits:
          memory: {instance['memory']}
          cpus: '{instance['cpu']}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{instance['http_port']}/health"]
      interval: {config.get('HEALTH_CHECK_INTERVAL', '30s')}
      timeout: {config.get('HEALTH_CHECK_TIMEOUT', '10s')}
      retries: {config.get('HEALTH_CHECK_RETRIES', '3')}
      start_period: {config.get('HEALTH_CHECK_START_PERIOD', '120s')}
    networks:
      - funasr-network

  # WebSocket服务 - {instance['name']}
  funasr-wss-{instance['name']}:
    image: {config['FUNASR_IMAGE']}
    container_name: funasr-wss-{instance['name']}
    ports:
      - "{instance['wss_port']}:{instance['wss_port']}"
    environment:
      - CUDA_VISIBLE_DEVICES={instance['gpu_id']}
      - WORKERS={config.get('WORKERS_PER_INSTANCE', '4')}
      - MAX_CONNECTIONS={config.get('WSS_MAX_CONNECTIONS', '50')}
      - CHUNK_SIZE={config.get('WSS_CHUNK_SIZE', '"[5, 10, 5]"')}
      - CHUNK_INTERVAL={config.get('WSS_CHUNK_INTERVAL', '10')}
      - LOG_LEVEL={config.get('LOG_LEVEL', 'info')}
    working_dir: /workspace/FunASR/runtime/python/websocket
    command: >
      bash -c "
        echo '🚀 启动FunASR WebSocket服务 {instance['name']}...' &&
        python funasr_wss_server.py --host 0.0.0.0 --port {instance['wss_port']}
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['{instance['gpu_id']}']
              capabilities: [gpu]
        limits:
          memory: {instance['memory']}
          cpus: '{instance['cpu']}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{instance['wss_port']}/"]
      interval: {config.get('HEALTH_CHECK_INTERVAL', '30s')}
      timeout: {config.get('HEALTH_CHECK_TIMEOUT', '10s')}
      retries: {config.get('HEALTH_CHECK_RETRIES', '3')}
      start_period: {config.get('HEALTH_CHECK_START_PERIOD', '120s')}
    networks:
      - funasr-network

"""
    
    # 生成负载均衡器配置
    http_depends_on = [f'funasr-http-{instance["name"]}' for instance in instances]
    wss_depends_on = [f'funasr-wss-{instance["name"]}' for instance in instances]
    all_depends_on = http_depends_on + wss_depends_on
    
    compose_content += f"""  # Nginx负载均衡器 (支持HTTP + WebSocket)
  funasr-loadbalancer:
    image: nginx:alpine
    container_name: funasr-realtime-lb
    ports:
      - "{config['NGINX_PORT']}:80"
    volumes:
      - ./nginx.generated.conf:/etc/nginx/nginx.conf:ro
    depends_on:
{chr(10).join(f'      - {name}' for name in all_depends_on)}
    restart: unless-stopped
    networks:
      - funasr-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: funasr-realtime-prometheus
    ports:
      - "{config['PROMETHEUS_PORT']}:9090"
    volumes:
      - ./prometheus.generated.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time={config.get("LOG_RETENTION_DAYS", "7")}d'
    networks:
      - funasr-network
    profiles:
      - monitoring

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: funasr-realtime-grafana
    ports:
      - "{config['GRAFANA_PORT']}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - funasr-network
    profiles:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  funasr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
"""
    
    return compose_content


def generate_nginx_conf(config, instances):
    """生成nginx.conf"""
    
    # 生成upstream配置
    http_upstream_servers = []
    wss_upstream_servers = []
    health_servers = []
    
    for instance in instances:
        http_upstream_servers.append(f"        server funasr-http-{instance['name']}:{instance['http_port']} weight=1 max_fails=3 fail_timeout=30s;")
        wss_upstream_servers.append(f"        server funasr-wss-{instance['name']}:{instance['wss_port']} weight=1 max_fails=3 fail_timeout=30s;")
        health_servers.append(f"        server funasr-http-{instance['name']}:{instance['http_port']};")
    
    nginx_content = f"""events {{
    worker_connections 4096;
    use epoll;
    multi_accept on;
}}

http {{
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format funasr_realtime '$remote_addr - $remote_user [$time_local] '
                               '"$request" $status $body_bytes_sent '
                               '"$http_referer" "$http_user_agent" '
                               'rt=$request_time uct="$upstream_connect_time" '
                               'uht="$upstream_header_time" urt="$upstream_response_time" '
                               'upstream="$upstream_addr" upgrade="$http_upgrade"';
    
    access_log /var/log/nginx/funasr_access.log funasr_realtime;
    error_log  /var/log/nginx/funasr_error.log warn;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # WebSocket配置
    map $http_upgrade $connection_upgrade {{
        default upgrade;
        '' close;
    }}
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        application/json
        application/javascript
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # HTTP文件识别集群
    upstream funasr_http_cluster {{
        # {len(instances)}个HTTP实例
{chr(10).join(http_upstream_servers)}
        
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }}
    
    # WebSocket实时转写集群
    upstream funasr_wss_cluster {{
        # 使用IP哈希确保WebSocket连接的粘性
        ip_hash;
        
        # {len(instances)}个WebSocket实例
{chr(10).join(wss_upstream_servers)}
    }}
    
    # 健康检查上游
    upstream funasr_health {{
{chr(10).join(health_servers)}
    }}
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=funasr_http_limit:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=funasr_wss_limit:10m rate=10r/s;
    limit_conn_zone $binary_remote_addr zone=funasr_conn:10m;
    
    # 主服务器配置
    server {{
        listen 80;
        server_name localhost;
        
        # 基本配置
        client_body_timeout 300s;
        client_header_timeout 60s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 健康检查端点
        location /health {{
            access_log off;
            return 200 "FunASR Realtime Load Balancer OK\\n";
            add_header Content-Type text/plain;
        }}
        
        # 集群健康检查
        location /cluster/health {{
            proxy_pass http://funasr_health/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }}
        
        # WebSocket实时转写接口
        location /ws/asr {{
            limit_req zone=funasr_wss_limit burst=20 nodelay;
            limit_conn funasr_conn 10;
            
            proxy_pass http://funasr_wss_cluster;
            
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-funasr-realtime-lb";
            
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_connect_timeout 60s;
            
            proxy_buffering off;
            proxy_cache off;
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }}
        
        # HTTP文件识别接口
        location /fileASR {{
            limit_req zone=funasr_http_limit burst=200 nodelay;
            limit_conn funasr_conn 50;
            
            proxy_pass http://funasr_http_cluster;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-funasr-realtime-lb";
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            proxy_temp_file_write_size 64k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }}
        
        # 兼容接口
        location /llm/asr/recognition {{
            limit_req zone=funasr_http_limit burst=200 nodelay;
            limit_conn funasr_conn 50;
            
            proxy_pass http://funasr_http_cluster/fileASR;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }}
        
        # Nginx状态页面
        location /nginx_status {{
            stub_status on;
            access_log off;
            allow **********/16;
            deny all;
        }}
        
        # 默认路由
        location / {{
            proxy_pass http://funasr_http_cluster;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }}
    }}
"""
    
    # 生成直接访问各实例的配置
    for i, instance in enumerate(instances):
        http_direct_port = 8001 + i
        wss_direct_port = 8011 + i
        
        nginx_content += f"""
    # 直接访问HTTP实例 - {instance['name']}
    server {{
        listen {http_direct_port};
        server_name localhost;
        
        location / {{
            proxy_pass http://funasr-http-{instance['name']}:{instance['http_port']};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-http-{instance['name']}";
        }}
    }}

    # 直接访问WebSocket实例 - {instance['name']}
    server {{
        listen {wss_direct_port};
        server_name localhost;

        location / {{
            proxy_pass http://funasr-wss-{instance['name']}:{instance['wss_port']};
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-wss-{instance['name']}";
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
        }}
    }}"""
    
    nginx_content += "\n}\n"
    
    return nginx_content


def generate_prometheus_yml(config, instances):
    """生成prometheus.yml"""

    http_targets = [f"'funasr-http-{instance['name']}:{instance['http_port']}'" for instance in instances]
    wss_targets = [f"'funasr-wss-{instance['name']}:{instance['wss_port']}'" for instance in instances]
    
    prometheus_content = f"""global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # FunASR HTTP实例监控
  - job_name: 'funasr-http-instances'
    static_configs:
      - targets: 
{chr(10).join(f'          - {target}' for target in http_targets)}
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # FunASR WebSocket实例监控
  - job_name: 'funasr-wss-instances'
    static_configs:
      - targets: 
{chr(10).join(f'          - {target}' for target in wss_targets)}
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Nginx负载均衡器监控
  - job_name: 'funasr-loadbalancer'
    static_configs:
      - targets: ['funasr-loadbalancer:80']
    metrics_path: '/nginx_status'
    scrape_interval: 15s

  # 系统监控 (如果有node_exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s
"""
    
    return prometheus_content


def main():
    """主函数"""
    print("🔧 生成FunASR实时转写集群配置...")
    
    # 加载配置
    config = load_config()

    # 解析GPU实例配置
    if 'GPU_INSTANCES' not in config:
        print("❌ config.env中未找到GPU_INSTANCES配置")
        sys.exit(1)

    instances = parse_gpu_instances(config['GPU_INSTANCES'])

    if not instances:
        print("❌ 未找到有效的GPU实例配置")
        sys.exit(1)

    print(f"📊 检测到 {len(instances)} 个GPU实例:")
    for i, instance in enumerate(instances):
        print(f"  {i+1}. {instance['name']} - GPU:{instance['gpu_id']} - HTTP:{instance['http_port']} - WSS:{instance['wss_port']}")
    
    # 生成配置文件
    print("\n📝 生成配置文件...")
    
    # 生成docker-compose.yml
    compose_content = generate_docker_compose(config, instances)
    with open(current_dir / 'docker-compose.generated.yml', 'w', encoding='utf-8') as f:
        f.write(compose_content)
    print("  ✅ docker-compose.generated.yml")
    
    # 生成nginx.conf
    nginx_content = generate_nginx_conf(config, instances)
    with open(current_dir / 'nginx.generated.conf', 'w', encoding='utf-8') as f:
        f.write(nginx_content)
    print("  ✅ nginx.generated.conf")
    
    # 生成prometheus.yml
    prometheus_content = generate_prometheus_yml(config, instances)
    with open(current_dir / 'prometheus.generated.yml', 'w', encoding='utf-8') as f:
        f.write(prometheus_content)
    print("  ✅ prometheus.generated.yml")
    
    print(f"\n🎉 配置生成完成！")
    print(f"📋 实例数量: {len(instances)} (每个GPU运行HTTP+WebSocket双服务)")
    print(f"🌐 负载均衡器端口: {config['NGINX_PORT']}")
    print(f"📊 监控端口: Prometheus({config['PROMETHEUS_PORT']}), Grafana({config['GRAFANA_PORT']})")
    print(f"\n🚀 部署命令:")
    print(f"  docker-compose -f docker-compose.generated.yml up -d")


if __name__ == "__main__":
    main()
