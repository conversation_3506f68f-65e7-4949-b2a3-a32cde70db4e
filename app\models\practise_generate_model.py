
from enum import Enum
from typing import List, Optional, Union
from pydantic import BaseModel, Field

from app.models.practise_analysis_model import PractiseType, DifficultyLevel

class PrecisionMode(str, Enum):
    """精度模式枚举"""
    FAST = "fast"  # 快速模式：单智能体生成
    HIGH = "high"  # 高精度模式：多智能体协作

class PractiseGenerateRequest(BaseModel):
    content: str = Field(..., example="这是一条题目内容或者知识点")
    subject: str = Field(..., example="小学语文")
    practise_type: PractiseType = Field(..., description="题目类型")
    num_of_practise: int = Field(2, description="生成的题目数量")
    difficulty: DifficultyLevel = Field(
        DifficultyLevel.medium,
        description="题目难度：简单、中等、困难、极难"
    )
    precision_mode: PrecisionMode = Field(
        PrecisionMode.FAST,
        description="精度模式：fast(快速单智能体) 或 high(高精度多智能体协作)"
    )
    knowledge_point_id: Optional[str] = Field(
        None,
        example="kp_12345",
        description="知识点ID，用于查询该知识点下已有的题目以避免重复"
    )

    llm_params: dict = Field(
        {},
        example={
            "temperature": 0.0,
            "top_p": 0.9,
        },
        description="llm模型参数"
    )


class SingleChoiceAnswer(BaseModel):
    value: str  # 必须是选项中存在的id（如"A"/"T"）

class MultipleChoiceAnswer(BaseModel):
    value: List[str]  # 必须是选项中存在的id列表（如["A", "C"]）

class TextAnswer(BaseModel):
    value: str  # 正确答案的文本内容


class Option(BaseModel):
    id: str  # 选项标识（如"A"/"T"）
    text: str  # 选项文本（如"正确"/"北京"）

class PractiseGenerateResponse(BaseModel):
    practise_type: PractiseType = Field(
        ..., 
        description="正确答案（格式由题目类型决定）"
    )
    content: str = Field(
        ..., 
        min_length=1, 
        description="题目正文内容（必填，至少1个字符）"
    )
    options: List[Option] = Field(
        ..., 
        description="选项列表（简答/论述题需传空列表）"
    )
    answer: Union[SingleChoiceAnswer, MultipleChoiceAnswer, TextAnswer] = Field(
        ..., 
        description="正确答案（格式由题目类型决定）"
    )

class PractiseGenerateResponseList(BaseModel):
    practise_list: List[PractiseGenerateResponse] = Field(
        ..., 
        min_items=1,  # 至少包含1个练习题
        description="练习题列表（JSON数组，每个元素为一个练习题对象）"
    )