#!/usr/bin/env python3
"""
AI服务平台测试示例
"""

import asyncio
import sys
import os

# 添加SDK路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'sdk'))

from ai_client import AIClient


async def test_ai_services():
    """测试AI服务"""
    print("🧪 开始测试AI服务平台...")
    
    # 创建客户端
    client = AIClient("http://localhost:8000", tenant_id="test")
    
    try:
        # 1. 测试服务列表
        print("\n1️⃣ 测试服务列表...")
        services = await client.get_services()
        print(f"✅ 可用服务: {services}")
        
        # 2. 测试OCR服务
        print("\n2️⃣ 测试OCR服务...")
        ocr_result = await client.ocr(
            image_url="https://example.com/test.jpg"
        )
        if ocr_result.success:
            print(f"✅ OCR成功: {ocr_result.text[:100]}...")
        else:
            print(f"❌ OCR失败: {ocr_result.error}")
        
        # 3. 测试TTS服务
        print("\n3️⃣ 测试TTS服务...")
        tts_result = await client.tts("Hello, this is a test message.")
        if tts_result.success:
            print(f"✅ TTS成功: {tts_result.audio_url}")
        else:
            print(f"❌ TTS失败: {tts_result.error}")
        
        # 4. 测试LLM服务
        print("\n4️⃣ 测试LLM服务...")
        llm_result = await client.chat([
            {"role": "user", "content": "你好，请介绍一下AI服务平台的优势"}
        ])
        if llm_result.success:
            print(f"✅ LLM成功: {llm_result.content[:100]}...")
        else:
            print(f"❌ LLM失败: {llm_result.error}")
        
        # 5. 测试ASR服务
        print("\n5️⃣ 测试ASR服务...")
        asr_result = await client.asr(
            audio_url="https://example.com/test.wav"
        )
        if asr_result.success:
            print(f"✅ ASR成功: {asr_result.text}")
        else:
            print(f"❌ ASR失败: {asr_result.error}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
    
    finally:
        await client.close()
    
    print("\n🎉 测试完成！")


async def test_education_platform():
    """测试教育平台"""
    print("\n📚 测试教育平台...")
    
    import httpx
    
    async with httpx.AsyncClient() as client:
        try:
            # 测试健康检查
            response = await client.get("http://localhost:8100/health")
            if response.status_code == 200:
                print("✅ 教育平台健康检查通过")
            else:
                print("❌ 教育平台健康检查失败")
            
            # 测试出题功能
            response = await client.post(
                "http://localhost:8100/api/questions/generate",
                json={
                    "topic": "数学",
                    "subject": "小学数学",
                    "difficulty": "medium",
                    "count": 3
                }
            )
            if response.status_code == 200:
                print("✅ 智能出题功能正常")
            else:
                print("❌ 智能出题功能异常")
                
        except Exception as e:
            print(f"❌ 教育平台测试失败: {e}")


async def test_medical_platform():
    """测试医疗平台"""
    print("\n🏥 测试医疗平台...")
    
    import httpx
    
    async with httpx.AsyncClient() as client:
        try:
            # 测试健康检查
            response = await client.get("http://localhost:8200/health")
            if response.status_code == 200:
                print("✅ 医疗平台健康检查通过")
            else:
                print("❌ 医疗平台健康检查失败")
            
            # 测试医疗咨询
            response = await client.post(
                "http://localhost:8200/api/consultation",
                data={"question": "头痛的常见原因有哪些？"}
            )
            if response.status_code == 200:
                print("✅ 医疗咨询功能正常")
            else:
                print("❌ 医疗咨询功能异常")
                
        except Exception as e:
            print(f"❌ 医疗平台测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 AI服务平台API测试")
    print("=" * 50)

    # 测试AI服务
    await test_ai_services()

    # 测试教育平台
    await test_education_platform()

    # 测试医疗平台
    await test_medical_platform()

    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    print("1. ✅ AI服务网关提供统一的API接入")
    print("2. ✅ 支持OCR、TTS、ASR、LLM等多种AI能力")
    print("3. ✅ 具备缓存、负载均衡、多租户等企业级特性")
    print("4. ✅ 教育和医疗平台可通过API或SDK调用")
    print("5. ✅ 所有接口支持Web端直接调用")
    print("\n✨ AI服务平台API测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
