"""
LangSmith 配置和监控设置
"""
import os
from typing import Optional

class LangSmithConfig:
    """LangSmith 配置管理"""
    
    def __init__(self):
        self.setup_environment()
    
    def setup_environment(self):
        """设置 LangSmith 环境变量"""
        
        # 启用 LangSmith 追踪
        os.environ.setdefault("LANGCHAIN_TRACING_V2", "true")
        
        # 设置项目名称
        os.environ.setdefault("LANGCHAIN_PROJECT", "LangGraph-Question-Generation")
        
        # 设置端点（如果需要自定义）
        # os.environ.setdefault("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com")
        
        print("🔧 LangSmith 环境配置完成")
        print(f"📊 项目名称: {os.environ.get('LANGCHAIN_PROJECT')}")
        print(f"🔍 追踪状态: {os.environ.get('LANGCHAIN_TRACING_V2')}")
    
    def set_api_key(self, api_key: str):
        """设置 LangSmith API Key"""
        if api_key and len(api_key) > 10:
            os.environ["LANGCHAIN_API_KEY"] = api_key
            print("🔑 LangSmith API Key 已设置")
            return True
        else:
            print("❌ 无效的 LangSmith API Key")
            return False

    def validate_api_key(self) -> bool:
        """验证 API Key 是否有效"""
        try:
            from langsmith import Client
            api_key = os.environ.get("LANGCHAIN_API_KEY")
            if not api_key:
                return False

            client = Client(api_key=api_key)
            # 尝试获取项目信息来验证 API Key
            client.list_projects(limit=1)
            print("✅ LangSmith API Key 验证成功")
            return True
        except Exception as e:
            print(f"❌ LangSmith API Key 验证失败: {e}")
            return False
    
    def get_project_url(self) -> Optional[str]:
        """获取项目 URL"""
        project_name = os.environ.get("LANGCHAIN_PROJECT")
        if project_name:
            # 注意：实际 URL 可能需要根据您的 LangSmith 设置调整
            return f"https://smith.langchain.com/projects/{project_name}"
        return None
    
    def print_monitoring_info(self):
        """打印监控信息"""
        print("\n🔍 LangSmith 监控信息")
        print("=" * 50)
        print(f"📊 项目名称: {os.environ.get('LANGCHAIN_PROJECT', 'N/A')}")
        print(f"🔍 追踪状态: {os.environ.get('LANGCHAIN_TRACING_V2', 'false')}")
        print(f"🔑 API Key: {'已设置' if os.environ.get('LANGCHAIN_API_KEY') else '未设置'}")
        
        project_url = self.get_project_url()
        if project_url:
            print(f"🌐 项目 URL: {project_url}")
        
        print("\n📝 监控功能:")
        print("  ✅ 工作流节点执行追踪")
        print("  ✅ LLM 调用监控")
        print("  ✅ 工具调用记录")
        print("  ✅ 状态变化追踪")
        print("  ✅ 错误和异常监控")
        print("  ✅ 性能指标收集")


def setup_langsmith(api_key: Optional[str] = "***************************************************") -> LangSmithConfig:
    """设置 LangSmith 监控"""
    
    config = LangSmithConfig()
    
    if api_key:
        config.set_api_key(api_key)
    else:
        print("⚠️ 未提供 LangSmith API Key")
        print("💡 要获取完整监控功能，请:")
        print("   1. 访问 https://smith.langchain.com/")
        print("   2. 注册账号并获取 API Key")
        print("   3. 调用 setup_langsmith(api_key='your-key')")
    
    config.print_monitoring_info()
    return config


if __name__ == "__main__":
    # 示例用法
    print("🔧 LangSmith 配置示例")
    
    # 基础设置（无 API Key）
    config = setup_langsmith()
    
    # 如果有 API Key，可以这样设置：
    # config = setup_langsmith(api_key="your-langsmith-api-key-here")
    
    print("\n🎉 LangSmith 配置完成！")
    print("现在可以在 LangGraph 工作流中看到详细的执行追踪。")
