# FunASR官方部署方案

## 📋 概述

使用FunASR官方提供的Docker镜像和HTTP服务，无需自定义代码，开箱即用的语音识别服务部署方案。

## 🎯 方案特点

- **纯官方**: 100%使用FunASR官方镜像和服务
- **开箱即用**: 无需修改任何代码
- **多实例**: 支持多GPU实例负载均衡
- **简单配置**: 通过环境变量和配置文件调整
- **标准接口**: 使用FunASR官方API接口

## 🏗️ 架构图

```
🌐 Nginx负载均衡器 :8080
    ├── FunASR官方实例1 :10095 (GPU-0)
    ├── FunASR官方实例2 :10096 (GPU-1)
    ├── FunASR官方实例3 :10097 (GPU-2)
    └── FunASR官方实例4 :10098 (GPU-3)

📊 监控系统
    ├── Prometheus :9090
    └── Grafana :3000
```

## 🖥️ 系统要求

### 硬件要求
- **CPU**: 16核心+
- **内存**: 64GB+
- **GPU**: 4张 × 8GB VRAM+
- **存储**: 500GB SSD+

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **NVIDIA Driver**: 470+
- **NVIDIA Docker**: 2.0+

## 📁 项目文件

```
funasr-official-deploy/
├── config.env                        # 部署配置文件
├── docker-compose.yml               # Docker Compose配置
├── nginx.conf                       # Nginx负载均衡配置
├── prometheus.yml                   # Prometheus监控配置
├── deploy.sh                        # 部署脚本
├── test.sh                          # 测试脚本
└── README.md                        # 本文档
```

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装NVIDIA Docker支持
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker

# 验证GPU支持
docker run --rm --gpus all registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1 nvidia-smi
```

### 2. 配置部署参数

编辑 `config.env` 文件：

```bash
# 基础配置
FUNASR_IMAGE=registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
NGINX_PORT=8080

# GPU实例配置
GPU_COUNT=4
BASE_PORT=10095

# 性能配置
WORKERS_PER_INSTANCE=4
```

### 3. 一键部署

```bash
# 进入项目目录
cd funasr-official-deploy

# 给脚本执行权限
chmod +x deploy.sh test.sh

# 一键部署
./deploy.sh deploy
```

### 4. 验证部署

```bash
# 检查服务状态
./deploy.sh check

# 运行测试
./test.sh
```

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `FUNASR_IMAGE` | funasr-runtime-sdk-gpu-0.2.1 | FunASR官方镜像 |
| `GPU_COUNT` | 4 | GPU实例数量 |
| `BASE_PORT` | 10095 | 起始端口号 |
| `NGINX_PORT` | 8080 | 负载均衡器端口 |
| `WORKERS_PER_INSTANCE` | 4 | 每实例工作进程数 |

### 端口分配

- **负载均衡器**: 8080
- **FunASR实例**: 10095-10098 (根据GPU_COUNT自动分配)
- **Prometheus**: 9090
- **Grafana**: 3000

## 📊 API接口

### 官方API接口

#### 文件上传识别
```bash
curl -X POST \
  -F "file=@audio.wav" \
  http://localhost:8080/fileASR
```

#### 健康检查
```bash
curl http://localhost:10095/health
```

### 响应格式

#### 成功响应
```json
{
  "code": 0,
  "message": "success",
  "result": [
    {
      "text": "识别的文本内容"
    }
  ]
}
```

## 🔧 服务管理

### 基本操作

```bash
# 启动服务
./deploy.sh deploy

# 检查状态
./deploy.sh check

# 查看日志
docker-compose logs -f

# 停止服务
./deploy.sh stop

# 清理环境
./deploy.sh clean
```

### 扩缩容

```bash
# 修改config.env中的GPU_COUNT
vim config.env

# 重新部署
./deploy.sh deploy
```

## 📈 性能指标

### 预期性能

| 配置 | 单实例QPS | 4实例集群QPS | 说明 |
|------|-----------|--------------|------|
| **官方默认** | 1,000 | 4,000 | 标准配置 |
| **优化配置** | 2,000 | 8,000 | 调整workers |

### 监控访问

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)

## 🔍 故障排查

### 常见问题

#### 1. 容器启动失败
```bash
# 查看日志
docker-compose logs funasr-gpu-0

# 检查GPU
nvidia-smi
```

#### 2. 负载均衡器502错误
```bash
# 检查后端实例
for port in {10095..10098}; do
    curl -I http://localhost:$port/health
done
```

#### 3. 性能问题
```bash
# 检查资源使用
docker stats
nvidia-smi
```

## ⚡ 性能优化

### 配置优化

```bash
# 增加工作进程数
WORKERS_PER_INSTANCE=8

# 增加GPU实例数
GPU_COUNT=8
```

### 系统优化

```bash
# 内核参数优化
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
sysctl -p
```

## 📝 与自定义方案对比

| 特性 | 官方部署 | 自定义server.py |
|------|----------|-----------------|
| **部署复杂度** | 简单 | 中等 |
| **性能** | 标准 | 优化 |
| **维护成本** | 低 | 中等 |
| **定制能力** | 有限 | 完全可控 |
| **稳定性** | 官方保证 | 自行维护 |
| **更新** | 官方更新 | 手动更新 |

## 📞 技术支持

- **FunASR官方**: https://github.com/modelscope/FunASR
- **官方文档**: https://github.com/modelscope/FunASR/tree/main/runtime
- **Docker Hub**: https://hub.docker.com/r/funasr/funasr

**文档版本**: v1.0.0  
**最后更新**: 2024年1月  
**适用版本**: FunASR Runtime v0.2.1+
