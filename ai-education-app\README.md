# AI Education App - 教育平台

## 📋 项目简介

基于AI服务平台的教育应用，提供智能出题、作业识别、语音解释等教育相关功能。

## 🚀 快速开始

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### Docker部署
```bash
# 构建镜像
docker build -t ai-education-app .

# 运行容器
docker run -p 8100:8100 \
  -e AI_GATEWAY_URL=http://ai-gateway:8000 \
  ai-education-app
```

## 📖 API文档

启动服务后访问：http://localhost:8100/docs

### 主要接口

#### 作业图片识别
```bash
POST /api/homework/ocr
Content-Type: multipart/form-data

file: <image_file>
```

#### 智能出题
```bash
POST /api/questions/generate
Content-Type: application/json

{
  "topic": "数学",
  "subject": "小学数学",
  "difficulty": "medium",
  "count": 5
}
```

#### 语音解释生成
```bash
POST /api/voice/generate
Content-Type: application/x-www-form-urlencoded

text=要生成语音的文本内容
```

#### AI助教对话
```bash
POST /api/chat
Content-Type: application/x-www-form-urlencoded

message=学生的问题
```

## ⚙️ 配置说明

### 环境变量
- `AI_GATEWAY_URL`: AI网关地址
- `DEBUG`: 调试模式
- `LOG_LEVEL`: 日志级别

## 🔧 功能特性

- ✅ 作业图片OCR识别
- ✅ 智能出题生成
- ✅ 语音解释合成
- ✅ AI助教对话
- ✅ 健康检查
- ✅ 错误处理

## 🧪 测试

```bash
# 健康检查
curl http://localhost:8100/health

# 测试智能出题
curl -X POST http://localhost:8100/api/questions/generate \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "数学",
    "subject": "小学数学",
    "difficulty": "medium",
    "count": 3
  }'

# 测试AI对话
curl -X POST http://localhost:8100/api/chat \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "message=什么是勾股定理？"
```

## 🎯 业务场景

### 智能出题
- 根据知识点自动生成题目
- 支持多种题型和难度
- 自动生成答案和解析

### 作业识别
- 识别手写作业内容
- 提取题目和答案
- 支持多种图片格式

### 语音解释
- 将文本转换为语音
- 支持多种语音类型
- 适合听力学习

### AI助教
- 智能回答学生问题
- 提供学习建议
- 个性化辅导

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request
