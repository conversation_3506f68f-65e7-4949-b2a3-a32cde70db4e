"""
学科老师管理模块
用于统一管理不同学科的老师类型、专业技能和选择逻辑
可在出题、判题、知识点抽取等多个场景复用
"""

from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass
from app.models.practise_analysis_model import DifficultyLevel


class TeacherType(Enum):
    """出题老师类型"""
    LANGUAGE_TEACHER = "language_teacher"  # 语文老师
    MATH_TEACHER = "math_teacher"  # 数学老师
    ENGLISH_TEACHER = "english_teacher"  # 英语老师
    SCIENCE_TEACHER = "science_teacher"  # 科学老师
    HISTORY_TEACHER = "history_teacher"  # 历史老师
    GEOGRAPHY_TEACHER = "geography_teacher"  # 地理老师
    GENERAL_TEACHER = "general_teacher"  # 通用老师


@dataclass
class TeacherProfile:
    """老师档案"""
    name: str  # 老师名称
    expertise: str  # 专业领域
    skills: List[str]  # 具体技能
    keywords: List[str]  # 关键词匹配
    content_keywords: List[str]  # 内容关键词匹配
    description: str  # 详细描述


class TeacherManager:
    """学科老师管理器"""
    
    def __init__(self):
        self._teacher_profiles = self._init_teacher_profiles()
    
    def _init_teacher_profiles(self) -> Dict[TeacherType, TeacherProfile]:
        """初始化老师档案"""
        return {
            TeacherType.LANGUAGE_TEACHER: TeacherProfile(
                name="语文老师",
                expertise="语文、文学、阅读理解、作文写作、诗词赏析、文言文解析",
                skills=[
                    "古诗词理解与赏析",
                    "现代文阅读理解",
                    "文言文翻译与解析", 
                    "作文指导与评价",
                    "语法知识讲解",
                    "字词句段篇章分析",
                    "文学常识梳理",
                    "修辞手法识别"
                ],
                keywords=["语文", "中文", "文学", "作文", "阅读", "汉语"],
                content_keywords=["诗词", "古诗", "文言文", "作文", "阅读理解", "散文", "小说", "议论文"],
                description="专业的语文教育专家，擅长古诗词赏析、现代文阅读、作文指导等语文核心技能"
            ),
            
            TeacherType.MATH_TEACHER: TeacherProfile(
                name="数学老师",
                expertise="数学、计算、几何、代数、概率统计",
                skills=[
                    "基础运算与计算",
                    "代数方程求解",
                    "几何图形分析",
                    "函数概念理解",
                    "概率统计计算",
                    "数学建模思维",
                    "逻辑推理能力",
                    "数学证明方法"
                ],
                keywords=["数学", "算术", "几何", "代数", "统计"],
                content_keywords=["计算", "方程", "几何", "函数", "概率", "图形", "数字", "运算"],
                description="专业的数学教育专家，擅长数学计算、逻辑推理、几何分析等数学核心技能"
            ),
            
            TeacherType.ENGLISH_TEACHER: TeacherProfile(
                name="英语老师", 
                expertise="英语、语法、词汇、听说读写",
                skills=[
                    "英语语法讲解",
                    "词汇记忆方法",
                    "阅读理解技巧",
                    "写作指导训练",
                    "听力训练方法",
                    "口语表达练习",
                    "翻译技能培养",
                    "英语文化背景"
                ],
                keywords=["英语", "english", "外语"],
                content_keywords=["单词", "语法", "grammar", "vocabulary", "reading", "writing"],
                description="专业的英语教育专家，擅长英语语法、词汇教学、听说读写全面技能培养"
            ),
            
            TeacherType.SCIENCE_TEACHER: TeacherProfile(
                name="科学老师",
                expertise="科学、物理、化学、生物、实验",
                skills=[
                    "物理现象解释",
                    "化学反应分析", 
                    "生物结构认知",
                    "实验设计指导",
                    "科学方法培养",
                    "观察记录训练",
                    "假设验证思维",
                    "科学素养提升"
                ],
                keywords=["科学", "物理", "化学", "生物", "实验"],
                content_keywords=["实验", "元素", "细胞", "力学", "反应", "观察", "现象"],
                description="专业的科学教育专家，擅长物理化学生物实验，培养学生科学思维和实践能力"
            ),
            
            TeacherType.HISTORY_TEACHER: TeacherProfile(
                name="历史老师",
                expertise="历史、史学、历史事件、文化传承",
                skills=[
                    "历史事件梳理",
                    "时间线构建",
                    "历史人物分析",
                    "文化背景解读",
                    "史料分析方法",
                    "历史思维培养",
                    "古今对比思考",
                    "文明发展认知"
                ],
                keywords=["历史", "史学", "文史"],
                content_keywords=["朝代", "历史事件", "古代", "近代", "文化", "传统"],
                description="专业的历史教育专家，擅长历史事件分析、文化传承教育、史学思维培养"
            ),
            
            TeacherType.GEOGRAPHY_TEACHER: TeacherProfile(
                name="地理老师",
                expertise="地理、地球科学、地形气候、人文地理",
                skills=[
                    "地形地貌认知",
                    "气候特征分析",
                    "地图读图技能",
                    "区域地理理解",
                    "人文地理认知",
                    "环境保护意识",
                    "空间思维培养",
                    "地理实践能力"
                ],
                keywords=["地理", "地球科学", "地质"],
                content_keywords=["地形", "气候", "国家", "城市", "地图", "区域"],
                description="专业的地理教育专家，擅长地形气候分析、地图技能培养、人文地理教育"
            ),
            
            TeacherType.GENERAL_TEACHER: TeacherProfile(
                name="通用老师",
                expertise="综合学科知识",
                skills=[
                    "跨学科整合",
                    "综合分析能力",
                    "通用学习方法",
                    "知识迁移能力",
                    "批判性思维",
                    "创新思维培养",
                    "学习策略指导",
                    "综合素养提升"
                ],
                keywords=["通用", "综合", "其他"],
                content_keywords=["综合", "通用", "跨学科"],
                description="综合学科教育专家，擅长跨学科知识整合、通用学习方法指导"
            )
        }
    
    def select_teacher_by_subject(self, subject: str, content: str = "") -> TeacherType:
        """根据学科和内容选择合适的老师"""
        subject_lower = subject.lower()
        content_lower = content.lower()
        
        # 遍历所有老师档案，找到最匹配的
        for teacher_type, profile in self._teacher_profiles.items():
            # 检查学科关键词匹配
            if any(keyword in subject_lower for keyword in profile.keywords):
                return teacher_type
            
            # 检查内容关键词匹配
            if content and any(keyword in content_lower for keyword in profile.content_keywords):
                return teacher_type
        
        # 默认返回通用老师
        return TeacherType.GENERAL_TEACHER
    
    def get_teacher_profile(self, teacher_type: TeacherType) -> TeacherProfile:
        """获取老师档案"""
        return self._teacher_profiles.get(teacher_type, self._teacher_profiles[TeacherType.GENERAL_TEACHER])
    
    def get_teacher_name(self, teacher_type: TeacherType) -> str:
        """获取老师名称"""
        return self.get_teacher_profile(teacher_type).name
    
    def get_teacher_expertise(self, teacher_type: TeacherType) -> str:
        """获取老师专业领域"""
        return self.get_teacher_profile(teacher_type).expertise
    
    def get_teacher_skills(self, teacher_type: TeacherType) -> List[str]:
        """获取老师技能列表"""
        return self.get_teacher_profile(teacher_type).skills
    
    def get_teacher_description(self, teacher_type: TeacherType) -> str:
        """获取老师详细描述"""
        return self.get_teacher_profile(teacher_type).description
    
    def get_all_teachers(self) -> Dict[TeacherType, TeacherProfile]:
        """获取所有老师档案"""
        return self._teacher_profiles.copy()
    
    def get_teachers_by_skill(self, skill_keyword: str) -> List[TeacherType]:
        """根据技能关键词查找老师"""
        matching_teachers = []
        for teacher_type, profile in self._teacher_profiles.items():
            if any(skill_keyword.lower() in skill.lower() for skill in profile.skills):
                matching_teachers.append(teacher_type)
        return matching_teachers

    def get_difficulty_guidance(self, teacher_type: TeacherType, difficulty: DifficultyLevel) -> str:
        """根据老师类型和难度获取出题指导"""
        profile = self.get_teacher_profile(teacher_type)

        difficulty_guidance = {
            DifficultyLevel.easy: {
                "description": "简单题目",
                "requirements": [
                    "基础概念理解",
                    "直接应用基本知识点",
                    "单一知识点考查",
                    "题目表述清晰简单"
                ]
            },
            DifficultyLevel.medium: {
                "description": "中等题目",
                "requirements": [
                    "知识点综合运用",
                    "需要一定的分析思考",
                    "多个概念的关联",
                    "适度的推理过程"
                ]
            },
            DifficultyLevel.hard: {
                "description": "困难题目",
                "requirements": [
                    "深度理解和灵活运用",
                    "复杂的逻辑推理",
                    "跨知识点综合分析",
                    "创新性思维要求"
                ]
            },
            DifficultyLevel.extreme: {
                "description": "极难题目",
                "requirements": [
                    "专业水平的深度思考",
                    "复杂的综合分析能力",
                    "创造性解决问题",
                    "高阶思维技能运用"
                ]
            }
        }

        guidance = difficulty_guidance.get(difficulty, difficulty_guidance[DifficultyLevel.medium])

        return f"""
                作为{profile.name}，针对{guidance['description']}的要求：

                难度特点：
                {chr(10).join(f"- {req}" for req in guidance['requirements'])}

                专业建议：
                - 运用你在{profile.expertise}方面的专业知识
                - 结合你的核心技能：{', '.join(profile.skills[:3])}
                - 确保题目难度符合{difficulty.value}水平
                - 题目设计要体现{profile.name}的专业特色
                """

    def get_difficulty_examples(self, teacher_type: TeacherType, difficulty: DifficultyLevel) -> Dict[str, str]:
        """根据老师类型和难度获取题目示例"""
        profile = self.get_teacher_profile(teacher_type)

        # 根据不同老师和难度提供示例
        examples = {
            (TeacherType.LANGUAGE_TEACHER, DifficultyLevel.easy): {
                "example": "《咏鹅》的作者是谁？",
                "explanation": "直接考查基础文学常识"
            },
            (TeacherType.LANGUAGE_TEACHER, DifficultyLevel.medium): {
                "example": "分析《咏鹅》中'白毛浮绿水'的修辞手法及其表达效果",
                "explanation": "需要理解修辞手法并分析表达效果"
            },
            (TeacherType.LANGUAGE_TEACHER, DifficultyLevel.hard): {
                "example": "比较《咏鹅》与其他描写动物的古诗在表现手法上的异同",
                "explanation": "需要跨作品比较分析，体现深度思考"
            },
            (TeacherType.MATH_TEACHER, DifficultyLevel.easy): {
                "example": "计算 2 + 3 = ?",
                "explanation": "基础加法运算"
            },
            (TeacherType.MATH_TEACHER, DifficultyLevel.medium): {
                "example": "小明有苹果x个，小红比小明多3个，两人共有15个苹果，求x的值",
                "explanation": "需要建立方程并求解"
            },
            (TeacherType.MATH_TEACHER, DifficultyLevel.hard): {
                "example": "已知函数f(x)=ax²+bx+c，若f(1)=0，f(2)=3，f(3)=8，求f(x)的解析式",
                "explanation": "需要运用函数知识和方程组求解"
            }
        }

        key = (teacher_type, difficulty)
        if key in examples:
            return examples[key]
        else:
            return {
                "example": f"根据{difficulty.value}难度要求设计的{profile.name}专业题目",
                "explanation": f"体现{profile.name}的专业特色和{difficulty.value}难度要求"
            }


# 全局单例实例
teacher_manager = TeacherManager()
