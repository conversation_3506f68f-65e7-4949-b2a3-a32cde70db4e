from fastapi import HTTPException, UploadFile
import sys, os
import httpx

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.interface_forward_service import InterfaceForwardService
from app.llm.vllm import VLLMClient
from app.services.abs_service import AbsService
from app.core.config import settings
import app.utils.prompts as prompts
import json
import base64


class TranslateService(AbsService):
    def __init__(self):
        super().__init__()
        self.client = VLLMClient(settings.VLLM_ENDPOINT)
        self.interface_forward_service = InterfaceForwardService()


    async def _translate_text_to_text(self, language_pair: str, text_list: list, llm_params: dict = {}) -> dict:
        if language_pair == "zh_to_en":
            prompt = prompts.get_translate_zh_to_en_prompt(text_list)
        elif language_pair == "en_to_zh":
            prompt = prompts.get_translate_en_to_zh_prompt(text_list)
        else:
            raise HTTPException(status_code=400, detail="不支持的翻译方向")
        
        print('prompt:', prompt)

        if 'temperature' not in llm_params.keys():
            llm_params['temperature'] = 0.01
        if 'top_p' not in llm_params.keys():
            llm_params['top_p'] = 0.001
        if 'top_k' not in llm_params.keys():
            llm_params['top_k'] = 1
        
        response_dict = await self.client.generate(prompt, system_role='专业翻译工程师和语言处理专家')  
        return self._parse_response(response_dict)   



    async def translate_text_to_text(self, language_pair: str, text_list: list, llm_params: dict = {}) -> dict:
        try:
            response_dict = await self._translate_text_to_text(language_pair, text_list, llm_params)  
            return self.wrap_response(response_dict)
        except TimeoutError as e:
            raise HTTPException(status_code=504, detail="模型响应超时")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"翻译服务服务内部错误, {e}")
        
    async def translate_audio_to_text(self, language_pair: str, file: UploadFile) -> dict:
        async with httpx.AsyncClient() as client:
            # 调用语音识别接口，该接口接收文件
            response = await client.post(
                url="https://t-matrix.nibs.ac.cn/mko/llm/asr/recognition",
                files={"file": file.file}
            )
        
        response_dict = response.json()
        text = response_dict["data"].get("text", "")
        if text == "":
            raise HTTPException(status_code=500, detail="语音识别失败")
        translate_text = await self._translate_text_to_text(language_pair, [text])
        translate_text = translate_text["translate_list"][0]
        return self.wrap_response({"translate_text": translate_text})
    
    async def translate_text_to_audio(self, language_pair: str, text: str, speaker: str) -> bytes:
        reponse = await self._translate_text_to_text(language_pair, [text])
        translate_text = reponse["translate_list"][0]
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url="https://t-matrix.nibs.ac.cn/mko/llm/tts/text_to_speech",
                json={
                    "text": translate_text,
                    "speaker": speaker,
                    "stream": False
                }
            )
        
        # 直接返回二进制音频数据
        mp3_file = response.content
        if not mp3_file:
            raise HTTPException(status_code=500, detail="语音合成失败")
        
        return mp3_file
    
    async def translate_audio_to_audio(self, language_pair: str, file: UploadFile, speaker: str) -> bytes:
        async with httpx.AsyncClient() as client:
            # 调用语音识别接口，该接口接收文件
            response = await client.post(
                url="https://t-matrix.nibs.ac.cn/mko/llm/asr/recognition",
                files={"file": file.file}
            )
        
        response_dict = response.json()
        text = response_dict["data"].get("text", "")
        if text == "":
            raise HTTPException(status_code=500, detail="语音识别失败")
        
        return await self.translate_text_to_audio(language_pair, text, speaker)  # 添加 await

    

    def _parse_response(self, response_dict) -> dict:
        try:
            model_response = self.safe_json_parse(response_dict["choices"][0]["message"]["content"])
        except Exception as e:
            self.logger.error(f"JSON解析错误: {e}")
            raise HTTPException(status_code=500, detail="Internal Server Error, safe_json_parse error")


        return model_response
