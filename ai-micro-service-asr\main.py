"""
ASR微服务 - 语音识别服务
支持多种音频格式和语音识别模型
"""

import asyncio
import time
import uuid
import os
import base64
import tempfile
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
import aioredis
import aiofiles


# 请求响应模型
class ASRRequest(BaseModel):
    """ASR请求模型"""
    audio_url: Optional[str] = Field(None, description="音频文件URL")
    audio_data: Optional[str] = Field(None, description="音频文件base64编码")
    language: str = Field(default="zh", description="识别语言")
    model: str = Field(default="whisper", description="ASR模型")
    format: str = Field(default="wav", description="音频格式")
    sample_rate: Optional[int] = Field(default=16000, description="采样率")
    channels: Optional[int] = Field(default=1, description="声道数")


class ASRResponse(BaseModel):
    """ASR响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Dict[str, Any] = Field(..., description="返回数据")
    metadata: Dict[str, Any] = Field(..., description="元数据")


# 创建应用
app = FastAPI(
    title="AI ASR Service",
    description="语音识别微服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
redis_client = None
http_client = httpx.AsyncClient(timeout=120)  # ASR可能需要更长时间

# ASR后端配置
ASR_BACKENDS = [
    {
        "name": "fun_asr",
        "base_url": "http://**************:10099",
        "api_key": "sk-vAc55vL1pr9VJT2jv1JE5ANzdlexoAjd",
        "weight": 1,
        "status": "active",
        "endpoint": "/llm/asr/recognition"
    }
    # 可以添加更多后端
]

# 支持的音频格式
SUPPORTED_FORMATS = ["wav", "mp3"]
SUPPORTED_LANGUAGES = ["zh", "en", "auto"]


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global redis_client
    
    print("🚀 ASR服务启动中...")
    
    # 初始化Redis
    try:
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_client = aioredis.from_url(redis_url)
        await redis_client.ping()
        print("✅ Redis连接成功")
    except:
        print("⚠️ Redis连接失败，缓存功能不可用")
        redis_client = None
    
    # 创建临时目录
    os.makedirs("/tmp/asr", exist_ok=True)
    
    print("✅ ASR服务启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    await http_client.aclose()
    if redis_client:
        await redis_client.close()
    print("✅ ASR服务关闭完成")


def select_backend() -> Dict[str, Any]:
    """选择可用的后端"""
    active_backends = [b for b in ASR_BACKENDS if b["status"] == "active"]
    if not active_backends:
        raise HTTPException(status_code=503, detail="No ASR backend available")
    
    # 简单选择第一个可用后端
    return active_backends[0]


async def get_cache(key: str) -> Optional[Any]:
    """获取缓存"""
    if not redis_client:
        return None
    
    try:
        data = await redis_client.get(key)
        if data:
            import json
            return json.loads(data)
    except:
        pass
    return None


async def set_cache(key: str, data: Any, ttl: int = 3600):
    """设置缓存"""
    if not redis_client:
        return
    
    try:
        import json
        await redis_client.setex(key, ttl, json.dumps(data))
    except:
        pass


async def download_audio_file(url: str) -> str:
    """下载音频文件"""
    try:
        response = await http_client.get(url)
        response.raise_for_status()
        
        # 创建临时文件
        temp_file = f"/tmp/asr/{uuid.uuid4()}.audio"
        async with aiofiles.open(temp_file, 'wb') as f:
            await f.write(response.content)
        
        return temp_file
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to download audio: {str(e)}")


async def save_audio_data(audio_data: str, format: str = "wav") -> str:
    """保存base64音频数据到临时文件"""
    try:
        # 解码base64数据
        audio_bytes = base64.b64decode(audio_data)
        
        # 创建临时文件
        temp_file = f"/tmp/asr/{uuid.uuid4()}.{format}"
        async with aiofiles.open(temp_file, 'wb') as f:
            await f.write(audio_bytes)
        
        return temp_file
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to save audio data: {str(e)}")


async def recognize_audio_backend(
    audio_file_path: str,
    language: str = "zh",
    model: str = "whisper"
) -> Dict[str, Any]:
    """调用后端ASR服务"""
    backend = select_backend()
    
    try:
        # 读取音频文件
        async with aiofiles.open(audio_file_path, 'rb') as f:
            audio_content = await f.read()
        
        # 构建multipart/form-data请求
        files = {
            'file': ('audio.wav', audio_content, 'audio/wav')
        }
        
        headers = {
            "X-API-Key": backend["api_key"]
        }
        
        # 调用后端API
        response = await http_client.post(
            f"{backend['base_url']}{backend['endpoint']}",
            files=files,
            headers=headers
        )
        response.raise_for_status()
        
        result = response.json()
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"ASR服务调用失败: {str(e)}")
    finally:
        # 清理临时文件
        try:
            os.remove(audio_file_path)
        except:
            pass


@app.get("/llm/asr/health")
async def health_check():
    """健康检查"""
    backend_status = []
    for backend in ASR_BACKENDS:
        try:
            # 简单的连通性测试
            response = await http_client.get(
                f"{backend['base_url']}/health",
                timeout=5
            )
            status = "healthy" if response.status_code == 200 else "unhealthy"
        except:
            status = "unhealthy"
        
        backend_status.append({
            "name": backend["name"],
            "status": status,
            "url": backend["base_url"]
        })
    
    # 获取节点信息
    node_id = os.getenv("NODE_ID", "unknown")
    node_role = os.getenv("NODE_ROLE", "worker")
    workers = os.getenv("WORKERS", "1")
    
    return {
        "status": "healthy",
        "service": "ai-micro-service-asr",
        "version": "1.0.0",
        "node_id": node_id,
        "node_role": node_role,
        "workers": int(workers),
        "timestamp": time.time(),
        "backends": backend_status,
        "supported_formats": SUPPORTED_FORMATS,
        "supported_languages": SUPPORTED_LANGUAGES,
        "redis_connected": redis_client is not None,
        "process_id": os.getpid()
    }


@app.post("/llm/asr/recognition", response_model=ASRResponse)
async def speech_recognition(
    request: ASRRequest,
    background_tasks: BackgroundTasks
):
    """语音识别"""
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # 验证输入
        if not request.audio_url and not request.audio_data:
            raise HTTPException(status_code=400, detail="Missing audio_url or audio_data")
        
        if request.language not in SUPPORTED_LANGUAGES:
            raise HTTPException(status_code=400, detail=f"Unsupported language: {request.language}")
        
        # 生成缓存键
        cache_key = f"asr:{hash(str(request.dict()))}"
        
        # 检查缓存
        cached_result = await get_cache(cache_key)
        if cached_result:
            return ASRResponse(
                success=True,
                data=cached_result,
                metadata={
                    "request_id": request_id,
                    "processing_time": time.time() - start_time,
                    "cache_hit": True,
                    "language": request.language,
                    "model": request.model
                }
            )
        
        # 准备音频文件
        if request.audio_url:
            audio_file_path = await download_audio_file(request.audio_url)
        else:
            audio_file_path = await save_audio_data(request.audio_data, request.format)
        
        # 调用后端服务
        result = await recognize_audio_backend(
            audio_file_path=audio_file_path,
            language=request.language,
            model=request.model
        )
        
        # 异步缓存结果
        background_tasks.add_task(
            set_cache,
            cache_key,
            result,
            3600  # 缓存1小时
        )
        
        processing_time = time.time() - start_time
        
        return ASRResponse(
            success=True,
            data=result,
            metadata={
                "request_id": request_id,
                "processing_time": processing_time,
                "cache_hit": False,
                "language": request.language,
                "model": request.model,
                "format": request.format
            }
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "request_id": request_id,
                "processing_time": processing_time
            }
        )


@app.post("/llm/asr/process")
async def process_asr(request: dict):
    """统一处理接口（兼容网关调用）"""
    try:
        # 解析请求
        audio_url = request.get("audio_url")
        audio_data = request.get("audio_data")
        language = request.get("language", "zh")
        model = request.get("model", "whisper")
        options = request.get("options", {})
        
        if not audio_url and not audio_data:
            raise HTTPException(status_code=400, detail="Missing audio_url or audio_data")
        
        # 构建ASR请求
        asr_request = ASRRequest(
            audio_url=audio_url,
            audio_data=audio_data,
            language=language,
            model=model,
            format=options.get("format", "wav"),
            sample_rate=options.get("sample_rate", 16000),
            channels=options.get("channels", 1)
        )
        
        # 调用ASR服务
        result = await speech_recognition(asr_request, BackgroundTasks())
        
        return {
            "success": result.success,
            "data": result.data,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/llm/asr/stats")
async def get_service_stats():
    """获取服务统计"""
    return {
        "service": "ai-micro-service-asr",
        "version": "1.0.0",
        "uptime": time.time() - getattr(app.state, 'start_time', time.time()),
        "backends": len(ASR_BACKENDS),
        "supported_formats": SUPPORTED_FORMATS,
        "supported_languages": SUPPORTED_LANGUAGES,
        "features": [
            "speech_recognition",
            "file_upload",
            "multiple_formats",
            "caching_support"
        ]
    }


# 记录启动时间
@app.middleware("http")
async def add_startup_time(request, call_next):
    if not hasattr(app.state, 'start_time'):
        app.state.start_time = time.time()
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=50100,
        reload=True
    )
