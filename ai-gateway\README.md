# AI Gateway - AI服务网关

## 📋 项目简介

AI服务网关，提供统一的AI服务接入点，支持负载均衡、缓存、多租户等企业级特性。

## 🚀 快速开始

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### Docker部署
```bash
# 构建镜像
docker build -t ai-gateway .

# 运行容器
docker run -p 8000:8000 \
  -e REDIS_URL=redis://localhost:6379 \
  ai-gateway
```

## 📖 API文档

启动服务后访问：http://localhost:8000/docs

### 主要接口

#### OCR服务
```bash
POST /api/v1/ocr
Content-Type: application/json
X-Tenant-ID: your-tenant

{
  "image_url": "https://example.com/image.jpg"
}
```

#### TTS服务
```bash
POST /api/v1/tts
Content-Type: application/json
X-Tenant-ID: your-tenant

{
  "text": "Hello World",
  "voice": "zh-CN-XiaoxiaoNeural"
}
```

#### ASR服务
```bash
POST /api/v1/asr
Content-Type: application/json
X-Tenant-ID: your-tenant

{
  "audio_url": "https://example.com/audio.wav"
}
```

#### LLM服务
```bash
POST /api/v1/llm
Content-Type: application/json
X-Tenant-ID: your-tenant

{
  "messages": [{"role": "user", "content": "你好"}]
}
```

## ⚙️ 配置说明

### 环境变量
- `REDIS_URL`: Redis连接地址
- `DEBUG`: 调试模式
- `LOG_LEVEL`: 日志级别

### 后端服务配置
在 `main.py` 中修改 `BACKEND_SERVICES` 配置：

```python
BACKEND_SERVICES = {
    "ocr": ["http://your-ocr-server:port"],
    "tts": ["http://tts-service:8002"],
    "asr": ["http://your-asr-server:port"],
    "llm": ["http://your-llm-server:port"]
}
```

## 🔧 功能特性

- ✅ 统一API入口
- ✅ 负载均衡
- ✅ Redis缓存
- ✅ 多租户支持
- ✅ CORS支持
- ✅ 健康检查
- ✅ 错误处理

## 📊 监控

- 健康检查：`GET /health`
- 服务状态：`GET /api/v1/services`

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request
