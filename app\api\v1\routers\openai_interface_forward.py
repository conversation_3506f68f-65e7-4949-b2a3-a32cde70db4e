from fastapi import APIRouter, Request
from app.services.openai_interface_forward_service import OpenaiInterfaceForwardService

router = APIRouter()
service = OpenaiInterfaceForwardService()


# 注册通配路由（自动处理所有请求）
@router.api_route("/llm/openai/{full_path:path}", methods=["GET", "POST", "PUT"])
async def handle_all_routes(request: Request):
    """动态路由入口"""
    return await service.route_request(request)
