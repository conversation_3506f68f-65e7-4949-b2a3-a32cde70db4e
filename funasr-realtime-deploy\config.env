# FunASR实时语音转写部署配置文件

# 基础配置
FUNASR_IMAGE=registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
COMPOSE_PROJECT_NAME=funasr-realtime

# 服务端口配置
NGINX_PORT=8080
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# GPU实例配置 (可以根据需要增减)
# 格式: 容器名称:GPU_ID:HTTP端口:WSS端口:内存限制:CPU限制
GPU_INSTANCES="
funasr-gpu-0:2:50300:50310:16G:8.0
funasr-gpu-1:7:50301:50311:16G:8.0
"

# 如果要部署4个实例，取消下面的注释
# GPU_INSTANCES="
# funasr-gpu-0:0:10095:10195:16G:8.0
# funasr-gpu-1:1:10096:10196:16G:8.0
# funasr-gpu-2:2:10097:10197:16G:8.0
# funasr-gpu-3:3:10098:10198:16G:8.0
# "

# 如果要部署8个实例，可以这样配置
# GPU_INSTANCES="
# funasr-node-1:0:10095:10195:16G:8.0
# funasr-node-2:1:10096:10196:16G:8.0
# funasr-node-3:2:10097:10197:16G:8.0
# funasr-node-4:3:10098:10198:16G:8.0
# funasr-node-5:4:10099:10199:16G:8.0
# funasr-node-6:5:10100:10200:16G:8.0
# funasr-node-7:6:10101:10201:16G:8.0
# funasr-node-8:7:10102:10202:16G:8.0
# "

# 性能配置
WORKERS_PER_INSTANCE=4        # 每个实例的工作进程数
MAX_REQUESTS=1000            # 每个进程处理的最大请求数
TIMEOUT=300                  # 请求超时时间(秒)

# WebSocket配置
WSS_CHUNK_SIZE="[5, 10, 5]"  # WebSocket音频块大小
WSS_CHUNK_INTERVAL=10        # WebSocket音频块间隔(ms)
WSS_MAX_CONNECTIONS=50       # 每个WebSocket实例最大连接数

# 资源限制
MEMORY_LIMIT=16G             # 每个实例的内存限制
CPU_LIMIT=8.0               # 每个实例的CPU限制

# 负载均衡配置
LB_ALGORITHM=round_robin     # 负载均衡算法: round_robin, least_conn, hash
LB_WEIGHT=1                 # 每个实例的权重
LB_MAX_FAILS=3              # 最大失败次数
LB_FAIL_TIMEOUT=30s         # 失败超时时间

# 监控配置
ENABLE_PROMETHEUS=true       # 是否启用Prometheus监控
ENABLE_GRAFANA=true         # 是否启用Grafana监控

# 日志配置
LOG_LEVEL=info              # 日志级别: debug, info, warning, error
LOG_RETENTION_DAYS=7        # 日志保留天数

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s   # 健康检查间隔
HEALTH_CHECK_TIMEOUT=10s    # 健康检查超时
HEALTH_CHECK_RETRIES=3      # 健康检查重试次数
HEALTH_CHECK_START_PERIOD=120s  # 启动等待时间

# SSL配置 (可选)
ENABLE_SSL=false            # 是否启用SSL
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
