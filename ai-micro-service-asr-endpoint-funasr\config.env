# FunASR GPU集群配置文件
# 修改此文件来自定义部署参数

# 基础配置
FUNASR_IMAGE=registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
NGINX_PORT=8080
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# GPU实例配置 (可以根据需要增减)
# 格式: 容器名称:GPU_ID:端口:内存限制:CPU限制:模型池大小
GPU_INSTANCES="
funasr-gpu-0:4:50250:16G:8.0:2
"

# 如果要部署4个实例，取消下面的注释
# GPU_INSTANCES="
# funasr-gpu-0:0:10312:16G:8.0:2
# funasr-gpu-1:1:10313:16G:8.0:2
# funasr-gpu-2:2:10314:16G:8.0:2
# funasr-gpu-3:3:10315:16G:8.0:2
# "

# 如果要部署8个实例，可以这样配置
# GPU_INSTANCES="
# funasr-node-1:0:10312:16G:8.0:2
# funasr-node-2:1:10313:16G:8.0:2
# funasr-node-3:2:10314:16G:8.0:2
# funasr-node-4:3:10315:16G:8.0:2
# funasr-node-5:4:10316:16G:8.0:2
# funasr-node-6:5:10317:16G:8.0:2
# funasr-node-7:6:10318:16G:8.0:2
# funasr-node-8:7:10319:16G:8.0:2
# "

# 负载均衡配置
LB_ALGORITHM=hash  # 可选: round_robin, least_conn, hash
LB_WEIGHT=4        # 每个实例的权重
LB_MAX_FAILS=3     # 最大失败次数
LB_FAIL_TIMEOUT=30s # 失败超时时间

# 性能配置
WORKERS=4
WORKER_CONNECTIONS=1000
MAX_MEMORY_GB=8.0
