from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    PROJECT_NAME: str = "Matrix API"
    API_V1_STR: str = ""
    DEBUG: bool = True
    OLLAMA_ENDPOINT: str = "http://**************:10099"
    MODEL_NAME: str = "qwen2.5-coder:32b"
    API_KEY: str = "wTgwoSp0syaL9xvRzvGYGIrQLwnbIWJs"

    VLLM_ENDPOINT: str = "http://**************:10099"

    # OLLAMA_ENDPOINT: str = "http://localhost:11434"
    # MODEL_NAME: str = "deepseek-r1:latest"


    # API Key验证配置
    ENABLE_API_KEY_AUTH: bool = False  # 默认关闭，兼容现有客户端

    api_keys: list[str] = [
        "sk-vAc55vL1pr9VJT2jv1JE5ANzdlexoAjd",
        "sk-UpHBCiYZPStECVQ7E91y1EQCiQhkXWFO"
    ]


    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
