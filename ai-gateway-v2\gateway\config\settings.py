"""
配置管理
支持环境变量和配置文件
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # 服务配置
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    WORKERS: int = Field(default=4, env="WORKERS")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/ai_gateway",
        env="DATABASE_URL"
    )
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_MAX_CONNECTIONS: int = Field(default=100, env="REDIS_MAX_CONNECTIONS")
    REDIS_RETRY_ON_TIMEOUT: bool = Field(default=True, env="REDIS_RETRY_ON_TIMEOUT")
    
    # 缓存配置
    CACHE_DEFAULT_TTL: int = Field(default=3600, env="CACHE_DEFAULT_TTL")  # 1小时
    CACHE_MAX_SIZE: int = Field(default=10000, env="CACHE_MAX_SIZE")
    CACHE_ENABLED: bool = Field(default=True, env="CACHE_ENABLED")
    
    # 服务发现配置
    CONSUL_HOST: str = Field(default="localhost", env="CONSUL_HOST")
    CONSUL_PORT: int = Field(default=8500, env="CONSUL_PORT")
    SERVICE_DISCOVERY_ENABLED: bool = Field(default=True, env="SERVICE_DISCOVERY_ENABLED")
    
    # 监控配置
    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    JAEGER_ENABLED: bool = Field(default=True, env="JAEGER_ENABLED")
    JAEGER_AGENT_HOST: str = Field(default="localhost", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    
    # 限流配置
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS: int = Field(default=1000, env="RATE_LIMIT_REQUESTS")  # 每分钟请求数
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # 时间窗口(秒)
    
    # 熔断器配置
    CIRCUIT_BREAKER_ENABLED: bool = Field(default=True, env="CIRCUIT_BREAKER_ENABLED")
    CIRCUIT_BREAKER_FAILURE_THRESHOLD: int = Field(default=5, env="CIRCUIT_BREAKER_FAILURE_THRESHOLD")
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT: int = Field(default=60, env="CIRCUIT_BREAKER_RECOVERY_TIMEOUT")
    CIRCUIT_BREAKER_EXPECTED_EXCEPTION: str = Field(default="Exception", env="CIRCUIT_BREAKER_EXPECTED_EXCEPTION")
    
    # 认证配置
    AUTH_ENABLED: bool = Field(default=False, env="AUTH_ENABLED")
    JWT_SECRET_KEY: str = Field(default="your-secret-key", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_EXPIRE_MINUTES: int = Field(default=30, env="JWT_EXPIRE_MINUTES")
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = Field(
        default=["*"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_METHODS: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE"],
        env="ALLOWED_METHODS"
    )
    ALLOWED_HEADERS: List[str] = Field(
        default=["*"],
        env="ALLOWED_HEADERS"
    )
    
    # 服务配置
    SERVICES: dict = Field(default={
        "ocr": {
            "url": "http://localhost:8001",
            "timeout": 30,
            "retry_attempts": 3,
            "circuit_breaker": True
        },
        "tts": {
            "url": "http://localhost:8002",
            "timeout": 60,
            "retry_attempts": 3,
            "circuit_breaker": True
        },
        "asr": {
            "url": "http://localhost:8003",
            "timeout": 60,
            "retry_attempts": 3,
            "circuit_breaker": True
        },
        "llm": {
            "url": "http://localhost:8004",
            "timeout": 120,
            "retry_attempts": 2,
            "circuit_breaker": True
        }
    })
    
    # 后端服务配置
    BACKEND_SERVICES: dict = Field(default={
        "ocr_backends": [
            {"url": "http://***************:20060", "weight": 1, "status": "active"},
            {"url": "http://**************:10099", "weight": 2, "status": "active"}
        ],
        "tts_backends": [
            {"url": "http://***************:20060", "weight": 1, "status": "active"}
        ],
        "asr_backends": [
            {"url": "http://**************:10099", "weight": 1, "status": "active"}
        ],
        "llm_backends": [
            {"url": "http://***************:20060", "weight": 1, "status": "active"},
            {"url": "http://**************:10099", "weight": 2, "status": "active"}
        ]
    })
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(default=10000, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT: int = Field(default=30, env="REQUEST_TIMEOUT")
    KEEP_ALIVE_TIMEOUT: int = Field(default=5, env="KEEP_ALIVE_TIMEOUT")
    
    # 日志配置
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # 安全配置
    SECURITY_HEADERS_ENABLED: bool = Field(default=True, env="SECURITY_HEADERS_ENABLED")
    TRUSTED_HOSTS: List[str] = Field(default=["*"], env="TRUSTED_HOSTS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


# 环境特定配置
class DevelopmentSettings(Settings):
    """开发环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    CACHE_ENABLED: bool = False
    AUTH_ENABLED: bool = False


class ProductionSettings(Settings):
    """生产环境配置"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    WORKERS: int = 8
    CACHE_ENABLED: bool = True
    AUTH_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 10000
    MAX_CONCURRENT_REQUESTS: int = 100000


class TestingSettings(Settings):
    """测试环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    DATABASE_URL: str = "postgresql://postgres:password@localhost:5432/ai_gateway_test"
    REDIS_URL: str = "redis://localhost:6379/1"
    CACHE_ENABLED: bool = False
    AUTH_ENABLED: bool = False


def get_settings() -> Settings:
    """根据环境获取配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# 导出配置
settings = get_settings()
