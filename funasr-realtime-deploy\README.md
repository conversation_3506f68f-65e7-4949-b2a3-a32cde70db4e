# FunASR实时语音转写部署方案

## 📋 概述

基于FunASR官方GPU镜像的完整实时语音转写解决方案，同时支持HTTP文件上传和WebSocket实时流式识别。

## 🎯 方案特点

- **双模式支持**: HTTP文件识别 + WebSocket实时转写
- **官方镜像**: 100%使用FunASR官方GPU镜像
- **多GPU负载均衡**: 支持多GPU实例分布式部署
- **完整代理**: Nginx同时代理HTTP和WebSocket
- **客户端示例**: 提供多种语言的WebSocket客户端
- **监控完整**: Prometheus + Grafana监控

## 🏗️ 架构图

```
🌐 Nginx负载均衡器 :8080
    ├── HTTP API (文件识别)
    │   ├── FunASR-HTTP-GPU-0 :10095 (/fileASR)
    │   ├── FunASR-HTTP-GPU-1 :10096 (/fileASR)
    │   ├── FunASR-HTTP-GPU-2 :10097 (/fileASR)
    │   └── FunASR-HTTP-GPU-3 :10098 (/fileASR)
    └── WebSocket API (实时转写)
        ├── FunASR-WSS-GPU-0 :10195 (wss://)
        ├── FunASR-WSS-GPU-1 :10196 (wss://)
        ├── FunASR-WSS-GPU-2 :10197 (wss://)
        └── FunASR-WSS-GPU-3 :10198 (wss://)

📊 监控系统
    ├── Prometheus :9090
    └── Grafana :3000
```

## 🖥️ 系统要求

### 硬件要求
- **CPU**: 16核心+
- **内存**: 64GB+
- **GPU**: 4张 × 8GB VRAM+
- **存储**: 500GB SSD+

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **NVIDIA Driver**: 470+
- **NVIDIA Docker**: 2.0+

## 📁 项目文件

```
funasr-realtime-deploy/
├── 📝 源文件
│   ├── config.env                   # 部署配置文件 ⭐
│   ├── generate-config.py           # 动态配置生成器 ⭐
│   ├── deploy.sh                    # 部署脚本 ⭐
│   ├── test.sh                      # 测试脚本
│   └── .gitignore                   # Git忽略文件
├── 👥 客户端示例
│   ├── python_client.py            # Python客户端
│   ├── javascript_client.html      # JavaScript客户端
│   └── test_audio.py               # 音频测试工具
├── 📖 文档
│   └── README.md                    # 本文档
└── 🔄 动态生成 (运行时创建)
    ├── docker-compose.generated.yml # Docker配置
    ├── nginx.generated.conf         # Nginx配置
    └── prometheus.generated.yml     # Prometheus配置
```

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装NVIDIA Docker支持
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

### 2. 配置部署参数

编辑 `config.env` 文件，自定义您的部署：

```bash
# 部署2个GPU实例的示例
GPU_INSTANCES="
funasr-gpu-0:0:10095:10195:16G:8.0
funasr-gpu-1:1:10096:10196:16G:8.0
"

# 部署4个GPU实例的示例
# GPU_INSTANCES="
# funasr-gpu-0:0:10095:10195:16G:8.0
# funasr-gpu-1:1:10096:10196:16G:8.0
# funasr-gpu-2:2:10097:10197:16G:8.0
# funasr-gpu-3:3:10098:10198:16G:8.0
# "

# 自定义配置示例
# GPU_INSTANCES="
# funasr-node-1:0:10095:10195:32G:16.0
# funasr-node-2:1:10096:10196:16G:8.0
# funasr-node-3:2:10097:10197:8G:4.0
# "
```

**配置格式**: `容器名称:GPU_ID:HTTP端口:WebSocket端口:内存限制:CPU限制`

### 3. 一键部署

```bash
# 进入项目目录
cd funasr-realtime-deploy

# 给脚本执行权限
chmod +x deploy.sh test.sh

# 一键部署（自动生成配置并启动）
./deploy.sh deploy
```

部署过程会自动：
1. 🔧 根据 `config.env` 生成 `docker-compose.generated.yml`
2. 🌐 生成对应的 `nginx.generated.conf` 负载均衡配置
3. 📊 生成 `prometheus.generated.yml` 监控配置
4. 🚀 启动所有HTTP和WebSocket服务并验证健康状态

### 4. 验证部署

```bash
# 检查服务状态
./deploy.sh check

# 运行完整测试
./test.sh full

# 测试WebSocket连接
./test.sh websocket

# 查看生成的配置文件
ls *.generated.*
```

## ⚙️ API接口

### HTTP文件识别接口

#### 官方fileASR接口
```bash
curl -X POST \
  -F "file=@audio.wav" \
  http://localhost:8080/fileASR
```

#### 兼容接口
```bash
curl -X POST \
  -F "file=@audio.wav" \
  http://localhost:8080/llm/asr/recognition
```

### WebSocket实时转写接口

#### 连接地址
```
ws://localhost:8080/ws/asr
wss://localhost:8080/wss/asr  (如果配置了SSL)
```

#### 消息格式

**发送音频数据**:
```json
{
  "mode": "2pass",
  "chunk_size": [5, 10, 5],
  "chunk_interval": 10,
  "wav_name": "microphone",
  "is_speaking": true,
  "wav_format": "PCM"
}
```

**接收识别结果**:
```json
{
  "mode": "2pass",
  "text": "识别的文本内容",
  "is_final": false,
  "timestamp": "2024-01-01 12:00:00"
}
```

## 🧪 客户端示例

### Python WebSocket客户端

```python
import asyncio
import websockets
import json
import pyaudio

async def realtime_asr():
    uri = "ws://localhost:8080/ws/asr"
    
    async with websockets.connect(uri) as websocket:
        # 发送配置
        config = {
            "mode": "2pass",
            "chunk_size": [5, 10, 5],
            "chunk_interval": 10,
            "wav_name": "microphone"
        }
        await websocket.send(json.dumps(config))
        
        # 发送音频数据
        # ... 音频采集和发送逻辑
        
        # 接收识别结果
        async for message in websocket:
            result = json.loads(message)
            print(f"识别结果: {result['text']}")

# 运行客户端
asyncio.run(realtime_asr())
```

### JavaScript WebSocket客户端

```javascript
const ws = new WebSocket('ws://localhost:8080/ws/asr');

ws.onopen = function() {
    // 发送配置
    const config = {
        mode: "2pass",
        chunk_size: [5, 10, 5],
        chunk_interval: 10,
        wav_name: "microphone"
    };
    ws.send(JSON.stringify(config));
};

ws.onmessage = function(event) {
    const result = JSON.parse(event.data);
    console.log('识别结果:', result.text);
};

// 发送音频数据
function sendAudioData(audioBuffer) {
    ws.send(audioBuffer);
}
```

## 📈 性能指标

### 预期性能

| 服务类型 | 单实例QPS | 4实例集群QPS | 延迟 |
|----------|-----------|--------------|------|
| **HTTP文件识别** | 1,000 | 4,000 | 1-3秒 |
| **WebSocket实时** | 50并发 | 200并发 | <500ms |

### 监控访问

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

## 🔧 服务管理

### 基本操作

```bash
# 启动服务
./deploy.sh deploy

# 检查状态
./deploy.sh check

# 查看日志
docker-compose logs -f

# 重启服务
./deploy.sh restart

# 停止服务
./deploy.sh stop

# 清理环境
./deploy.sh clean
```

### 扩缩容

```bash
# 修改config.env中的GPU_COUNT
vim config.env

# 重新部署
./deploy.sh deploy
```

## 🔍 故障排查

### 常见问题

#### 1. WebSocket连接失败
```bash
# 检查WebSocket服务
for port in {10195..10198}; do
    curl -I http://localhost:$port/
done

# 检查Nginx WebSocket代理
curl -H "Upgrade: websocket" -H "Connection: Upgrade" http://localhost:8080/ws/asr
```

#### 2. 实时转写延迟高
```bash
# 检查GPU使用率
nvidia-smi

# 检查网络延迟
ping localhost

# 调整chunk_interval参数
```

## 📝 与其他方案对比

| 特性 | 实时转写方案 | 官方部署 | 自定义server.py |
|------|-------------|----------|-----------------|
| **HTTP文件识别** | ✅ | ✅ | ✅ |
| **WebSocket实时** | ✅ | ❌ | ❌ |
| **部署复杂度** | 中等 | 简单 | 中等 |
| **功能完整性** | 最完整 | 基础 | HTTP优化 |
| **适用场景** | 全场景 | 文件处理 | 高并发文件 |

## 📞 技术支持

- **FunASR官方**: https://github.com/modelscope/FunASR
- **WebSocket文档**: https://github.com/modelscope/FunASR/tree/main/runtime/python/websocket
- **客户端示例**: https://github.com/modelscope/FunASR/tree/main/runtime/python/websocket

**文档版本**: v1.0.0  
**最后更新**: 2024年1月  
**适用版本**: FunASR Runtime v0.2.1+
