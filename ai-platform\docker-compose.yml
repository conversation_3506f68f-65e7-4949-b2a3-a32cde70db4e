version: '3.8'

services:
  # AI服务网关
  gateway:
    build: 
      context: ./gateway
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    depends_on:
      - redis
    volumes:
      - ./gateway:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - ai-platform
    restart: unless-stopped

  # TTS微服务
  tts-service:
    build:
      context: ./services/tts-service
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./services/tts-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8002 --reload
    networks:
      - ai-platform
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - ai-platform
    restart: unless-stopped

  # 教育平台
  education-app:
    build:
      context: ./business-apps/education-app
      dockerfile: Dockerfile
    ports:
      - "8100:8100"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
    depends_on:
      - gateway
    volumes:
      - ./business-apps/education-app:/app
      - ./sdk:/app/sdk
    command: uvicorn main:app --host 0.0.0.0 --port 8100 --reload
    networks:
      - ai-platform
    restart: unless-stopped

  # 医疗平台
  medical-app:
    build:
      context: ./business-apps/medical-app
      dockerfile: Dockerfile
    ports:
      - "8200:8200"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
    depends_on:
      - gateway
    volumes:
      - ./business-apps/medical-app:/app
      - ./sdk:/app/sdk
    command: uvicorn main:app --host 0.0.0.0 --port 8200 --reload
    networks:
      - ai-platform
    restart: unless-stopped

volumes:
  redis_data:

networks:
  ai-platform:
    driver: bridge
