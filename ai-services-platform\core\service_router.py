"""
服务路由器
智能路由AI请求到最优的服务实例
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

import httpx
import aioredis
from pydantic import BaseModel

from config.settings import settings
from core.tenant_manager import TenantConfig


class LoadBalanceStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED = "weighted"
    LEAST_CONNECTIONS = "least_connections"
    RESPONSE_TIME = "response_time"
    BUSINESS_OPTIMIZED = "business_optimized"


@dataclass
class ServiceInstance:
    """服务实例"""
    service_type: str
    instance_id: str
    url: str
    weight: int = 1
    status: str = "active"  # active, inactive, maintenance
    current_connections: int = 0
    avg_response_time: float = 0.0
    last_health_check: float = 0.0
    failure_count: int = 0
    success_count: int = 0
    
    @property
    def health_score(self) -> float:
        """健康评分 0-1"""
        if self.status != "active":
            return 0.0
        
        # 基于响应时间和成功率计算
        total_requests = self.success_count + self.failure_count
        if total_requests == 0:
            return 0.5
        
        success_rate = self.success_count / total_requests
        time_score = max(0, 1 - (self.avg_response_time / 5000))  # 5秒为基准
        
        return (success_rate * 0.7 + time_score * 0.3)


class ServiceRouter:
    """服务路由器"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        self.services: Dict[str, List[ServiceInstance]] = {}
        self.round_robin_counters: Dict[str, int] = {}
        self.client = httpx.AsyncClient(timeout=30)
        
    async def initialize(self):
        """初始化"""
        self.redis = aioredis.from_url(settings.REDIS_URL)
        
        # 加载服务配置
        await self._load_service_instances()
        
        # 启动健康检查
        asyncio.create_task(self._health_check_loop())
        
        print("✅ 服务路由器初始化完成")
    
    async def _load_service_instances(self):
        """加载服务实例配置"""
        # 从配置文件加载服务实例
        service_configs = {
            "ocr": [
                {"url": "http://localhost:8001", "weight": 1},
                {"url": "http://***************:20060", "weight": 2},
                {"url": "http://**************:10099", "weight": 1}
            ],
            "tts": [
                {"url": "http://localhost:8002", "weight": 1},
                {"url": "http://***************:20060", "weight": 1}
            ],
            "asr": [
                {"url": "http://localhost:8003", "weight": 1},
                {"url": "http://**************:10099", "weight": 1}
            ],
            "llm": [
                {"url": "http://localhost:8004", "weight": 1},
                {"url": "http://***************:20060", "weight": 1},
                {"url": "http://**************:10099", "weight": 2}
            ],
            "translate": [
                {"url": "http://localhost:8005", "weight": 1}
            ]
        }
        
        for service_type, instances in service_configs.items():
            self.services[service_type] = []
            for i, config in enumerate(instances):
                instance = ServiceInstance(
                    service_type=service_type,
                    instance_id=f"{service_type}_{i}",
                    url=config["url"],
                    weight=config["weight"]
                )
                self.services[service_type].append(instance)
        
        print(f"加载了 {sum(len(instances) for instances in self.services.values())} 个服务实例")
    
    async def route_request(
        self, 
        request: Any, 
        tenant_config: TenantConfig
    ) -> Optional[str]:
        """路由请求到最优服务实例"""
        service_type = request.service_type
        
        if service_type not in self.services:
            return None
        
        # 获取可用实例
        available_instances = [
            instance for instance in self.services[service_type]
            if instance.status == "active" and instance.health_score > 0.3
        ]
        
        if not available_instances:
            return None
        
        # 根据业务上下文选择路由策略
        strategy = self._select_strategy(request, tenant_config)
        
        # 执行路由选择
        selected_instance = await self._select_instance(
            available_instances, 
            strategy, 
            request, 
            tenant_config
        )
        
        if selected_instance:
            # 更新连接数
            selected_instance.current_connections += 1
            return selected_instance.url
        
        return None
    
    def _select_strategy(self, request: Any, tenant_config: TenantConfig) -> LoadBalanceStrategy:
        """选择负载均衡策略"""
        # 高优先级租户使用响应时间优化
        if tenant_config.priority_level >= 8:
            return LoadBalanceStrategy.RESPONSE_TIME
        
        # 根据业务上下文选择
        business_domain = request.business_context.get("domain")
        if business_domain == "medical":
            # 医疗场景优先响应时间
            return LoadBalanceStrategy.RESPONSE_TIME
        elif business_domain == "education":
            # 教育场景平衡负载
            return LoadBalanceStrategy.WEIGHTED
        
        # 默认轮询
        return LoadBalanceStrategy.ROUND_ROBIN
    
    async def _select_instance(
        self,
        instances: List[ServiceInstance],
        strategy: LoadBalanceStrategy,
        request: Any,
        tenant_config: TenantConfig
    ) -> Optional[ServiceInstance]:
        """根据策略选择实例"""
        
        if strategy == LoadBalanceStrategy.ROUND_ROBIN:
            return self._round_robin_select(instances, request.service_type)
        
        elif strategy == LoadBalanceStrategy.WEIGHTED:
            return self._weighted_select(instances)
        
        elif strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(instances)
        
        elif strategy == LoadBalanceStrategy.RESPONSE_TIME:
            return self._response_time_select(instances)
        
        elif strategy == LoadBalanceStrategy.BUSINESS_OPTIMIZED:
            return await self._business_optimized_select(instances, request, tenant_config)
        
        # 默认返回第一个
        return instances[0] if instances else None
    
    def _round_robin_select(self, instances: List[ServiceInstance], service_type: str) -> ServiceInstance:
        """轮询选择"""
        if service_type not in self.round_robin_counters:
            self.round_robin_counters[service_type] = 0
        
        instance = instances[self.round_robin_counters[service_type] % len(instances)]
        self.round_robin_counters[service_type] += 1
        return instance
    
    def _weighted_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """权重选择"""
        total_weight = sum(instance.weight for instance in instances)
        random_weight = random.randint(1, total_weight)
        
        current_weight = 0
        for instance in instances:
            current_weight += instance.weight
            if random_weight <= current_weight:
                return instance
        
        return instances[0]
    
    def _least_connections_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """最少连接选择"""
        return min(instances, key=lambda x: x.current_connections)
    
    def _response_time_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """响应时间优化选择"""
        # 综合考虑响应时间和健康评分
        def score_func(instance):
            return instance.health_score / (instance.avg_response_time + 1)
        
        return max(instances, key=score_func)
    
    async def _business_optimized_select(
        self, 
        instances: List[ServiceInstance], 
        request: Any, 
        tenant_config: TenantConfig
    ) -> ServiceInstance:
        """业务优化选择"""
        # 根据业务特性选择最优实例
        business_domain = request.business_context.get("domain")
        
        if business_domain == "medical":
            # 医疗场景：优先选择延迟最低的实例
            return min(instances, key=lambda x: x.avg_response_time)
        
        elif business_domain == "education":
            # 教育场景：平衡负载和成本
            return self._weighted_select(instances)
        
        # 默认选择健康评分最高的
        return max(instances, key=lambda x: x.health_score)
    
    async def call_service(
        self, 
        service_url: str, 
        request: Any, 
        tenant_id: str, 
        request_id: str
    ) -> Dict[str, Any]:
        """调用具体服务"""
        start_time = time.time()
        
        # 构建请求
        payload = {
            "model": request.model,
            "input_data": request.input_data,
            "options": request.options,
            "business_context": request.business_context,
            "tenant_id": tenant_id,
            "request_id": request_id
        }
        
        try:
            # 发送请求
            response = await self.client.post(
                f"{service_url}/process",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            processing_time = time.time() - start_time
            
            # 更新实例统计
            await self._update_instance_stats(service_url, processing_time, True)
            
            return result.get("data", {})
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # 更新实例统计
            await self._update_instance_stats(service_url, processing_time, False)
            
            raise e
        finally:
            # 减少连接数
            await self._decrease_connections(service_url)
    
    async def _update_instance_stats(self, service_url: str, processing_time: float, success: bool):
        """更新实例统计"""
        for instances in self.services.values():
            for instance in instances:
                if instance.url == service_url:
                    # 更新响应时间（移动平均）
                    if instance.avg_response_time == 0:
                        instance.avg_response_time = processing_time
                    else:
                        instance.avg_response_time = (
                            instance.avg_response_time * 0.8 + processing_time * 0.2
                        )
                    
                    # 更新成功/失败计数
                    if success:
                        instance.success_count += 1
                        instance.failure_count = max(0, instance.failure_count - 1)
                    else:
                        instance.failure_count += 1
                    
                    break
    
    async def _decrease_connections(self, service_url: str):
        """减少连接数"""
        for instances in self.services.values():
            for instance in instances:
                if instance.url == service_url:
                    instance.current_connections = max(0, instance.current_connections - 1)
                    break
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await self._health_check_all_services()
                await asyncio.sleep(30)  # 30秒检查一次
            except Exception as e:
                print(f"健康检查失败: {e}")
                await asyncio.sleep(10)
    
    async def _health_check_all_services(self):
        """检查所有服务健康状态"""
        tasks = []
        for instances in self.services.values():
            for instance in instances:
                tasks.append(self._health_check_instance(instance))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _health_check_instance(self, instance: ServiceInstance):
        """检查单个实例健康状态"""
        try:
            start_time = time.time()
            response = await self.client.get(f"{instance.url}/health", timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                instance.status = "active"
                instance.last_health_check = time.time()
                
                # 更新响应时间
                if instance.avg_response_time == 0:
                    instance.avg_response_time = response_time
                else:
                    instance.avg_response_time = (
                        instance.avg_response_time * 0.9 + response_time * 0.1
                    )
            else:
                instance.status = "inactive"
                instance.failure_count += 1
                
        except Exception:
            instance.status = "inactive"
            instance.failure_count += 1
    
    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {}
        for service_type, instances in self.services.items():
            active_count = sum(1 for i in instances if i.status == "active")
            total_count = len(instances)
            avg_response_time = sum(i.avg_response_time for i in instances) / total_count if total_count > 0 else 0
            
            status[service_type] = {
                "active_instances": active_count,
                "total_instances": total_count,
                "avg_response_time": avg_response_time,
                "health_rate": active_count / total_count if total_count > 0 else 0
            }
        
        return status
    
    async def get_service_info(self, service_type: str) -> Dict[str, Any]:
        """获取服务信息"""
        if service_type not in self.services:
            return {}
        
        instances = self.services[service_type]
        active_instances = [i for i in instances if i.status == "active"]
        
        return {
            "service_type": service_type,
            "total_instances": len(instances),
            "active_instances": len(active_instances),
            "avg_response_time": sum(i.avg_response_time for i in active_instances) / len(active_instances) if active_instances else 0,
            "supported_models": await self._get_supported_models(service_type)
        }
    
    async def _get_supported_models(self, service_type: str) -> List[str]:
        """获取支持的模型列表"""
        # 这里可以从服务实例查询支持的模型
        model_mapping = {
            "ocr": ["paddle_ocr", "tesseract", "easyocr"],
            "tts": ["edge_tts", "cosyvoice"],
            "asr": ["fun_asr", "whisper"],
            "llm": ["qwen3-8b", "qwen3-32b", "gpt-3.5-turbo"],
            "translate": ["baidu", "google", "youdao"]
        }
        return model_mapping.get(service_type, [])
