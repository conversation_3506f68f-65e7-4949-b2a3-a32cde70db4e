import asyncio
import random
import time
from typing import List, Dict, Optional
from dataclasses import dataclass, field
import httpx
from app.services.abs_service import AbsService

@dataclass
class ServerInfo:
    url: str
    weight: int
    status: str
    current_connections: int = 0
    response_time: float = 0.0
    last_check: float = field(default_factory=time.time)
    failure_count: int = 0

class LoadBalancerService(AbsService):
    def __init__(self):
        super().__init__()
        self.server_stats = {}
        self.round_robin_counters = {}
        self.circuit_breakers = {}
        
    def get_server_for_model(self, model_name: str, config: Dict) -> Optional[str]:
        """根据负载均衡策略选择服务器"""
        model_config = config.get("model_server_dict", {}).get(model_name)
        if not model_config:
            return None
            
        # 过滤可用服务器
        servers = []
        for server in model_config.get("servers", []):
            if server["status"] == "active" and not self._is_circuit_open(server["url"]):
                servers.append(server)
        
        if not servers:
            return None
            
        strategy = model_config.get("load_balance_strategy", "round_robin")
        
        if strategy == "round_robin":
            return self._round_robin_select(model_name, servers)
        elif strategy == "weighted":
            return self._weighted_select(servers)
        elif strategy == "least_connections":
            return self._least_connections_select(servers)
        else:
            return servers[0]["url"]
    
    def _round_robin_select(self, model_name: str, servers: List[Dict]) -> str:
        if model_name not in self.round_robin_counters:
            self.round_robin_counters[model_name] = 0
        
        server = servers[self.round_robin_counters[model_name] % len(servers)]
        self.round_robin_counters[model_name] += 1
        return server["url"]
    
    def _weighted_select(self, servers: List[Dict]) -> str:
        total_weight = sum(s["weight"] for s in servers)
        random_weight = random.randint(1, total_weight)
        
        current_weight = 0
        for server in servers:
            current_weight += server["weight"]
            if random_weight <= current_weight:
                return server["url"]
        
        return servers[0]["url"]
    
    def _least_connections_select(self, servers: List[Dict]) -> str:
        min_connections = float('inf')
        selected_server = servers[0]
        
        for server in servers:
            connections = self.server_stats.get(server["url"], {}).get("current_connections", 0)
            if connections < min_connections:
                min_connections = connections
                selected_server = server
        
        return selected_server["url"]
    
    #服务正常 → CLOSED状态
    #↓
    #连续5次失败 → OPEN状态 (拒绝所有请求)
    #↓
    #等待60秒 → HALF_OPEN状态 (允许测试请求)
    #↓
    #测试成功 → CLOSED状态 (恢复正常)
    #测试失败 → OPEN状态 (继续熔断)
    def _is_circuit_open(self, server_url: str) -> bool:
        """检查熔断器状态"""
        breaker = self.circuit_breakers.get(server_url, {})
        if breaker.get("state") == "open":
            if time.time() - breaker.get("last_failure", 0) > 60:  # 60秒后尝试半开
                self.circuit_breakers[server_url]["state"] = "half_open"
                return False
            return True
        return False
    
    def record_success(self, server_url: str):
        """记录成功请求"""
        if server_url in self.circuit_breakers:
            self.circuit_breakers[server_url] = {"state": "closed", "failure_count": 0}
    
    def record_failure(self, server_url: str):
        """记录失败请求"""
        if server_url not in self.circuit_breakers:
            self.circuit_breakers[server_url] = {"state": "closed", "failure_count": 0}
        
        self.circuit_breakers[server_url]["failure_count"] += 1
        if self.circuit_breakers[server_url]["failure_count"] >= 5:  # 失败阈值
            self.circuit_breakers[server_url]["state"] = "open"
            self.circuit_breakers[server_url]["last_failure"] = time.time()
    
    async def health_check_servers(self, config: Dict):
        """定期健康检查"""
        print("开始健康检查")
        for route_config in config.values():
            model_server_dict = route_config.get("model_server_dict", {})
            for model_name, model_config in model_server_dict.items():
                servers = model_config.get("servers", [])
                for server in servers:
                    await self._check_server_health(server)
        print("健康检查完成")
        print(f"健康检查结果: {self.circuit_breakers}")
    
    async def _check_server_health(self, server: Dict):
        """检查单个服务器健康状态"""
        try:
            async with httpx.AsyncClient(timeout=5) as client:
                response = await client.get(f"{server['url']}/health")
                if response.status_code == 200:
                    server["status"] = "active"
                    self.record_success(server["url"])
                else:
                    server["status"] = "inactive"
                    self.record_failure(server["url"])
        except Exception:
            server["status"] = "inactive"
            self.record_failure(server["url"])
