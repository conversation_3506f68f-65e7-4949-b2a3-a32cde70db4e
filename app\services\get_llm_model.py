from fastapi import HTTPException

from app.llm.vllm import <PERSON><PERSON><PERSON><PERSON>
from app.services.abs_service import AbsService
import core.forward_route_config as forward_route_config


class GetLLMModelService(AbsService):
    def __init__(self):
        super().__init__()

    async def get_llm_model(self) -> dict:
        # 读取forward_route_config中的配置
        #
        result_dict = {}
        result_dict['model_dict'] = forward_route_config.ROUTE_MAPPING['/llm/openai']['model_description_dict']
        result_dict['business_default_dict'] = forward_route_config.ROUTE_MAPPING['/llm/openai']['default_business_model_dict']
        return result_dict

        
