from pydantic import BaseModel, Field

class GeneratePotentialLineRequest(BaseModel):
    text: str = Field(..., min_length=1, example=["这是文本"])
    llm_params: dict = Field(
        {},
        example={
            "model": "Qwen2-7B-Instruct",
            "stream": False,
            "max_tokens": 1000,
            "temperature": 0.0,
            "top_p": 1.0,
        },
        description="llm模型参数"
    )


# 潜力线响应模型
class GeneratePotentialLineResponse(BaseModel):
    commercial_potential_line_list: list=Field([], example="")
    research_potential_line_list:list=Field([], example="")
    education_potential_line_list: list=Field([], example="")
    commercial_analysis: str=Field("", example="")
    research_analysis: str=Field("", example="")
    education_analysis: str=Field("", example="")


