#!/bin/bash

# ASR微服务Gunicorn启动脚本
# 支持多进程部署，提高并发处理能力

set -e

# 环境变量配置
WORKERS=${WORKERS:-3}                    # 工作进程数，默认3个
WORKER_CLASS=${WORKER_CLASS:-uvicorn.workers.UvicornWorker}  # 工作进程类型
WORKER_CONNECTIONS=${WORKER_CONNECTIONS:-800}  # 每个进程的连接数
MAX_REQUESTS=${MAX_REQUESTS:-800}        # 每个进程处理的最大请求数
MAX_REQUESTS_JITTER=${MAX_REQUESTS_JITTER:-80}  # 请求数抖动
TIMEOUT=${TIMEOUT:-600}                  # 请求超时时间 (ASR需要更长时间)
KEEPALIVE=${KEEPALIVE:-5}               # Keep-alive时间
BIND=${BIND:-0.0.0.0:50100}              # 绑定地址
LOG_LEVEL=${LOG_LEVEL:-info}            # 日志级别
PRELOAD=${PRELOAD:-true}                # 预加载应用

# 根据CPU核心数自动调整工作进程数
if [ "$WORKERS" = "auto" ]; then
    WORKERS=$(python3 -c "import multiprocessing; print(multiprocessing.cpu_count() * 2 + 1)")
    echo "自动设置工作进程数: $WORKERS"
fi

# 创建日志目录
mkdir -p /app/logs

echo "🚀 启动ASR微服务 (Gunicorn多进程模式)"
echo "配置信息:"
echo "  工作进程数: $WORKERS"
echo "  工作进程类型: $WORKER_CLASS"
echo "  每进程连接数: $WORKER_CONNECTIONS"
echo "  绑定地址: $BIND"
echo "  日志级别: $LOG_LEVEL"
echo "  预加载应用: $PRELOAD"
echo "  超时时间: $TIMEOUT"

# 启动Gunicorn
exec gunicorn main:app \
    --workers $WORKERS \
    --worker-class $WORKER_CLASS \
    --worker-connections $WORKER_CONNECTIONS \
    --max-requests $MAX_REQUESTS \
    --max-requests-jitter $MAX_REQUESTS_JITTER \
    --timeout $TIMEOUT \
    --keep-alive $KEEPALIVE \
    --bind $BIND \
    --log-level $LOG_LEVEL \
    --access-logfile /app/logs/access.log \
    --error-logfile /app/logs/error.log \
    --capture-output \
    --enable-stdio-inheritance \
    $([ "$PRELOAD" = "true" ] && echo "--preload") \
    --worker-tmp-dir /dev/shm
