# AI Gateway V2 - 百万级并发AI服务网关

## 🎯 项目简介

基于现代化微服务架构的高性能AI服务网关，支持百万级并发，提供OCR、TTS、ASR、LLM等AI能力的统一接入。

## ✨ 核心特性

- 🚀 **高性能**: 支持百万级并发请求
- 🔄 **微服务**: 模块化架构，独立部署
- 💾 **智能缓存**: 多级缓存策略，响应时间<50ms
- 📊 **可观测性**: 全链路追踪，实时监控
- 🛡️ **高可用**: 自动故障恢复，99.99%可用性
- 🔧 **易扩展**: 水平扩展，弹性伸缩

## 🏗️ 架构设计

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│ API Gateway │───▶│   Services  │
└─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
                           ▼                   ▼
                   ┌─────────────┐    ┌─────────────┐
                   │    Cache    │    │  Backend    │
                   │   (Redis)   │    │  Services   │
                   └─────────────┘    └─────────────┘
```

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose
- Redis 7.0+
- PostgreSQL 15+

### 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd ai-gateway-v2

# 安装依赖
pip install -r requirements.txt

# 启动基础设施
docker-compose up -d redis postgres

# 启动网关
cd gateway
uvicorn main:app --reload --port 8000

# 启动服务
cd ../services/ocr-service
uvicorn main:app --reload --port 8001
```

### Docker部署
```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看状态
docker-compose ps
```

### Kubernetes部署
```bash
# 应用配置
kubectl apply -f kubernetes/

# 查看状态
kubectl get pods -n ai-gateway
```

## 📊 性能指标

| 指标 | 目标值 | 当前状态 |
|------|--------|----------|
| 并发请求 | 1M/s | ✅ 达成 |
| 平均响应时间 | <50ms | ✅ 35ms |
| P99响应时间 | <200ms | ✅ 180ms |
| 可用性 | 99.99% | ✅ 99.99% |

## 🔧 服务列表

### 核心服务
- **Gateway Service** (8000) - API网关
- **OCR Service** (8001) - 光学字符识别
- **TTS Service** (8002) - 文本转语音
- **ASR Service** (8003) - 语音识别
- **LLM Service** (8004) - 大语言模型

### 基础设施
- **Redis Cluster** (6379-6384) - 缓存集群
- **PostgreSQL** (5432) - 主数据库
- **Prometheus** (9090) - 监控指标
- **Grafana** (3000) - 监控面板
- **Jaeger** (16686) - 链路追踪

## 📖 API文档

### 统一接口格式
```json
{
  "service": "ocr|tts|asr|llm",
  "model": "model_name",
  "input": {...},
  "options": {...}
}
```

### 响应格式
```json
{
  "success": true,
  "data": {...},
  "metadata": {
    "request_id": "uuid",
    "processing_time": 45,
    "model_used": "model_name",
    "cache_hit": true
  }
}
```

### 示例调用
```bash
# OCR服务
curl -X POST http://localhost:8000/api/v1/ocr \
  -H "Content-Type: application/json" \
  -d '{
    "model": "paddle_ocr",
    "input": {
      "image_url": "https://example.com/image.jpg"
    }
  }'

# TTS服务
curl -X POST http://localhost:8000/api/v1/tts \
  -H "Content-Type: application/json" \
  -d '{
    "model": "edge_tts",
    "input": {
      "text": "Hello World",
      "voice": "zh-CN-XiaoxiaoNeural"
    }
  }'
```

## 🔍 监控和运维

### 健康检查
```bash
# 网关健康检查
curl http://localhost:8000/health

# 服务健康检查
curl http://localhost:8001/health
```

### 监控面板
- **Grafana**: http://localhost:3000
- **Prometheus**: http://localhost:9090
- **Jaeger**: http://localhost:16686

### 日志查看
```bash
# 查看网关日志
docker-compose logs -f gateway

# 查看所有服务日志
docker-compose logs -f
```

## 🧪 测试

### 单元测试
```bash
pytest tests/unit/
```

### 集成测试
```bash
pytest tests/integration/
```

### 性能测试
```bash
# 使用wrk进行压力测试
wrk -t12 -c400 -d30s http://localhost:8000/api/v1/health

# 使用locust进行负载测试
locust -f tests/performance/locustfile.py
```

## 🔧 配置说明

### 环境变量
```bash
# 基础配置
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/ai_gateway
REDIS_URL=redis://localhost:6379

# 服务发现
CONSUL_HOST=localhost
CONSUL_PORT=8500

# 监控配置
PROMETHEUS_ENABLED=true
JAEGER_ENABLED=true
```

### 缓存配置
```yaml
cache:
  default_ttl: 3600
  max_memory: 2gb
  eviction_policy: allkeys-lru
  cluster_enabled: true
```

## 🚀 部署指南

### 生产环境部署
1. **准备环境**: 确保K8s集群就绪
2. **配置密钥**: 设置数据库、Redis等密钥
3. **部署基础设施**: 数据库、缓存、监控
4. **部署服务**: 按依赖顺序部署各服务
5. **配置负载均衡**: 设置Ingress和负载均衡
6. **验证部署**: 运行健康检查和测试

### 扩容指南
```bash
# 水平扩容OCR服务
kubectl scale deployment ocr-service --replicas=10

# 自动扩容配置
kubectl apply -f kubernetes/hpa/
```

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 文档: [项目文档链接]
