from fastapi import HTTPException
from app.llm.vllm import V<PERSON><PERSON><PERSON>
from app.services.abs_service import AbsService
from app.core.config import settings
import app.utils.prompts as prompts
from app.models.practise_analysis_model import (  # 前一步设计的模型类
    AnalysisResponse, PractiseType, AbilityType,
)


class PractiseAnalysisService(AbsService):
    def __init__(self):
        super().__init__()
        self.client = VLLMClient(settings.VLLM_ENDPOINT)

    async def analyze_practise(self, content: str, subject: str, llm_params: dict = {}) -> AnalysisResponse:
        """
        试题分析核心方法
        :param llm_params:
        :param content: 题目完整内容
        :param subject: 学科分类
        :return: 结构化分析结果
        """
        # 预处理验证
        if len(content) < 10:
            raise HTTPException(status_code=400, detail="试题内容过短（需≥10字符）")

        # 构造动态提示词
        prompt = self._build_prompt(content, subject)
        print('prompt:', prompt)

        
        if 'temperature' not in llm_params.keys():
            llm_params['temperature'] = 0.01
        if 'top_p' not in llm_params.keys():
            llm_params['top_p'] = 0.001
        if 'top_k' not in llm_params.keys():
            llm_params['top_k'] = 1

        try:
            response_dict = await self.client.generate(prompt, llm_params)
            self.logger.info(f"模型响应: {response_dict}")
            return self._parse_response(response_dict)
        except TimeoutError as e:
            raise HTTPException(status_code=504, detail="模型响应超时")
        except Exception as e:
            self.logger.error(f"500错误: {e}")
            raise HTTPException(status_code=500, detail=f"试题分析服务内部错误, {e}")

    def _build_prompt(self, content: str, subject) -> str:
        return prompts.get_practise_analysis_prompt(
            question=content,
            subject=subject,
        )

    def _parse_response(self, response_dict: dict) -> dict:
        try:
            model_response = self.safe_json_parse(response_dict["choices"][0]["message"]["content"])
        except Exception as e:
            self.logger.error(f"JSON解析错误: {e}")
            raise HTTPException(status_code=500, detail=f"Internal Server Error, safe_json_parse error, {response_dict}")
        # 验证返回字段
        required_fields = ['difficulty', 'type', 'ability_list', 'knowledge_point', 'solution_idea', 'reference_answer']
        for field in required_fields:
            if field not in model_response:
                raise HTTPException(
                    status_code=500,
                    detail=f"模型响应缺少必要字段: {field}"
                )
        model_response.update({'model': response_dict['model']})
        return model_response
