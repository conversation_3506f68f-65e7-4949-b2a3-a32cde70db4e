from fastapi import HTTPException
import sys, os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.llm.vllm import VLLMClient
from app.services.abs_service import AbsService
from app.core.config import settings
import app.utils.prompts as prompts


class GeneratePotentialLineService(AbsService):
    def __init__(self):
        super().__init__()
        self.client = VLLMClient(settings.VLLM_ENDPOINT)

    async def generate_potential_line(self, text: str, llm_params: dict = {}) -> dict:
        prompt = prompts.get_potential_line(text)
        # 构造动态提示词
        print('prompt:', prompt)

        if 'chat_template_kwargs' not in llm_params.keys():
            llm_params['chat_template_kwargs'] = {
                "enable_thinking": False,
                "enable_search": True,  # 禁用搜索功能
            }
        if 'temperature' not in llm_params.keys():
            llm_params['temperature'] = 0.01
        if 'top_p' not in llm_params.keys():
            llm_params['top_p'] = 0.001
        if 'top_k' not in llm_params.keys():
            llm_params['top_k'] = 1


        try:
            response_dict = await self.client.generate(prompt, llm_params, system_role='你是一个人工智能数据分析师、预测专家和多语言信息处理专家')
            self.logger.info(f"模型响应: {response_dict}")
            return self._parse_response(response_dict)
        except TimeoutError as e:
            raise HTTPException(status_code=504, detail="模型响应超时")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"经验抽取服务内部错误, {e}")

    def _parse_response(self, response_dict) -> dict:
        try:
            model_response = self.safe_json_parse(response_dict["choices"][0]["message"]["content"])
            print('model_response:', model_response)
        except Exception as e:
            self.logger.error(f"JSON解析错误: {e}")
            raise HTTPException(status_code=500, detail="Internal Server Error, safe_json_parse error")

        # # 验证返回字段
        # required_fields = ["result"]
        # for field in required_fields:
        #     if field not in model_response:
        #         raise HTTPException(
        #             status_code=500,
        #             detail=f"模型响应缺少必要字段: {field}"
        #         )

        return model_response