from fastapi import FastAPI
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from app.core.config import settings
from fastapi.middleware.cors import CORSMiddleware
from app.middleware.auth_middleware import APIKeyAuthMiddleware
from app.api.v1.routers import (
    health,
    practise_score, 
    practise_analysis, 
    ocr_interface_forward, 
    tts_interface_forward, 
    note_summarize, 
    openai_interface_forward, 
    get_llm_model,
    asr_interface_forward,
    entity_extract_experience,
    generate_potential_line,
    face_recognition_interface_forward,
    multimodal_image_interface_forward,
    ocr_interface_forward_new,
    practise_generate,
    translate
)
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from app.services.load_balancer_service import LoadBalancerService
import app.core.forward_route_config as route_config
from contextlib import asynccontextmanager

# 创建调度器和负载均衡器
# scheduler = AsyncIOScheduler()
# load_balancer = LoadBalancerService()

# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     # 启动时执行
#     scheduler.add_job(
#         load_balancer.health_check_servers,
#         "interval",
#         seconds=30,
#         args=[route_config.ROUTE_MAPPING],
#         id="health_check"
#     )
#     scheduler.start()
#     print("🚀 健康检查任务已启动")
    
#     yield
    
#     # 关闭时执行
#     scheduler.shutdown()
#     print("🛑 健康检查任务已停止")

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="**matrix**内部接口文档",
    version="1.0.0",
    contact={
        "name": "软件开发部",
        "email": ""
    },
    license_info={
        "name": "内部使用授权协议"
    },
    docs_url="/swagger",
    redoc_url=None,
    # lifespan=lifespan  # 使用 lifespan 替代 startup/shutdown 事件
)

app.add_middleware(APIKeyAuthMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

ROUTERS = [
    (health.router, "Health"),
    (practise_score.router, "PractiseScore"),
    (practise_analysis.router, "PractiseAnalysis"),
    (note_summarize.router, "NoteSummarize"),
    (entity_extract_experience.router, "EntityExtractExperience"),
    (generate_potential_line.router, "GeneratePotentialLine"),
    (ocr_interface_forward.router, "OcrInterfaceForward"),  # 通用端口转发
    (tts_interface_forward.router, "TTSInterfaceForward"),  # TTS通用端口转发
    (asr_interface_forward.router, "ASRInterfaceForward"),  # TTS通用端口转发
    (openai_interface_forward.router, "OpenaiInterfaceForward"),  # openai通用端口转发,通用大模型调用
    (get_llm_model.router, "GetLLMModel"),  # 获取可用大模型列表，模型描述
    (face_recognition_interface_forward.router, "FaceRecognitionInterfaceForward"),  # 人脸通用端口转发
    (multimodal_image_interface_forward.router, "MultimodalImageInterfaceForward"),  # 多模态图片生成相关
    (ocr_interface_forward_new.router, "OCRInterfaceForwardNew"),  # orc新版
    (practise_generate.router, "PractiseGenerate"),  # 练习题生成
    (translate.router, "Translate"),  # 翻译接口

]

for router, tag in ROUTERS:
    app.include_router(
        router,
        prefix=settings.API_V1_STR,
        tags=[tag]
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, timeout_keep_alive=300, reload=True)
