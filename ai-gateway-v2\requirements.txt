# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
uvloop==0.19.0
httptools==0.6.1

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 数据验证
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# 缓存
aioredis==2.0.1
cachetools==5.3.2

# 消息队列
celery==5.3.4
kombu==5.3.4

# 监控和追踪
prometheus-client==0.19.0
prometheus-fastapi-instrumentator==6.1.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-exporter-jaeger==1.21.0

# 服务发现
python-consul==1.1.0
consul==1.1.0

# 日志
structlog==23.2.0
colorama==0.4.6

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 工具库
click==8.1.7
python-dotenv==1.0.0
tenacity==8.2.3
backoff==2.2.1

# 图像处理 (OCR服务)
Pillow==10.1.0
opencv-python==********
numpy==1.25.2

# 音频处理 (TTS/ASR服务)
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1

# 机器学习
torch==2.1.1
transformers==4.36.0
tokenizers==0.15.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 性能测试
locust==2.17.0
wrk==0.4.0

# 部署
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0
