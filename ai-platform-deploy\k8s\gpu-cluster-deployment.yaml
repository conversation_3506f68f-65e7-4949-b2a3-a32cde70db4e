apiVersion: apps/v1
kind: Deployment
metadata:
  name: tts-gpu-cluster
  namespace: ai-platform
  labels:
    app: tts-gpu-cluster
    tier: gpu-compute
spec:
  replicas: 10  # 10台TTS算力服务器
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
  selector:
    matchLabels:
      app: tts-gpu-cluster
  template:
    metadata:
      labels:
        app: tts-gpu-cluster
        tier: gpu-compute
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - tts-gpu-cluster
            topologyKey: kubernetes.io/hostname
      containers:
      - name: tts-gpu-worker
        image: yourorg/tts-gpu-worker:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: MODEL_PATH
          value: "/models/tts"
        - name: BATCH_SIZE
          value: "32"
        - name: MAX_WORKERS
          value: "4"
        - name: GPU_MEMORY_FRACTION
          value: "0.9"
        resources:
          requests:
            memory: "8Gi"
            cpu: "4000m"
            nvidia.com/gpu: 1
          limits:
            memory: "32Gi"
            cpu: "16000m"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: model-storage
          mountPath: /models
          readOnly: true
        - name: tmp-storage
          mountPath: /tmp
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 2
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-storage-pvc
      - name: tmp-storage
        emptyDir:
          sizeLimit: 10Gi
      nodeSelector:
        accelerator: "nvidia-tesla-v100"  # GPU节点选择器
        node-type: "gpu-compute"
      tolerations:
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: tts-gpu-cluster
  namespace: ai-platform
  labels:
    app: tts-gpu-cluster
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: tts-gpu-cluster

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: llm-gpu-cluster
  namespace: ai-platform
  labels:
    app: llm-gpu-cluster
    tier: gpu-compute
spec:
  replicas: 20  # 20台LLM算力服务器
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 3
      maxUnavailable: 2
  selector:
    matchLabels:
      app: llm-gpu-cluster
  template:
    metadata:
      labels:
        app: llm-gpu-cluster
        tier: gpu-compute
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - llm-gpu-cluster
            topologyKey: kubernetes.io/hostname
      containers:
      - name: llm-gpu-worker
        image: yourorg/llm-gpu-worker:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: MODEL_NAME
          value: "qwen-72b"
        - name: MAX_BATCH_SIZE
          value: "16"
        - name: MAX_SEQUENCE_LENGTH
          value: "4096"
        - name: TENSOR_PARALLEL_SIZE
          value: "2"
        resources:
          requests:
            memory: "32Gi"
            cpu: "8000m"
            nvidia.com/gpu: 2
          limits:
            memory: "128Gi"
            cpu: "32000m"
            nvidia.com/gpu: 2
        volumeMounts:
        - name: model-storage
          mountPath: /models
          readOnly: true
        - name: cache-storage
          mountPath: /cache
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 60
          timeoutSeconds: 30
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 15
          failureThreshold: 2
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-storage-pvc
      - name: cache-storage
        emptyDir:
          sizeLimit: 50Gi
      nodeSelector:
        accelerator: "nvidia-a100"  # 高端GPU节点
        node-type: "gpu-compute"
      tolerations:
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: llm-gpu-cluster
  namespace: ai-platform
  labels:
    app: llm-gpu-cluster
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: llm-gpu-cluster
