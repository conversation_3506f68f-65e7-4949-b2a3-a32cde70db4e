#!/bin/bash

# FunASR官方部署脚本
# 使用FunASR官方镜像和HTTP服务

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🚀 FunASR官方部署脚本${NC}"
echo "使用FunASR官方镜像和HTTP服务"
echo "========================================"

# 加载配置
load_config() {
    if [ -f "config.env" ]; then
        source config.env
        echo -e "${GREEN}✅ 配置文件加载完成${NC}"
    else
        echo -e "${RED}❌ 未找到config.env配置文件${NC}"
        exit 1
    fi
}

# 检查环境
check_environment() {
    echo -e "${YELLOW}🔍 检查部署环境...${NC}"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装${NC}"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    
    # 检查NVIDIA Docker (使用官方FunASR GPU镜像测试)
    if ! docker run --rm --gpus all ${FUNASR_IMAGE} nvidia-smi &> /dev/null; then
        echo -e "${RED}❌ NVIDIA Docker支持未配置或无法访问GPU${NC}"
        exit 1
    fi
    
    # 检查GPU数量
    gpu_count=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
    echo -e "${CYAN}💻 检测到 $gpu_count 个GPU${NC}"
    
    if [ "$gpu_count" -lt "${GPU_COUNT:-4}" ]; then
        echo -e "${YELLOW}⚠️ 配置需要${GPU_COUNT:-4}个GPU，但只检测到${gpu_count}个${NC}"
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 拉取官方镜像
pull_official_image() {
    echo -e "${YELLOW}📥 拉取FunASR官方镜像...${NC}"
    
    docker pull ${FUNASR_IMAGE}
    
    echo -e "${GREEN}✅ 官方镜像拉取完成${NC}"
}

# 部署服务
deploy_services() {
    echo -e "${YELLOW}🚀 部署FunASR官方服务...${NC}"
    
    # 停止现有服务
    docker-compose down 2>/dev/null || true
    
    # 启动核心服务
    docker-compose up -d funasr-gpu-0 funasr-gpu-1 funasr-gpu-2 funasr-gpu-3 funasr-loadbalancer
    
    # 如果启用监控，启动监控服务
    if [ "${ENABLE_PROMETHEUS:-true}" = "true" ] || [ "${ENABLE_GRAFANA:-true}" = "true" ]; then
        echo -e "${YELLOW}📊 启动监控服务...${NC}"
        docker-compose --profile monitoring up -d
    fi
    
    echo -e "${GREEN}✅ 服务部署完成${NC}"
}

# 等待服务就绪
wait_for_services() {
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    
    # 等待FunASR实例启动
    base_port=${BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((base_port + i))
        echo -n "等待FunASR实例 GPU-$i (端口$port) 启动..."
        for j in {1..180}; do  # 等待最多3分钟
            if curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
                echo -e " ${GREEN}✅${NC}"
                break
            fi
            echo -n "."
            sleep 1
        done
    done
    
    # 等待负载均衡器
    nginx_port=${NGINX_PORT:-8080}
    echo -n "等待负载均衡器(端口$nginx_port)启动..."
    for i in {1..60}; do
        if curl -f -s http://localhost:$nginx_port/health > /dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            break
        fi
        echo -n "."
        sleep 1
    done
    
    echo -e "${GREEN}✅ 所有服务启动完成${NC}"
}

# 健康检查
health_check() {
    echo -e "${YELLOW}🔍 执行健康检查...${NC}"
    
    # 检查容器状态
    echo -e "\n${CYAN}📦 容器状态:${NC}"
    docker-compose ps
    
    echo -e "\n${CYAN}🏥 FunASR实例健康检查:${NC}"
    
    # 检查各个FunASR实例
    base_port=${BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((base_port + i))
        container_name="funasr-official-gpu-$i"
        
        if curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
            echo -e "  ${GREEN}✅ $container_name (端口$port): 正常${NC}"
        else
            echo -e "  ${RED}❌ $container_name (端口$port): 异常${NC}"
        fi
    done
    
    # 检查负载均衡器
    echo -e "\n${CYAN}⚖️ 负载均衡器:${NC}"
    nginx_port=${NGINX_PORT:-8080}
    if curl -f -s http://localhost:$nginx_port/health > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 负载均衡器(端口$nginx_port): 正常${NC}"
    else
        echo -e "  ${RED}❌ 负载均衡器(端口$nginx_port): 异常${NC}"
    fi
    
    # 检查监控服务
    if [ "${ENABLE_PROMETHEUS:-true}" = "true" ] || [ "${ENABLE_GRAFANA:-true}" = "true" ]; then
        echo -e "\n${CYAN}📊 监控服务:${NC}"
        
        if [ "${ENABLE_PROMETHEUS:-true}" = "true" ]; then
            prometheus_port=${PROMETHEUS_PORT:-9090}
            if curl -f -s http://localhost:$prometheus_port/-/healthy > /dev/null 2>&1; then
                echo -e "  ${GREEN}✅ Prometheus(端口$prometheus_port): 正常${NC}"
            else
                echo -e "  ${RED}❌ Prometheus(端口$prometheus_port): 异常${NC}"
            fi
        fi
        
        if [ "${ENABLE_GRAFANA:-true}" = "true" ]; then
            grafana_port=${GRAFANA_PORT:-3000}
            if curl -f -s http://localhost:$grafana_port/api/health > /dev/null 2>&1; then
                echo -e "  ${GREEN}✅ Grafana(端口$grafana_port): 正常${NC}"
            else
                echo -e "  ${RED}❌ Grafana(端口$grafana_port): 异常${NC}"
            fi
        fi
    fi
    
    echo -e "${GREEN}✅ 健康检查完成${NC}"
}

# 显示访问信息
show_access_info() {
    echo -e "${GREEN}🎉 FunASR官方服务部署成功！${NC}"
    echo ""
    echo -e "${CYAN}🌐 服务访问地址:${NC}"
    echo "  🔗 负载均衡器:     http://localhost:${NGINX_PORT:-8080}"
    
    if [ "${ENABLE_PROMETHEUS:-true}" = "true" ]; then
        echo "  📊 Prometheus:     http://localhost:${PROMETHEUS_PORT:-9090}"
    fi
    
    if [ "${ENABLE_GRAFANA:-true}" = "true" ]; then
        echo "  📈 Grafana:        http://localhost:${GRAFANA_PORT:-3000} (admin/admin123)"
    fi
    
    echo ""
    echo -e "${CYAN}🔧 FunASR实例直接访问:${NC}"
    
    base_port=${BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((base_port + i))
        echo "  📍 GPU-$i实例:      http://localhost:$port"
    done
    
    echo ""
    echo -e "${CYAN}🧪 测试命令:${NC}"
    echo "  # 通过负载均衡器测试 (官方API)"
    echo "  curl -X POST -F 'file=@test.wav' http://localhost:${NGINX_PORT:-8080}/fileASR"
    echo ""
    echo "  # 兼容接口测试"
    echo "  curl -X POST -F 'file=@test.wav' http://localhost:${NGINX_PORT:-8080}/llm/asr/recognition"
    echo ""
    echo "  # 查看实例健康状态"
    echo "  curl http://localhost:${BASE_PORT:-10095}/health"
    echo ""
    echo "  # 查看集群状态"
    echo "  curl http://localhost:${NGINX_PORT:-8080}/cluster/health"
    echo ""
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "  查看状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f"
    echo "  重启服务: docker-compose restart"
    echo "  停止服务: docker-compose down"
    echo ""
    echo -e "${CYAN}📊 架构特点:${NC}"
    echo "  🏗️ 架构: 100%官方FunASR镜像和服务"
    echo "  ⚡ 实例: ${GPU_COUNT:-4}个GPU实例负载均衡"
    echo "  🔄 负载均衡: Nginx轮询分发"
    echo "  🛡️ 故障隔离: GPU级别隔离"
    echo "  📊 监控: Prometheus + Grafana"
    echo ""
    echo -e "${GREEN}✨ FunASR官方集群已就绪！${NC}"
}

# 主函数
main() {
    case $1 in
        "deploy")
            load_config
            check_environment
            pull_official_image
            deploy_services
            wait_for_services
            health_check
            show_access_info
            ;;
        "check")
            load_config
            health_check
            ;;
        "stop")
            docker-compose down
            echo -e "${GREEN}✅ 服务已停止${NC}"
            ;;
        "clean")
            docker-compose down -v
            docker system prune -f
            echo -e "${GREEN}✅ 环境已清理${NC}"
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "restart")
            docker-compose restart
            echo -e "${GREEN}✅ 服务已重启${NC}"
            ;;
        *)
            echo "用法: $0 {deploy|check|stop|clean|logs|restart}"
            echo ""
            echo "  deploy   - 部署FunASR官方服务"
            echo "  check    - 健康检查"
            echo "  stop     - 停止服务"
            echo "  clean    - 清理环境"
            echo "  logs     - 查看日志"
            echo "  restart  - 重启服务"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
