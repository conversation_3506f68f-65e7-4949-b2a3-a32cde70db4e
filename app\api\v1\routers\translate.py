from fastapi import APIRouter, File, Form, UploadFile, Response
from app.services.translate_service import TranslateService
from app.models.translate_model import (
    TranslateTextToTextRequest, TranslateTextToTextResponse, 
    TranslateAudioToTextResponse, 
    TranslateTextToAudioRequest
)

router = APIRouter()
service = TranslateService()

@router.post("/llm/matrix/translate/text_to_text", response_model=TranslateTextToTextResponse)
async def translate_text_to_text(request: TranslateTextToTextRequest):
    return await service.translate_text_to_text(request.language_pair, request.text_list)

@router.post("/llm/matrix/translate/audio_to_text", response_model=TranslateAudioToTextResponse)
async def translate_audio_to_text(language_pair: str = Form(...), file: UploadFile = File(...)):
    return await service.translate_audio_to_text(language_pair, file)

@router.post("/llm/matrix/translate/text_to_audio")
async def translate_text_to_audio(request: TranslateTextToAudioRequest):
    audio_content = await service.translate_text_to_audio(request.language_pair, request.text, request.speaker)
    return Response(content=audio_content, media_type="audio/mpeg")


@router.post("/llm/matrix/translate/audio_to_audio")
async def translate_audio_to_audio(language_pair: str = Form(...), file: UploadFile = File(...), speaker: str = Form("zh-CN-XiaoxiaoNeural")):
    audio_content = await service.translate_audio_to_audio(language_pair, file, speaker)
    return Response(content=audio_content, media_type="audio/mpeg")

