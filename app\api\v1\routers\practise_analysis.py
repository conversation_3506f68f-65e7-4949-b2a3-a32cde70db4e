from fastapi import APIRouter
from app.services.practise_analysis_service import PractiseAnalysisService
from app.models.practise_analysis_model import AnalysisRequest, AnalysisResponse

router = APIRouter()
service = PractiseAnalysisService()


@router.post("/llm/matrix/practise/analysis", response_model=AnalysisResponse)
async def practise_score(request: AnalysisRequest):
    return await service.analyze_practise(request.content, request.subject, request.llm_params)
