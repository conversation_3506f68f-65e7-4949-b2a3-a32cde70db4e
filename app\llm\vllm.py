import httpx
from tenacity import retry, wait_exponential
import app.core.forward_route_config as forward_route_config
from app.services.openai_interface_forward_service import OpenaiInterfaceForwardService
from app.utils.util import create_forward_url

class VLLMClient:
    def __init__(self, endpoint: str):
        self.endpoint = endpoint

    async def generate(self, prompt: str, client_body={}, system_role='你是一个编程高手') -> dict:
        
        client_body.update({
            "temperature": client_body.get('temperature', 0.0),  # 替换为实际温度值

            "stream": False,
            "model": "Qwen3-32B",  # 替换为实际模型名称
            "messages": [
                {"role": "system", "content": system_role},
                {"role": "user", "content": prompt}], 

            "response_format": {
                "type": "json_object"
            }
        })

        if 'chat_template_kwargs' not in client_body.keys():
            client_body['chat_template_kwargs'] = {
                "enable_thinking": False
            }

        headers = {
            "Content-Type": "application/json"
        }
        headers.update({"X-MODEL": "Qwen3-32B"})

        target_url, _ = create_forward_url(client_body.get("model"), "/llm/openai/v1/chat/completions")

        print('vllm params url', target_url)
        print('vllm params headers', headers)
        print('vllm params body', client_body)
        

        async with httpx.AsyncClient(timeout=300) as client:
                target_response = await client.post(
                    target_url,
                    json=client_body,
                    headers=headers
                )
                target_response.raise_for_status()
                return target_response.json()  # 直接返回目标服务的JSON响应