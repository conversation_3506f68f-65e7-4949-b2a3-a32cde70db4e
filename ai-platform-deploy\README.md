# AI Platform Deploy - AI服务平台部署

## 📋 项目简介

AI服务平台的统一部署仓库，包含所有服务的Docker Compose配置和部署脚本。

## 🏗️ 架构图

```
┌─────────────────┐    ┌─────────────────┐
│   教育项目       │    │   医疗项目       │
│   (8100)        │    │   (8200)        │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
        ┌────────────▼────────────┐
        │      AI Gateway         │
        │      (8000)             │
        └────────────┬────────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌───▼───┐    ┌───▼───┐    ┌───▼───┐    ┌───▼───┐
│  OCR  │    │  TTS  │    │  ASR  │    │  LLM  │
│ 后端   │    │ 8002  │    │ 后端   │    │ 后端   │
└───────┘    └───────┘    └───────┘    └───────┘
```

## 🚀 快速部署

### 前置要求
- Docker
- Docker Compose
- Git

### 一键部署
```bash
# 克隆部署仓库
git clone https://github.com/yourorg/ai-platform-deploy.git
cd ai-platform-deploy

# 一键部署
chmod +x deploy.sh
./deploy.sh
```

### 手动部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📋 服务列表

| 服务 | 端口 | 仓库 | 描述 |
|------|------|------|------|
| AI网关 | 8000 | ai-gateway | 统一API入口 |
| TTS服务 | 8002 | ai-tts-service | 文本转语音 |
| 教育平台 | 8100 | ai-education-app | 教育业务应用 |
| 医疗平台 | 8200 | ai-medical-app | 医疗业务应用 |
| Redis | 6379 | - | 缓存服务 |

## ⚙️ 配置说明

### 环境变量配置
编辑 `.env` 文件：

```bash
# 后端AI服务器地址
OCR_BACKENDS=http://192.168.235.198:20060,http://61.172.167.136:10099
TTS_BACKENDS=http://tts-service:8002
ASR_BACKENDS=http://61.172.167.136:10099
LLM_BACKENDS=http://192.168.235.198:20060,http://61.172.167.136:10099

# Redis配置
REDIS_URL=redis://redis:6379

# TTS服务配置
TTS_API_KEY=your-tts-api-key
TTS_BASE_URL=https://your-tts-server.com
```

### 服务镜像配置
在 `docker-compose.yml` 中配置各服务的镜像：

```yaml
services:
  gateway:
    image: yourorg/ai-gateway:latest
    # 或使用本地构建
    # build: https://github.com/yourorg/ai-gateway.git
```

## 🔧 管理命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d gateway tts-service

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart gateway

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f gateway
```

### 更新服务
```bash
# 拉取最新镜像
docker-compose pull

# 重新构建并启动
docker-compose up -d --build

# 更新特定服务
docker-compose pull gateway
docker-compose up -d gateway
```

## 🧪 测试验证

### 健康检查
```bash
# 检查所有服务
./scripts/health_check.sh

# 手动检查
curl http://localhost:8000/health
curl http://localhost:8002/health
curl http://localhost:8100/health
```

### 功能测试
```bash
# 测试TTS服务
curl -X POST http://localhost:8000/api/v1/tts \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: test" \
  -d '{"text": "Hello World", "voice": "zh-CN-XiaoxiaoNeural"}'

# 测试教育平台
curl -X POST http://localhost:8100/api/questions/generate \
  -H "Content-Type: application/json" \
  -d '{"topic": "数学", "subject": "小学数学", "count": 3}'
```

## 📊 监控

### 服务状态
- AI网关: http://localhost:8000/health
- TTS服务: http://localhost:8002/health
- 教育平台: http://localhost:8100/health

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f gateway
docker-compose logs -f tts-service
```

## 🔄 CI/CD集成

### GitHub Actions示例
```yaml
name: Deploy AI Platform
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy
        run: |
          docker-compose pull
          docker-compose up -d
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request

## 📞 支持

如有问题，请提交 Issue 或联系维护团队。
