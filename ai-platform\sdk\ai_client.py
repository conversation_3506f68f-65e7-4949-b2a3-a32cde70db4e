"""
AI服务平台客户端SDK - 简化版本
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

import httpx


@dataclass
class AIResult:
    """AI服务结果"""
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    error: Optional[str] = None

    @property
    def text(self) -> str:
        """提取文本结果"""
        return self.data.get("text", "")
    
    @property
    def content(self) -> str:
        """提取内容结果"""
        return self.data.get("content", "")
    
    @property
    def audio_url(self) -> str:
        """提取音频URL"""
        return self.data.get("audio_url", "")


class AIClient:
    """AI服务客户端"""
    
    def __init__(self, base_url: str, tenant_id: str = "default", timeout: int = 30):
        """
        初始化客户端
        
        Args:
            base_url: AI网关地址
            tenant_id: 租户ID
            timeout: 请求超时时间
        """
        self.base_url = base_url.rstrip("/")
        self.tenant_id = tenant_id
        
        self.session = httpx.AsyncClient(
            timeout=timeout,
            headers={
                "Content-Type": "application/json",
                "X-Tenant-ID": tenant_id
            }
        )
    
    async def close(self):
        """关闭客户端"""
        await self.session.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def ocr(
        self, 
        image_data: Union[bytes, str] = None,
        image_url: str = None,
        options: Dict[str, Any] = None
    ) -> AIResult:
        """
        OCR图像识别
        
        Args:
            image_data: 图片数据（bytes）
            image_url: 图片URL
            options: 选项参数
        """
        payload = {"options": options or {}}
        
        if image_data:
            if isinstance(image_data, bytes):
                import base64
                payload["image_data"] = base64.b64encode(image_data).decode()
            else:
                payload["image_data"] = image_data
        elif image_url:
            payload["image_url"] = image_url
        else:
            return AIResult(False, {}, {}, "需要提供image_data或image_url")
        
        return await self._call_service("/api/v1/ocr", payload)
    
    async def tts(
        self,
        text: str,
        voice: str = "zh-CN-XiaoxiaoNeural",
        speed: float = 1.0,
        pitch: float = 0.0,
        volume: float = 1.0,
        stream: bool = False,
        options: Dict[str, Any] = None
    ) -> AIResult:
        """
        文本转语音

        Args:
            text: 要转换的文本
            voice: 声音类型
            speed: 语速 (0.5-2.0)
            pitch: 音调 (-1.0-1.0)
            volume: 音量 (0.0-1.0)
            stream: 是否流式返回
            options: 选项参数
        """
        payload = {
            "text": text,
            "voice": voice,
            "options": {
                "speed": speed,
                "pitch": pitch,
                "volume": volume,
                "stream": stream,
                **(options or {})
            }
        }

        return await self._call_service("/api/v1/tts", payload)

    async def get_tts_speakers(
        self,
        gender: Optional[str] = None,
        language: Optional[str] = None,
        model: str = "edge"
    ) -> AIResult:
        """
        获取TTS语音列表

        Args:
            gender: 性别筛选 (Male, Female)
            language: 语言筛选
            model: TTS模型
        """
        try:
            params = {"model": model}
            if gender:
                params["gender"] = gender
            if language:
                params["language"] = language

            response = await self.session.get(
                f"{self.base_url}/api/v1/tts/speakers",
                params=params
            )
            response.raise_for_status()

            result = response.json()
            return AIResult(True, result, {})

        except Exception as e:
            return AIResult(False, {}, {}, str(e))
    
    async def asr(
        self,
        audio_data: Union[bytes, str] = None,
        audio_url: str = None,
        options: Dict[str, Any] = None
    ) -> AIResult:
        """
        语音识别
        
        Args:
            audio_data: 音频数据（bytes）
            audio_url: 音频URL
            options: 选项参数
        """
        payload = {"options": options or {}}
        
        if audio_data:
            if isinstance(audio_data, bytes):
                import base64
                payload["audio_data"] = base64.b64encode(audio_data).decode()
            else:
                payload["audio_data"] = audio_data
        elif audio_url:
            payload["audio_url"] = audio_url
        else:
            return AIResult(False, {}, {}, "需要提供audio_data或audio_url")
        
        return await self._call_service("/api/v1/asr", payload)
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "auto",
        options: Dict[str, Any] = None
    ) -> AIResult:
        """
        LLM对话
        
        Args:
            messages: 对话消息列表
            model: 模型名称
            options: 选项参数
        """
        payload = {
            "messages": messages,
            "model": model,
            "options": options or {}
        }
        
        return await self._call_service("/api/v1/llm", payload)
    
    async def complete(
        self,
        prompt: str,
        model: str = "auto",
        options: Dict[str, Any] = None
    ) -> AIResult:
        """
        文本补全
        
        Args:
            prompt: 提示文本
            model: 模型名称
            options: 选项参数
        """
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(messages, model, options)
    
    async def get_services(self) -> Dict[str, Any]:
        """获取可用服务列表"""
        try:
            response = await self.session.get(f"{self.base_url}/api/v1/services")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    async def _call_service(self, endpoint: str, payload: Dict[str, Any]) -> AIResult:
        """调用AI服务"""
        try:
            response = await self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload
            )
            response.raise_for_status()
            
            result = response.json()
            
            return AIResult(
                success=result["success"],
                data=result["data"],
                metadata=result["metadata"]
            )
            
        except httpx.HTTPStatusError as e:
            error_detail = e.response.json() if e.response.content else str(e)
            return AIResult(False, {}, {}, str(error_detail))
        except Exception as e:
            return AIResult(False, {}, {}, str(e))


# 便捷函数
async def create_client(base_url: str, tenant_id: str = "default") -> AIClient:
    """创建AI客户端"""
    return AIClient(base_url, tenant_id)


# 使用示例
async def example():
    """使用示例"""
    async with AIClient("http://localhost:8000", "education") as client:
        # OCR识别
        result = await client.ocr(image_url="https://example.com/image.jpg")
        if result.success:
            print(f"识别结果: {result.text}")
        
        # TTS合成
        result = await client.tts("Hello World")
        if result.success:
            print(f"音频URL: {result.audio_url}")
        
        # LLM对话
        result = await client.chat([{"role": "user", "content": "你好"}])
        if result.success:
            print(f"回复: {result.content}")


if __name__ == "__main__":
    asyncio.run(example())
