#!/usr/bin/env python3
"""
FunASR WebSocket实时语音转写客户端
支持麦克风实时录音和WebSocket流式识别
"""

import asyncio
import websockets
import json
import pyaudio
import wave
import threading
import time
import argparse
from typing import Optional

class FunASRWebSocketClient:
    def __init__(self, 
                 server_url: str = "ws://localhost:8080/ws/asr",
                 sample_rate: int = 16000,
                 chunk_size: int = 1024,
                 channels: int = 1):
        """
        初始化FunASR WebSocket客户端
        
        Args:
            server_url: WebSocket服务器地址
            sample_rate: 采样率
            chunk_size: 音频块大小
            channels: 声道数
        """
        self.server_url = server_url
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.channels = channels
        
        # 音频配置
        self.format = pyaudio.paInt16
        self.audio = pyaudio.PyAudio()
        self.stream: Optional[pyaudio.Stream] = None
        
        # 控制标志
        self.is_recording = False
        self.is_connected = False
        
        # WebSocket连接
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            print(f"🔗 连接到FunASR服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            self.is_connected = True
            
            # 发送初始配置
            config = {
                "mode": "2pass",
                "chunk_size": [5, 10, 5],
                "chunk_interval": 10,
                "wav_name": "microphone",
                "wav_format": "PCM"
            }
            
            await self.websocket.send(json.dumps(config))
            print("✅ 连接成功，配置已发送")
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            self.is_connected = False
            raise
    
    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            print("🔌 WebSocket连接已断开")
    
    def start_recording(self):
        """开始录音"""
        try:
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            self.is_recording = True
            print("🎤 开始录音...")
            
        except Exception as e:
            print(f"❌ 录音启动失败: {e}")
            raise
    
    def stop_recording(self):
        """停止录音"""
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
            self.is_recording = False
            print("⏹️ 录音已停止")
    
    async def send_audio_data(self):
        """发送音频数据到WebSocket服务器"""
        if not self.is_connected or not self.websocket:
            return
            
        try:
            while self.is_recording and self.is_connected:
                if self.stream:
                    # 读取音频数据
                    audio_data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                    
                    # 发送音频数据
                    await self.websocket.send(audio_data)
                    
                # 控制发送频率
                await asyncio.sleep(0.01)  # 10ms间隔
                
        except Exception as e:
            print(f"❌ 音频发送失败: {e}")
    
    async def receive_results(self):
        """接收识别结果"""
        if not self.is_connected or not self.websocket:
            return
            
        try:
            async for message in self.websocket:
                try:
                    # 尝试解析JSON消息
                    result = json.loads(message)
                    
                    # 提取识别文本
                    text = result.get('text', '')
                    is_final = result.get('is_final', False)
                    timestamp = result.get('timestamp', '')
                    
                    if text:
                        status = "【最终】" if is_final else "【临时】"
                        print(f"🗣️ {status} {text}")
                        
                except json.JSONDecodeError:
                    # 可能是二进制数据或其他格式
                    print(f"📨 收到非JSON消息: {len(message)} bytes")
                    
        except Exception as e:
            print(f"❌ 接收结果失败: {e}")
    
    async def realtime_transcribe(self, duration: Optional[int] = None):
        """实时转写主函数"""
        try:
            # 连接WebSocket
            await self.connect()
            
            # 开始录音
            self.start_recording()
            
            # 创建发送和接收任务
            send_task = asyncio.create_task(self.send_audio_data())
            receive_task = asyncio.create_task(self.receive_results())
            
            print(f"🚀 实时转写已开始...")
            if duration:
                print(f"⏱️ 将运行 {duration} 秒")
                await asyncio.sleep(duration)
            else:
                print("💡 按 Ctrl+C 停止转写")
                # 等待用户中断
                try:
                    await asyncio.gather(send_task, receive_task)
                except KeyboardInterrupt:
                    print("\n⏹️ 用户中断转写")
            
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断转写")
        except Exception as e:
            print(f"❌ 转写过程出错: {e}")
        finally:
            # 清理资源
            self.stop_recording()
            await self.disconnect()
            
            # 取消任务
            if 'send_task' in locals():
                send_task.cancel()
            if 'receive_task' in locals():
                receive_task.cancel()
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'audio'):
            self.audio.terminate()

async def test_file_upload(server_base_url: str, audio_file: str):
    """测试HTTP文件上传接口"""
    import aiohttp
    import aiofiles
    
    url = f"{server_base_url.replace('ws://', 'http://').replace('/ws/asr', '')}/fileASR"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with aiofiles.open(audio_file, 'rb') as f:
                audio_data = await f.read()
                
            data = aiohttp.FormData()
            data.add_field('file', audio_data, filename='test.wav', content_type='audio/wav')
            
            print(f"📤 上传文件到: {url}")
            async with session.post(url, data=data) as response:
                result = await response.json()
                print(f"📥 识别结果: {result}")
                
    except Exception as e:
        print(f"❌ 文件上传测试失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="FunASR WebSocket实时语音转写客户端")
    parser.add_argument("--server", default="ws://localhost:8080/ws/asr", 
                       help="WebSocket服务器地址")
    parser.add_argument("--duration", type=int, help="转写时长(秒)，不指定则持续运行")
    parser.add_argument("--test-file", help="测试HTTP文件上传接口的音频文件路径")
    parser.add_argument("--sample-rate", type=int, default=16000, help="采样率")
    parser.add_argument("--chunk-size", type=int, default=1024, help="音频块大小")
    
    args = parser.parse_args()
    
    print("🎯 FunASR WebSocket实时语音转写客户端")
    print("=" * 50)
    
    # 如果指定了测试文件，先测试HTTP接口
    if args.test_file:
        print("📁 测试HTTP文件上传接口...")
        asyncio.run(test_file_upload(args.server, args.test_file))
        print()
    
    # 创建客户端
    client = FunASRWebSocketClient(
        server_url=args.server,
        sample_rate=args.sample_rate,
        chunk_size=args.chunk_size
    )
    
    # 开始实时转写
    try:
        asyncio.run(client.realtime_transcribe(args.duration))
    except KeyboardInterrupt:
        print("\n👋 程序退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
