#!/bin/bash

# AI Gateway V2 部署脚本
# 支持本地开发、Docker和Kubernetes部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_warning "Docker 未安装，将跳过Docker相关功能"
        DOCKER_AVAILABLE=false
    else
        DOCKER_AVAILABLE=true
    fi
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_warning "kubectl 未安装，将跳过Kubernetes相关功能"
        K8S_AVAILABLE=false
    else
        K8S_AVAILABLE=true
    fi
    
    log_success "依赖检查完成"
}

# 本地开发环境部署
deploy_local() {
    log_info "部署本地开发环境..."
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    log_info "安装Python依赖..."
    pip install -r requirements.txt
    
    # 启动基础设施
    if [ "$DOCKER_AVAILABLE" = true ]; then
        log_info "启动基础设施服务..."
        docker-compose up -d redis postgres consul
        
        # 等待服务启动
        log_info "等待服务启动..."
        sleep 10
    else
        log_warning "Docker不可用，请手动启动Redis、PostgreSQL和Consul"
    fi
    
    # 数据库迁移
    log_info "执行数据库迁移..."
    cd gateway
    alembic upgrade head
    cd ..
    
    log_success "本地开发环境部署完成"
    log_info "启动命令:"
    log_info "  网关: cd gateway && uvicorn main:app --reload --port 8000"
    log_info "  OCR服务: cd services/ocr-service && uvicorn main:app --reload --port 8001"
}

# Docker部署
deploy_docker() {
    log_info "Docker部署..."
    
    if [ "$DOCKER_AVAILABLE" != true ]; then
        log_error "Docker不可用"
        exit 1
    fi
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose build
    
    # 启动服务
    log_info "启动所有服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 健康检查
    log_info "执行健康检查..."
    for i in {1..10}; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log_success "服务启动成功"
            break
        fi
        log_info "等待服务启动... ($i/10)"
        sleep 5
    done
    
    # 显示服务状态
    log_info "服务状态:"
    docker-compose ps
    
    log_success "Docker部署完成"
    log_info "访问地址:"
    log_info "  API网关: http://localhost:8000"
    log_info "  API文档: http://localhost:8000/docs"
    log_info "  监控面板: http://localhost:3000"
    log_info "  Prometheus: http://localhost:9090"
}

# Kubernetes部署
deploy_k8s() {
    log_info "Kubernetes部署..."
    
    if [ "$K8S_AVAILABLE" != true ]; then
        log_error "kubectl不可用"
        exit 1
    fi
    
    # 创建命名空间
    log_info "创建命名空间..."
    kubectl create namespace ai-gateway --dry-run=client -o yaml | kubectl apply -f -
    
    # 部署基础设施
    log_info "部署基础设施..."
    kubectl apply -f kubernetes/infrastructure/
    
    # 等待基础设施就绪
    log_info "等待基础设施就绪..."
    kubectl wait --for=condition=ready pod -l app=redis -n ai-gateway --timeout=300s
    kubectl wait --for=condition=ready pod -l app=postgres -n ai-gateway --timeout=300s
    
    # 部署应用
    log_info "部署应用服务..."
    kubectl apply -f kubernetes/
    
    # 等待应用就绪
    log_info "等待应用就绪..."
    kubectl wait --for=condition=ready pod -l app=ai-gateway -n ai-gateway --timeout=300s
    
    # 显示部署状态
    log_info "部署状态:"
    kubectl get pods -n ai-gateway
    kubectl get services -n ai-gateway
    
    # 获取访问地址
    GATEWAY_URL=$(kubectl get ingress ai-gateway-ingress -n ai-gateway -o jsonpath='{.spec.rules[0].host}')
    if [ -n "$GATEWAY_URL" ]; then
        log_success "Kubernetes部署完成"
        log_info "访问地址: https://$GATEWAY_URL"
    else
        log_success "Kubernetes部署完成"
        log_info "使用端口转发访问: kubectl port-forward svc/ai-gateway-service 8000:80 -n ai-gateway"
    fi
}

# 性能测试
run_performance_test() {
    log_info "运行性能测试..."
    
    # 检查Locust
    if ! command -v locust &> /dev/null; then
        log_info "安装Locust..."
        pip install locust
    fi
    
    # 运行测试
    log_info "启动性能测试..."
    cd tests/performance
    
    # 基础性能测试
    log_info "运行基础性能测试 (1000用户, 5分钟)..."
    locust -f locustfile.py --host=http://localhost:8000 \
           --headless --users=1000 --spawn-rate=50 -t 300s \
           --html=performance_report.html
    
    log_success "性能测试完成，报告: tests/performance/performance_report.html"
}

# 清理环境
cleanup() {
    log_info "清理环境..."
    
    case $1 in
        "local")
            log_info "停止本地服务..."
            if [ "$DOCKER_AVAILABLE" = true ]; then
                docker-compose down
            fi
            ;;
        "docker")
            log_info "停止Docker服务..."
            docker-compose down -v
            docker system prune -f
            ;;
        "k8s")
            log_info "删除Kubernetes资源..."
            kubectl delete namespace ai-gateway
            ;;
        *)
            log_info "停止所有服务..."
            if [ "$DOCKER_AVAILABLE" = true ]; then
                docker-compose down -v
            fi
            if [ "$K8S_AVAILABLE" = true ]; then
                kubectl delete namespace ai-gateway --ignore-not-found=true
            fi
            ;;
    esac
    
    log_success "清理完成"
}

# 显示帮助
show_help() {
    echo "AI Gateway V2 部署脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  local     - 部署本地开发环境"
    echo "  docker    - Docker部署"
    echo "  k8s       - Kubernetes部署"
    echo "  test      - 运行性能测试"
    echo "  cleanup   - 清理环境 [local|docker|k8s]"
    echo "  help      - 显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 local          # 本地开发部署"
    echo "  $0 docker         # Docker部署"
    echo "  $0 k8s            # Kubernetes部署"
    echo "  $0 test           # 性能测试"
    echo "  $0 cleanup docker # 清理Docker环境"
}

# 主函数
main() {
    echo "🚀 AI Gateway V2 部署脚本"
    echo "================================"
    
    # 检查依赖
    check_dependencies
    
    # 处理命令
    case $1 in
        "local")
            deploy_local
            ;;
        "docker")
            deploy_docker
            ;;
        "k8s")
            deploy_k8s
            ;;
        "test")
            run_performance_test
            ;;
        "cleanup")
            cleanup $2
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            log_error "请指定命令"
            show_help
            exit 1
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
