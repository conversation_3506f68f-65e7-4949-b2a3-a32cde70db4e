#!/bin/bash

# FunASR实时语音转写部署脚本
# 支持HTTP文件识别 + WebSocket实时转写

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🚀 FunASR实时语音转写部署脚本${NC}"
echo "支持HTTP文件识别 + WebSocket实时转写"
echo "========================================"

# 加载配置
load_config() {
    if [ -f "config.env" ]; then
        source config.env
        echo -e "${GREEN}✅ 配置文件加载完成${NC}"
    else
        echo -e "${RED}❌ 未找到config.env配置文件${NC}"
        exit 1
    fi
}

# 检查环境
check_environment() {
    echo -e "${YELLOW}🔍 检查部署环境...${NC}"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装${NC}"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    
    # 检查NVIDIA Docker
    if ! docker run --rm --gpus all ${FUNASR_IMAGE} nvidia-smi &> /dev/null; then
        echo -e "${RED}❌ NVIDIA Docker支持未配置或无法访问GPU${NC}"
        exit 1
    fi
    
    # 检查GPU数量
    gpu_count=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
    echo -e "${CYAN}💻 检测到 $gpu_count 个GPU${NC}"
    
    if [ "$gpu_count" -lt "${GPU_COUNT:-4}" ]; then
        echo -e "${YELLOW}⚠️ 配置需要${GPU_COUNT:-4}个GPU，但只检测到${gpu_count}个${NC}"
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 拉取官方镜像
pull_official_image() {
    echo -e "${YELLOW}📥 拉取FunASR官方镜像...${NC}"
    
    docker pull ${FUNASR_IMAGE}
    
    echo -e "${GREEN}✅ 官方镜像拉取完成${NC}"
}

# 生成配置文件
generate_configs() {
    echo -e "${YELLOW}🔧 生成动态配置...${NC}"

    # 检查必要文件
    required_files=(
        "config.env"
        "generate-config.py"
    )

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}❌ 缺少必要文件: $file${NC}"
            exit 1
        fi
    done

    # 确保脚本可执行
    chmod +x generate-config.py 2>/dev/null || true

    # 生成配置文件
    python3 generate-config.py

    if [ ! -f "docker-compose.generated.yml" ]; then
        echo -e "${RED}❌ 配置生成失败${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ 动态配置生成完成${NC}"
}

# 部署服务
deploy_services() {
    echo -e "${YELLOW}🚀 部署FunASR实时转写服务...${NC}"

    # 停止现有服务
    docker-compose -f docker-compose.generated.yml down 2>/dev/null || true

    # 启动所有服务
    docker-compose -f docker-compose.generated.yml up -d

    echo -e "${GREEN}✅ 服务部署完成${NC}"
}

# 等待服务就绪
wait_for_services() {
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"

    # 从生成的配置文件中提取端口信息
    if [ ! -f "docker-compose.generated.yml" ]; then
        echo -e "${RED}❌ 未找到生成的配置文件${NC}"
        return 1
    fi

    # 提取HTTP端口
    http_ports=($(grep -E "^\s*-\s*\"[0-9]+:10095\"" docker-compose.generated.yml | grep -v "10195\|10196\|10197\|10198\|10199" | sed 's/.*"\([0-9]*\):.*/\1/' | sort -u))

    # 提取WebSocket端口
    wss_ports=($(grep -E "^\s*-\s*\"[0-9]+:10095\"" docker-compose.generated.yml | grep "10195\|10196\|10197\|10198\|10199" | sed 's/.*"\([0-9]*\):.*/\1/' | sort -u))

    echo "等待HTTP文件识别服务..."
    for port in "${http_ports[@]}"; do
        echo -n "  HTTP服务 (端口$port)..."
        for j in {1..180}; do
            if curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
                echo -e " ${GREEN}✅${NC}"
                break
            fi
            echo -n "."
            sleep 1
        done
    done

    echo "等待WebSocket实时转写服务..."
    for port in "${wss_ports[@]}"; do
        echo -n "  WebSocket服务 (端口$port)..."
        for j in {1..180}; do
            if curl -f -s http://localhost:$port/ > /dev/null 2>&1; then
                echo -e " ${GREEN}✅${NC}"
                break
            fi
            echo -n "."
            sleep 1
        done
    done

    # 等待负载均衡器
    nginx_port=$(grep "NGINX_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "8080")
    echo -n "等待负载均衡器(端口$nginx_port)启动..."
    for i in {1..60}; do
        if curl -f -s http://localhost:$nginx_port/health > /dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            break
        fi
        echo -n "."
        sleep 1
    done

    echo -e "${GREEN}✅ 所有服务启动完成${NC}"
}

# 健康检查
health_check() {
    echo -e "${YELLOW}🔍 执行健康检查...${NC}"
    
    # 检查容器状态
    echo -e "\n${CYAN}📦 容器状态:${NC}"
    docker-compose -f docker-compose.generated.yml ps
    
    echo -e "\n${CYAN}🏥 HTTP服务健康检查:${NC}"
    
    # 检查HTTP实例
    http_base_port=${HTTP_BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((http_base_port + i))
        container_name="funasr-http-gpu-$i"
        
        if curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
            echo -e "  ${GREEN}✅ $container_name (端口$port): 正常${NC}"
        else
            echo -e "  ${RED}❌ $container_name (端口$port): 异常${NC}"
        fi
    done
    
    echo -e "\n${CYAN}🌐 WebSocket服务健康检查:${NC}"
    
    # 检查WebSocket实例
    wss_base_port=${WSS_BASE_PORT:-10195}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((wss_base_port + i))
        container_name="funasr-wss-gpu-$i"
        
        if curl -f -s http://localhost:$port/ > /dev/null 2>&1; then
            echo -e "  ${GREEN}✅ $container_name (端口$port): 正常${NC}"
        else
            echo -e "  ${RED}❌ $container_name (端口$port): 异常${NC}"
        fi
    done
    
    # 检查负载均衡器
    echo -e "\n${CYAN}⚖️ 负载均衡器:${NC}"
    nginx_port=${NGINX_PORT:-8080}
    if curl -f -s http://localhost:$nginx_port/health > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 负载均衡器(端口$nginx_port): 正常${NC}"
    else
        echo -e "  ${RED}❌ 负载均衡器(端口$nginx_port): 异常${NC}"
    fi
    
    echo -e "${GREEN}✅ 健康检查完成${NC}"
}

# 显示访问信息
show_access_info() {
    echo -e "${GREEN}🎉 FunASR实时转写服务部署成功！${NC}"
    echo ""
    echo -e "${CYAN}🌐 服务访问地址:${NC}"
    echo "  🔗 负载均衡器:     http://localhost:${NGINX_PORT:-8080}"
    echo ""
    echo -e "${CYAN}📁 HTTP文件识别服务:${NC}"
    
    http_base_port=${HTTP_BASE_PORT:-10095}
    gpu_count=${GPU_COUNT:-4}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((http_base_port + i))
        echo "  📍 HTTP GPU-$i:     http://localhost:$port"
    done
    
    echo ""
    echo -e "${CYAN}🌐 WebSocket实时转写服务:${NC}"
    
    wss_base_port=${WSS_BASE_PORT:-10195}
    
    for i in $(seq 0 $((gpu_count-1))); do
        port=$((wss_base_port + i))
        echo "  📍 WebSocket GPU-$i: ws://localhost:$port"
    done
    
    echo ""
    echo -e "${CYAN}🧪 测试命令:${NC}"
    echo "  # HTTP文件识别测试"
    echo "  curl -X POST -F 'file=@test.wav' http://localhost:${NGINX_PORT:-8080}/fileASR"
    echo ""
    echo "  # WebSocket实时转写测试"
    echo "  # 使用clients/目录下的客户端示例"
    echo "  python3 clients/python_client.py"
    echo ""
    echo "  # 健康检查"
    echo "  curl http://localhost:${NGINX_PORT:-8080}/health"
    echo "  curl http://localhost:${NGINX_PORT:-8080}/cluster/health"
    echo ""
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "  查看状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f"
    echo "  重启服务: docker-compose restart"
    echo "  停止服务: docker-compose down"
    echo ""
    echo -e "${CYAN}📊 架构特点:${NC}"
    echo "  🏗️ 架构: HTTP文件识别 + WebSocket实时转写"
    echo "  ⚡ 实例: ${GPU_COUNT:-4}个GPU × 2种服务 = 8个容器"
    echo "  🔄 负载均衡: HTTP轮询 + WebSocket粘性会话"
    echo "  🛡️ 故障隔离: GPU级别隔离"
    echo "  📊 完整功能: 文件上传 + 实时流式识别"
    echo ""
    echo -e "${GREEN}✨ FunASR实时转写集群已就绪！${NC}"
}

# 主函数
main() {
    case $1 in
        "deploy")
            load_config
            check_environment
            pull_official_image
            generate_configs
            deploy_services
            wait_for_services
            health_check
            show_access_info
            ;;
        "check")
            load_config
            health_check
            ;;
        "stop")
            docker-compose -f docker-compose.generated.yml down
            echo -e "${GREEN}✅ 服务已停止${NC}"
            ;;
        "clean")
            docker-compose -f docker-compose.generated.yml down -v
            docker system prune -f
            echo -e "${GREEN}✅ 环境已清理${NC}"
            ;;
        "logs")
            docker-compose -f docker-compose.generated.yml logs -f
            ;;
        "restart")
            docker-compose -f docker-compose.generated.yml restart
            echo -e "${GREEN}✅ 服务已重启${NC}"
            ;;
        *)
            echo "用法: $0 {deploy|check|stop|clean|logs|restart}"
            echo ""
            echo "  deploy   - 部署FunASR实时转写服务"
            echo "  check    - 健康检查"
            echo "  stop     - 停止服务"
            echo "  clean    - 清理环境"
            echo "  logs     - 查看日志"
            echo "  restart  - 重启服务"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
