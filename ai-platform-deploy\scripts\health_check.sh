#!/bin/bash

# AI服务平台健康检查脚本

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "🔍 AI服务平台健康检查"
echo "===================="

# 检查服务列表
services=(
    "http://localhost:8000/health:AI网关:gateway"
    "http://localhost:8002/health:TTS服务:tts-service"
    "http://localhost:8100/health:教育平台:education-app"
    "http://localhost:8200/health:医疗平台:medical-app"
    "http://localhost:6379:Redis缓存:redis"
)

all_healthy=true

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-2)
    name=$(echo $service | cut -d: -f3)
    container=$(echo $service | cut -d: -f4)
    
    # 检查容器状态
    if docker-compose ps $container | grep -q "Up"; then
        container_status="运行中"
    else
        container_status="已停止"
        all_healthy=false
    fi
    
    # 检查HTTP健康检查（除了Redis）
    if [[ $url == *"6379"* ]]; then
        # Redis检查
        if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
            health_status="健康"
        else
            health_status="异常"
            all_healthy=false
        fi
    else
        # HTTP服务检查
        if curl -f -s $url > /dev/null 2>&1; then
            health_status="健康"
        else
            health_status="异常"
            all_healthy=false
        fi
    fi
    
    # 显示结果
    if [[ $health_status == "健康" && $container_status == "运行中" ]]; then
        echo -e "${GREEN}✅ $name: $container_status, $health_status${NC}"
    else
        echo -e "${RED}❌ $name: $container_status, $health_status${NC}"
    fi
done

echo ""
echo "📊 详细状态："
docker-compose ps

echo ""
if [ "$all_healthy" = true ]; then
    echo -e "${GREEN}🎉 所有服务运行正常！${NC}"
    exit 0
else
    echo -e "${RED}⚠️  部分服务存在问题，请检查日志${NC}"
    echo "查看日志命令: docker-compose logs -f [service-name]"
    exit 1
fi
