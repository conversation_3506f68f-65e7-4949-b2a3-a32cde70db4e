from fastapi import APIRouter
from app.services.practise_score_service import PractiseScoreService
from app.models.practise_score_model import GradingRequest, GradingResponse

router = APIRouter()
service = PractiseScoreService()


@router.post("/llm/matrix/practise/score", response_model=GradingResponse)
async def practise_score(request: GradingRequest):
    return await service.practise_score(request.question, request.answer)
