import json
import re
import logging
from logging.handlers import TimedRotatingFileHandler

from fastapi.responses import JSONResponse

class AbsService:
    def __init__(self):
        handler = TimedRotatingFileHandler(
            filename="app.log",
            when="midnight",  # 每天午夜轮转
            interval=1,
            backupCount=15,  # 保留最近 7 天的日志
            encoding="utf-8"
        )
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            self.logger.addHandler(handler)



    def safe_json_parse(self, raw_response: str) -> dict:
        """安全解析JSON响应"""
        # 清理响应中的常见非JSON内容
        clean_response = re.sub(r"^[^{]*", "", raw_response)  # 去除JSON前的文本
        clean_response = re.sub(r"[^}]*$", "", clean_response)  # 去除JSON后的文本
        clean_response = clean_response.replace("，", ",")  # 中文逗号替换
        clean_response = clean_response.replace("‘", "'").replace("’", "'")  # 引号标准化
        # 去掉\n连同后面的空格
        clean_response = clean_response.replace("\n", "")

        try:
            return json.loads(clean_response)
        except json.JSONDecodeError:
            # 尝试修复常见的格式问题
            fixed_response = re.sub(r",(\s*[\]}])", r"\1", clean_response)  # 去除尾部逗号
            fixed_response = re.sub(r"(\w+)\s*:", r'"\1":', fixed_response)  # 自动补双引号
            self.logger.info('fixed_response: ' + fixed_response)
            return json.loads(fixed_response)

    def parase_llm_params(self, llm_params: dict) -> dict:
        """解析LLM参数"""
        model = None if len(llm_params) == 0 else llm_params.get('model', None)
        stream = False if len(llm_params) == 0 else llm_params.get('stream', False)
        options = {} if len(llm_params) == 0 else llm_params.get('options', {})
        return model, stream, options
    
    def wrap_response(self, data_dict: dict={}, status=0, msg='success'):
        return JSONResponse(
            status_code=200,
            content={"data": data_dict, 'status': status, 'msg': msg}
        )