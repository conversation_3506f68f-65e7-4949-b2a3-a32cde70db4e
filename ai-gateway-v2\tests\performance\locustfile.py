"""
性能测试脚本
使用Locust进行百万级并发测试
"""

import json
import random
import time
from locust import HttpUser, task, between, events
from locust.runners import MasterRunner


class AIGatewayUser(HttpUser):
    """AI网关用户模拟"""
    
    wait_time = between(0.1, 2.0)  # 请求间隔
    
    def on_start(self):
        """用户开始时的初始化"""
        self.client.verify = False  # 忽略SSL验证
        
        # 测试数据
        self.test_images = [
            "https://example.com/test1.jpg",
            "https://example.com/test2.jpg",
            "https://example.com/test3.jpg"
        ]
        
        self.test_texts = [
            "Hello, this is a test text for TTS.",
            "人工智能技术正在快速发展。",
            "Machine learning is transforming industries."
        ]
        
        self.test_prompts = [
            "What is artificial intelligence?",
            "Explain machine learning in simple terms.",
            "How does deep learning work?"
        ]
    
    @task(3)
    def test_health_check(self):
        """健康检查测试 - 高频率"""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(2)
    def test_ocr_service(self):
        """OCR服务测试"""
        payload = {
            "model": "paddle_ocr",
            "input": {
                "image_url": random.choice(self.test_images)
            },
            "options": {
                "language": "zh-en"
            }
        }
        
        with self.client.post(
            "/api/v1/ocr/process",
            json=payload,
            catch_response=True,
            name="OCR Process"
        ) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("success"):
                        response.success()
                    else:
                        response.failure("OCR processing failed")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"OCR request failed: {response.status_code}")
    
    @task(2)
    def test_tts_service(self):
        """TTS服务测试"""
        payload = {
            "model": "edge_tts",
            "input": {
                "text": random.choice(self.test_texts),
                "voice": "zh-CN-XiaoxiaoNeural"
            },
            "options": {
                "speed": 1.0,
                "pitch": 0
            }
        }
        
        with self.client.post(
            "/api/v1/tts/process",
            json=payload,
            catch_response=True,
            name="TTS Process"
        ) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("success"):
                        response.success()
                    else:
                        response.failure("TTS processing failed")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"TTS request failed: {response.status_code}")
    
    @task(1)
    def test_llm_service(self):
        """LLM服务测试"""
        payload = {
            "model": "gpt-3.5-turbo",
            "input": {
                "messages": [
                    {"role": "user", "content": random.choice(self.test_prompts)}
                ]
            },
            "options": {
                "temperature": 0.7,
                "max_tokens": 100
            }
        }
        
        with self.client.post(
            "/api/v1/llm/process",
            json=payload,
            catch_response=True,
            name="LLM Process"
        ) as response:
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("success"):
                        response.success()
                    else:
                        response.failure("LLM processing failed")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"LLM request failed: {response.status_code}")
    
    @task(1)
    def test_cache_performance(self):
        """缓存性能测试"""
        # 重复请求相同数据测试缓存
        payload = {
            "model": "paddle_ocr",
            "input": {
                "image_url": self.test_images[0]  # 固定使用第一张图片
            }
        }
        
        start_time = time.time()
        with self.client.post(
            "/api/v1/ocr/process",
            json=payload,
            catch_response=True,
            name="Cache Test"
        ) as response:
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    cache_hit = result.get("metadata", {}).get("cache_hit", False)
                    
                    # 缓存命中应该很快
                    if cache_hit and response_time < 0.1:
                        response.success()
                    elif not cache_hit and response_time < 2.0:
                        response.success()
                    else:
                        response.failure(f"Response too slow: {response_time:.3f}s")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Cache test failed: {response.status_code}")


class HighConcurrencyUser(HttpUser):
    """高并发用户模拟"""
    
    wait_time = between(0.01, 0.1)  # 极短间隔
    
    @task
    def rapid_health_check(self):
        """快速健康检查"""
        self.client.get("/health")


class StressTestUser(HttpUser):
    """压力测试用户"""
    
    wait_time = between(0, 0.05)  # 几乎无间隔
    
    @task(5)
    def stress_health(self):
        """压力测试 - 健康检查"""
        self.client.get("/health")
    
    @task(1)
    def stress_ocr(self):
        """压力测试 - OCR"""
        payload = {
            "model": "paddle_ocr",
            "input": {"image_url": "https://example.com/test.jpg"}
        }
        self.client.post("/api/v1/ocr/process", json=payload)


# 性能监控事件
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """请求监控"""
    if exception:
        print(f"Request failed: {name} - {exception}")
    elif response_time > 1000:  # 超过1秒的请求
        print(f"Slow request: {name} - {response_time:.0f}ms")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始"""
    print("🚀 Performance test started")
    print(f"Target host: {environment.host}")
    
    if isinstance(environment.runner, MasterRunner):
        print(f"Running in distributed mode with {environment.runner.worker_count} workers")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束"""
    print("🏁 Performance test completed")
    
    # 打印统计信息
    stats = environment.runner.stats
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Total failures: {stats.total.num_failures}")
    print(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    print(f"Max response time: {stats.total.max_response_time:.2f}ms")
    print(f"Requests per second: {stats.total.current_rps:.2f}")
    
    # 性能目标检查
    if stats.total.avg_response_time > 100:
        print("⚠️  Warning: Average response time exceeds 100ms")
    
    if stats.total.num_failures / stats.total.num_requests > 0.01:
        print("⚠️  Warning: Failure rate exceeds 1%")
    
    if stats.total.current_rps < 1000:
        print("⚠️  Warning: RPS below target (1000)")


# 自定义测试场景
class WebsiteUser(HttpUser):
    """网站用户行为模拟"""
    
    wait_time = between(1, 5)
    
    def on_start(self):
        """模拟用户登录"""
        pass
    
    @task(3)
    def browse_services(self):
        """浏览服务"""
        self.client.get("/api/v1/ocr/models")
        self.client.get("/api/v1/tts/models")
    
    @task(2)
    def use_ocr(self):
        """使用OCR服务"""
        payload = {
            "model": "paddle_ocr",
            "input": {"image_url": "https://example.com/document.jpg"}
        }
        self.client.post("/api/v1/ocr/process", json=payload)
    
    @task(1)
    def check_stats(self):
        """查看统计"""
        self.client.get("/api/v1/ocr/stats")


# 运行命令示例:
# locust -f locustfile.py --host=http://localhost:8000
# locust -f locustfile.py --host=http://localhost:8000 --users=1000 --spawn-rate=10 -t 300s
# locust -f locustfile.py --host=http://localhost:8000 --headless --users=10000 --spawn-rate=100 -t 600s
