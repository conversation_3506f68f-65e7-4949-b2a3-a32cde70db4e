version: '3.8'

# 测试环境配置 - 适用于3台服务器
# 普通服务器运行所有业务服务，算力服务器运行AI模型

services:
  # AI服务网关 (普通服务器)
  gateway:
    image: yourorg/ai-gateway:latest
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      # 配置算力服务器地址
      - TTS_BACKENDS=http://192.168.1.100:8080,http://192.168.1.101:8080
      - LLM_BACKENDS=http://192.168.1.100:8081,http://192.168.1.101:8081
      - OCR_BACKENDS=http://192.168.1.100:8082
      - ASR_BACKENDS=http://192.168.1.101:8082
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # TTS微服务 (普通服务器) - Gunicorn多进程
  tts-service:
    image: yourorg/ai-tts-service:latest
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - TTS_BACKENDS=http://192.168.1.100:8080,http://192.168.1.101:8080
      - DEBUG=true
      - ENVIRONMENT=production
      # Gunicorn配置
      - WORKERS=4                    # 4个工作进程
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=1000      # 每进程1000连接
      - MAX_REQUESTS=1000           # 每进程最大请求数
      - TIMEOUT=300                 # 请求超时时间
      - LOG_LEVEL=info
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    volumes:
      - tts_logs:/app/logs          # 日志持久化
    deploy:
      resources:
        limits:
          cpus: '4.0'               # 增加CPU限制
          memory: 8G                # 增加内存限制
        reservations:
          cpus: '2.0'
          memory: 4G

  # 教育平台 (普通服务器)
  education-app:
    image: yourorg/ai-education-app:latest
    ports:
      - "8100:8100"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
      - DEBUG=true
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # 医疗平台 (普通服务器)
  medical-app:
    image: yourorg/ai-medical-app:latest
    ports:
      - "8200:8200"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
      - DEBUG=true
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Redis缓存 (普通服务器)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  # Prometheus监控 (普通服务器)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=7d'  # 测试环境保留7天
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Grafana监控面板 (普通服务器)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  # Nginx负载均衡器 (普通服务器)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.test.conf:/etc/nginx/nginx.conf
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # TTS微服务实例2 (可选，进一步提高并发)
  tts-service-2:
    image: yourorg/ai-tts-service:latest
    ports:
      - "8003:8002"                 # 使用不同端口
    environment:
      - REDIS_URL=redis://redis:6379
      - TTS_BACKENDS=http://192.168.1.100:8080,http://192.168.1.101:8080
      - DEBUG=true
      - ENVIRONMENT=production
      - WORKERS=4
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=1000
      - MAX_REQUESTS=1000
      - TIMEOUT=300
      - LOG_LEVEL=info
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    volumes:
      - tts_logs_2:/app/logs
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G
    profiles:
      - multi-instance              # 使用profile控制是否启动

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
  tts_logs:
  tts_logs_2:

networks:
  ai-platform:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
