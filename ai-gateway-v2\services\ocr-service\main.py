"""
OCR微服务
高性能光学字符识别服务
"""

import asyncio
import time
import uuid
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from shared.cache import cache_manager
from shared.monitoring import MetricsCollector
from shared.service_discovery import ServiceDiscovery
from core.ocr_processor import OCRProcessor
from core.load_balancer import LoadBalancer


# 请求模型
class OCRRequest(BaseModel):
    """OCR请求模型"""
    model: str = Field(default="paddle_ocr", description="OCR模型")
    input: Dict[str, Any] = Field(..., description="输入数据")
    options: Dict[str, Any] = Field(default_factory=dict, description="选项参数")


class OCRResponse(BaseModel):
    """OCR响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Dict[str, Any] = Field(..., description="识别结果")
    metadata: Dict[str, Any] = Field(..., description="元数据")


# 创建应用
app = FastAPI(
    title="OCR Service",
    description="高性能光学字符识别微服务",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化组件
ocr_processor = OCRProcessor()
load_balancer = LoadBalancer("ocr")
metrics_collector = MetricsCollector()
service_discovery = ServiceDiscovery()


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    print("🚀 OCR服务启动中...")
    
    # 初始化缓存
    await cache_manager.initialize()
    
    # 初始化处理器
    await ocr_processor.initialize()
    
    # 注册服务
    await service_discovery.register_service(
        name="ocr-service",
        host="localhost",
        port=8001,
        health_check_url="/health"
    )
    
    print("✅ OCR服务启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    print("🛑 OCR服务关闭中...")
    
    # 注销服务
    await service_discovery.deregister_service("ocr-service")
    
    # 清理资源
    await cache_manager.close()
    await ocr_processor.cleanup()
    
    print("✅ OCR服务关闭完成")


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "ocr-service",
        "version": "2.0.0",
        "timestamp": time.time()
    }


@app.post("/process", response_model=OCRResponse)
async def process_ocr(
    request: OCRRequest,
    background_tasks: BackgroundTasks
):
    """处理OCR请求"""
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # 生成缓存键
        cache_key = f"ocr:{request.model}:{hash(str(request.input))}"
        
        # 检查缓存
        cached_result = await cache_manager.get(cache_key, "ocr")
        if cached_result:
            # 记录缓存命中
            metrics_collector.record_cache_hit("ocr")
            
            return OCRResponse(
                success=True,
                data=cached_result,
                metadata={
                    "request_id": request_id,
                    "processing_time": time.time() - start_time,
                    "model_used": request.model,
                    "cache_hit": True
                }
            )
        
        # 缓存未命中，处理请求
        metrics_collector.record_cache_miss("ocr")
        
        # 选择后端服务
        backend_url = await load_balancer.get_backend()
        if not backend_url:
            raise HTTPException(status_code=503, detail="No available backend services")
        
        # 处理OCR
        result = await ocr_processor.process(
            model=request.model,
            input_data=request.input,
            options=request.options,
            backend_url=backend_url
        )
        
        # 异步缓存结果
        background_tasks.add_task(
            cache_manager.set,
            cache_key,
            result,
            ttl=3600,  # 1小时
            service="ocr"
        )
        
        # 记录指标
        processing_time = time.time() - start_time
        metrics_collector.record_request("ocr", processing_time, True)
        
        return OCRResponse(
            success=True,
            data=result,
            metadata={
                "request_id": request_id,
                "processing_time": processing_time,
                "model_used": request.model,
                "cache_hit": False,
                "backend_used": backend_url
            }
        )
        
    except Exception as e:
        # 记录错误
        processing_time = time.time() - start_time
        metrics_collector.record_request("ocr", processing_time, False)
        
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "request_id": request_id,
                "processing_time": processing_time
            }
        )


@app.get("/models")
async def get_available_models():
    """获取可用模型列表"""
    return {
        "models": [
            {
                "name": "paddle_ocr",
                "description": "PaddleOCR通用文字识别",
                "languages": ["zh", "en"],
                "features": ["text_detection", "text_recognition"]
            },
            {
                "name": "tesseract",
                "description": "Tesseract OCR引擎",
                "languages": ["zh", "en", "fr", "de"],
                "features": ["text_recognition"]
            },
            {
                "name": "easyocr",
                "description": "EasyOCR多语言识别",
                "languages": ["zh", "en", "ja", "ko"],
                "features": ["text_detection", "text_recognition"]
            }
        ]
    }


@app.get("/stats")
async def get_service_stats():
    """获取服务统计"""
    cache_stats = await cache_manager.get_stats()
    processor_stats = await ocr_processor.get_stats()
    load_balancer_stats = await load_balancer.get_stats()
    
    return {
        "service": "ocr-service",
        "cache": cache_stats.get("ocr", {}),
        "processor": processor_stats,
        "load_balancer": load_balancer_stats,
        "uptime": time.time() - app.state.start_time if hasattr(app.state, 'start_time') else 0
    }


@app.post("/warm-up")
async def warm_up_cache(warm_up_data: List[Dict[str, Any]]):
    """缓存预热"""
    await cache_manager.warm_up("ocr", warm_up_data)
    return {"message": f"预热完成，处理了 {len(warm_up_data)} 项数据"}


@app.delete("/cache")
async def clear_cache():
    """清除缓存"""
    pattern = "ocr:*"
    deleted = await cache_manager.clear_pattern(pattern, "ocr")
    return {"message": f"清除了 {deleted} 项缓存"}


# 记录启动时间
@app.middleware("http")
async def add_startup_time(request, call_next):
    if not hasattr(app.state, 'start_time'):
        app.state.start_time = time.time()
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        workers=1
    )
