global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # AI网关监控
  - job_name: 'ai-gateway'
    static_configs:
      - targets: ['gateway:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # TTS多节点监控
  - job_name: 'tts-node-1'
    static_configs:
      - targets: ['tts-node-1:8002']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 8s
    params:
      node: ['tts-node-1']

  - job_name: 'tts-node-2'
    static_configs:
      - targets: ['tts-node-2:8002']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 8s
    params:
      node: ['tts-node-2']

  - job_name: 'tts-node-3'
    static_configs:
      - targets: ['tts-node-3:8002']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 8s
    params:
      node: ['tts-node-3']

  # TTS负载均衡器监控
  - job_name: 'tts-load-balancer'
    static_configs:
      - targets: ['nginx-tts-lb:80']
    metrics_path: '/nginx_status'
    scrape_interval: 15s

  # ASR服务监控
  - job_name: 'asr-service'
    static_configs:
      - targets: ['asr-service:8003']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s
    params:
      service: ['asr-service']

  # 教育平台监控
  - job_name: 'education-app'
    static_configs:
      - targets: ['education-app:8100']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 医疗平台监控
  - job_name: 'medical-app'
    static_configs:
      - targets: ['medical-app:8200']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 算力服务器监控 (需要在算力服务器上部署node_exporter)
  - job_name: 'compute-servers'
    static_configs:
      - targets: ['192.168.1.100:9100', '192.168.1.101:9100']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # TTS后端模型服务监控
  - job_name: 'tts-backends'
    static_configs:
      - targets: ['192.168.1.100:8080', '192.168.1.101:8080']
    metrics_path: '/metrics'
    scrape_interval: 20s
    scrape_timeout: 15s

  # LLM后端模型服务监控
  - job_name: 'llm-backends'
    static_configs:
      - targets: ['192.168.1.100:8081', '192.168.1.101:8081']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 20s
