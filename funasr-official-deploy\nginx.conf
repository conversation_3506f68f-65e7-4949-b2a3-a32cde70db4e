events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format funasr_official '$remote_addr - $remote_user [$time_local] '
                               '"$request" $status $body_bytes_sent '
                               '"$http_referer" "$http_user_agent" '
                               'rt=$request_time uct="$upstream_connect_time" '
                               'uht="$upstream_header_time" urt="$upstream_response_time" '
                               'upstream="$upstream_addr"';
    
    access_log /var/log/nginx/funasr_access.log funasr_official;
    error_log  /var/log/nginx/funasr_error.log warn;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;  # 支持大音频文件
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        application/json
        application/javascript
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # FunASR官方集群配置
    upstream funasr_official_cluster {
        # 轮询负载均衡
        server funasr-gpu-0:10095 weight=1 max_fails=3 fail_timeout=30s;
        server funasr-gpu-1:10095 weight=1 max_fails=3 fail_timeout=30s;
        server funasr-gpu-2:10095 weight=1 max_fails=3 fail_timeout=30s;
        server funasr-gpu-3:10095 weight=1 max_fails=3 fail_timeout=30s;
        
        # 连接保持
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }
    
    # 健康检查上游
    upstream funasr_health {
        server funasr-gpu-0:10095;
        server funasr-gpu-1:10095;
        server funasr-gpu-2:10095;
        server funasr-gpu-3:10095;
    }
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=funasr_limit:10m rate=100r/s;
    limit_conn_zone $binary_remote_addr zone=funasr_conn:10m;
    
    # 主服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 基本配置
        client_body_timeout 300s;
        client_header_timeout 60s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 限流限连
        limit_req zone=funasr_limit burst=200 nodelay;
        limit_conn funasr_conn 50;
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "FunASR Official Load Balancer OK\n";
            add_header Content-Type text/plain;
        }
        
        # 集群健康检查
        location /cluster/health {
            proxy_pass http://funasr_health/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # FunASR官方文件ASR接口
        location /fileASR {
            proxy_pass http://funasr_official_cluster;
            
            # 请求头设置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-funasr-official-lb";
            
            # 超时配置 (ASR需要较长时间)
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # 缓冲配置
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            proxy_temp_file_write_size 64k;
            
            # 连接保持
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # 错误处理
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }
        
        # 兼容接口 - 映射到官方fileASR
        location /llm/asr/recognition {
            proxy_pass http://funasr_official_cluster/fileASR;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }
        
        # Nginx状态页面
        location /nginx_status {
            stub_status on;
            access_log off;
            allow **********/16;  # 仅允许内网访问
            deny all;
        }
        
        # 默认路由
        location / {
            proxy_pass http://funasr_official_cluster;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
    
    # 直接访问各个实例的配置 (调试用)
    server {
        listen 8001;
        server_name localhost;
        
        location / {
            proxy_pass http://funasr-gpu-0:10095;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-gpu-0";
        }
    }
    
    server {
        listen 8002;
        server_name localhost;
        
        location / {
            proxy_pass http://funasr-gpu-1:10095;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-gpu-1";
        }
    }
    
    server {
        listen 8003;
        server_name localhost;
        
        location / {
            proxy_pass http://funasr-gpu-2:10095;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-gpu-2";
        }
    }
    
    server {
        listen 8004;
        server_name localhost;
        
        location / {
            proxy_pass http://funasr-gpu-3:10095;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-gpu-3";
        }
    }
}
