"""
TTS微服务
文本转语音服务，支持多种语音模型和说话人
"""

import asyncio
import time
import uuid
import os
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
import aioredis


# 请求响应模型
class QuerySpeakersRequest(BaseModel):
    """查询语音列表请求"""
    gender: Optional[str] = Field(None, description="性别筛选: Male, Female")
    model: str = Field(default="edge", description="TTS模型")
    language: Optional[str] = Field(None, description="语言筛选")


class TTSRequest(BaseModel):
    """TTS请求模型"""
    text: str = Field(..., description="要转换的文本")
    speaker: str = Field(default="zh-CN-XiaoxiaoNeural", description="说话人")
    model: str = Field(default="edge", description="TTS模型")
    stream: bool = Field(default=False, description="是否流式返回")
    speed: Optional[float] = Field(default=1.0, description="语速")
    pitch: Optional[float] = Field(default=0.0, description="音调")
    volume: Optional[float] = Field(default=1.0, description="音量")


class TTSResponse(BaseModel):
    """TTS响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Dict[str, Any] = Field(..., description="返回数据")
    metadata: Dict[str, Any] = Field(..., description="元数据")


# 创建应用
app = FastAPI(
    title="TTS Service",
    description="文本转语音微服务",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
redis_client = None
http_client = httpx.AsyncClient(timeout=60)

# TTS后端配置
TTS_BACKENDS = [
    {
        "name": "matrix_tts",
        "base_url": "https://t-matrix.nibs.ac.cn/mko",
        "api_key": "sk-vAc55vL1pr9VJT2jv1JE5ANzdlexoAjd",
        "weight": 1,
        "status": "active"
    }
    # 可以添加更多后端
]

# 缓存的语音列表
CACHED_SPEAKERS = {}


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global redis_client
    
    print("🚀 TTS服务启动中...")
    
    # 初始化Redis
    try:
        redis_client = aioredis.from_url("redis://redis:6379")
        await redis_client.ping()
        print("✅ Redis连接成功")
    except:
        print("⚠️ Redis连接失败，缓存功能不可用")
        redis_client = None
    
    # 预加载语音列表
    await preload_speakers()
    
    print("✅ TTS服务启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    await http_client.aclose()
    if redis_client:
        await redis_client.close()
    print("✅ TTS服务关闭完成")


async def preload_speakers():
    """预加载语音列表"""
    try:
        print("📋 预加载语音列表...")
        
        # 获取所有语音
        speakers = await query_speakers_from_backend()
        if speakers:
            CACHED_SPEAKERS["all"] = speakers
            print(f"✅ 预加载了 {len(speakers)} 个语音")
        else:
            print("⚠️ 语音列表预加载失败")
            
    except Exception as e:
        print(f"❌ 预加载语音列表失败: {e}")


def select_backend() -> Dict[str, Any]:
    """选择可用的后端"""
    active_backends = [b for b in TTS_BACKENDS if b["status"] == "active"]
    if not active_backends:
        raise HTTPException(status_code=503, detail="No TTS backend available")
    
    # 简单选择第一个可用后端
    return active_backends[0]


async def get_cache(key: str) -> Optional[Any]:
    """获取缓存"""
    if not redis_client:
        return None
    
    try:
        data = await redis_client.get(key)
        if data:
            import json
            return json.loads(data)
    except:
        pass
    return None


async def set_cache(key: str, data: Any, ttl: int = 3600):
    """设置缓存"""
    if not redis_client:
        return
    
    try:
        import json
        await redis_client.setex(key, ttl, json.dumps(data))
    except:
        pass


async def query_speakers_from_backend(
    gender: Optional[str] = None,
    model: str = "edge",
    language: Optional[str] = None
) -> List[Dict[str, Any]]:
    """从后端查询语音列表"""
    backend = select_backend()
    
    # 构建请求
    payload = {"model": model}
    if gender:
        payload["gender"] = gender
    if language:
        payload["language"] = language
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": backend["api_key"]
    }
    
    try:
        response = await http_client.post(
            f"{backend['base_url']}/llm/tts/query_speakers",
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        result = response.json()
        return result.get("speakers", result.get("data", []))
        
    except Exception as e:
        print(f"查询语音列表失败: {e}")
        return []


async def text_to_speech_backend(
    text: str,
    speaker: str,
    model: str = "edge",
    stream: bool = False,
    **options
) -> Dict[str, Any]:
    """调用后端TTS服务"""
    backend = select_backend()
    
    # 构建请求
    payload = {
        "text": text,
        "speaker": speaker,
        "stream": stream
    }
    
    # 添加可选参数
    if options.get("speed"):
        payload["speed"] = options["speed"]
    if options.get("pitch"):
        payload["pitch"] = options["pitch"]
    if options.get("volume"):
        payload["volume"] = options["volume"]
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": backend["api_key"]
    }
    
    try:
        response = await http_client.post(
            f"{backend['base_url']}/llm/tts/text_to_speech",
            json=payload,
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"TTS服务调用失败: {str(e)}")


@app.get("/health")
async def health_check():
    """健康检查"""
    backend_status = []
    for backend in TTS_BACKENDS:
        try:
            # 简单的连通性测试
            response = await http_client.get(
                f"{backend['base_url']}/health",
                timeout=5
            )
            status = "healthy" if response.status_code == 200 else "unhealthy"
        except:
            status = "unhealthy"
        
        backend_status.append({
            "name": backend["name"],
            "status": status,
            "url": backend["base_url"]
        })
    
    return {
        "status": "healthy",
        "service": "tts-service",
        "version": "2.0.0",
        "timestamp": time.time(),
        "backends": backend_status,
        "cached_speakers": len(CACHED_SPEAKERS.get("all", []))
    }


@app.post("/query_speakers")
async def query_speakers(request: QuerySpeakersRequest):
    """查询可用语音列表"""
    try:
        # 生成缓存键
        cache_key = f"speakers:{request.model}:{request.gender}:{request.language}"
        
        # 检查缓存
        cached_speakers = await get_cache(cache_key)
        if cached_speakers:
            return {
                "success": True,
                "speakers": cached_speakers,
                "metadata": {
                    "cache_hit": True,
                    "count": len(cached_speakers)
                }
            }
        
        # 从后端查询
        speakers = await query_speakers_from_backend(
            gender=request.gender,
            model=request.model,
            language=request.language
        )
        
        # 缓存结果
        await set_cache(cache_key, speakers, ttl=86400)  # 缓存24小时
        
        return {
            "success": True,
            "speakers": speakers,
            "metadata": {
                "cache_hit": False,
                "count": len(speakers),
                "model": request.model
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/text_to_speech", response_model=TTSResponse)
async def text_to_speech(
    request: TTSRequest,
    background_tasks: BackgroundTasks
):
    """文本转语音"""
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    try:
        # 生成缓存键
        cache_key = f"tts:{hash(str(request.dict()))}"
        
        # 检查缓存（非流式请求才缓存）
        if not request.stream:
            cached_result = await get_cache(cache_key)
            if cached_result:
                return TTSResponse(
                    success=True,
                    data=cached_result,
                    metadata={
                        "request_id": request_id,
                        "processing_time": time.time() - start_time,
                        "cache_hit": True,
                        "speaker": request.speaker
                    }
                )
        
        # 调用后端服务
        result = await text_to_speech_backend(
            text=request.text,
            speaker=request.speaker,
            model=request.model,
            stream=request.stream,
            speed=request.speed,
            pitch=request.pitch,
            volume=request.volume
        )
        
        # 异步缓存结果（非流式）
        if not request.stream:
            background_tasks.add_task(
                set_cache,
                cache_key,
                result,
                7200  # 缓存2小时
            )
        
        processing_time = time.time() - start_time
        
        return TTSResponse(
            success=True,
            data=result,
            metadata={
                "request_id": request_id,
                "processing_time": processing_time,
                "cache_hit": False,
                "speaker": request.speaker,
                "text_length": len(request.text),
                "stream": request.stream
            }
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "request_id": request_id,
                "processing_time": processing_time
            }
        )


@app.post("/process")
async def process_tts(request: dict):
    """统一处理接口（兼容网关调用）"""
    try:
        # 解析请求
        text = request.get("text")
        speaker = request.get("speaker", request.get("voice", "zh-CN-XiaoxiaoNeural"))
        options = request.get("options", {})
        
        if not text:
            raise HTTPException(status_code=400, detail="Missing text parameter")
        
        # 构建TTS请求
        tts_request = TTSRequest(
            text=text,
            speaker=speaker,
            speed=options.get("speed", 1.0),
            pitch=options.get("pitch", 0.0),
            volume=options.get("volume", 1.0),
            stream=options.get("stream", False)
        )
        
        # 调用TTS服务
        result = await text_to_speech(tts_request, BackgroundTasks())
        
        return {
            "success": result.success,
            "data": result.data,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/speakers")
async def get_speakers(
    gender: Optional[str] = None,
    language: Optional[str] = None,
    model: str = "edge"
):
    """获取语音列表（简化接口）"""
    request = QuerySpeakersRequest(
        gender=gender,
        language=language,
        model=model
    )
    return await query_speakers(request)


@app.get("/stats")
async def get_service_stats():
    """获取服务统计"""
    return {
        "service": "tts-service",
        "version": "2.0.0",
        "uptime": time.time() - getattr(app.state, 'start_time', time.time()),
        "backends": len(TTS_BACKENDS),
        "cached_speakers": len(CACHED_SPEAKERS.get("all", [])),
        "supported_models": ["edge"],
        "features": [
            "text_to_speech",
            "speaker_query",
            "voice_customization",
            "streaming_support"
        ]
    }


# 记录启动时间
@app.middleware("http")
async def add_startup_time(request, call_next):
    if not hasattr(app.state, 'start_time'):
        app.state.start_time = time.time()
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=True
    )
