"""
TTS微服务Gunicorn配置文件
支持多进程部署和性能优化
"""

import multiprocessing
import os

# 服务器配置
bind = "0.0.0.0:8002"
backlog = 2048

# 工作进程配置
workers = int(os.getenv("WORKERS", multiprocessing.cpu_count() * 2 + 1))
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = int(os.getenv("WORKER_CONNECTIONS", 1000))
max_requests = int(os.getenv("MAX_REQUESTS", 1000))
max_requests_jitter = int(os.getenv("MAX_REQUESTS_JITTER", 100))
timeout = int(os.getenv("TIMEOUT", 300))
keepalive = int(os.getenv("KEEPALIVE", 5))

# 性能优化
preload_app = True
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统

# 日志配置
loglevel = os.getenv("LOG_LEVEL", "info")
accesslog = "/app/logs/access.log"
errorlog = "/app/logs/error.log"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程管理
pidfile = "/app/logs/gunicorn.pid"
daemon = False
user = None
group = None
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# SSL配置 (如果需要)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# 钩子函数
def on_starting(server):
    """服务器启动时调用"""
    server.log.info("🚀 TTS微服务启动中...")
    server.log.info(f"工作进程数: {workers}")
    server.log.info(f"工作进程类型: {worker_class}")
    server.log.info(f"绑定地址: {bind}")

def on_reload(server):
    """重新加载时调用"""
    server.log.info("🔄 TTS微服务重新加载")

def when_ready(server):
    """服务器就绪时调用"""
    server.log.info("✅ TTS微服务就绪，开始接受请求")

def worker_int(worker):
    """工作进程收到SIGINT信号时调用"""
    worker.log.info(f"🔄 工作进程 {worker.pid} 收到中断信号")

def pre_fork(server, worker):
    """fork工作进程前调用"""
    server.log.info(f"🔧 准备启动工作进程 {worker.age}")

def post_fork(server, worker):
    """fork工作进程后调用"""
    server.log.info(f"✅ 工作进程 {worker.pid} 启动完成")

def post_worker_init(worker):
    """工作进程初始化完成后调用"""
    worker.log.info(f"🎯 工作进程 {worker.pid} 初始化完成")

def worker_abort(worker):
    """工作进程异常退出时调用"""
    worker.log.error(f"❌ 工作进程 {worker.pid} 异常退出")

def pre_exec(server):
    """exec前调用"""
    server.log.info("🔄 准备重新执行服务器")

def pre_request(worker, req):
    """处理请求前调用"""
    # 可以在这里添加请求预处理逻辑
    pass

def post_request(worker, req, environ, resp):
    """处理请求后调用"""
    # 可以在这里添加请求后处理逻辑
    pass

# 自定义配置验证
def validate_config():
    """验证配置参数"""
    if workers < 1:
        raise ValueError("工作进程数必须大于0")
    
    if worker_connections < 1:
        raise ValueError("工作进程连接数必须大于0")
    
    if timeout < 30:
        raise ValueError("超时时间不能少于30秒")

# 执行配置验证
validate_config()

# 环境特定配置
if os.getenv("ENVIRONMENT") == "production":
    # 生产环境配置
    workers = max(workers, 4)  # 生产环境至少4个进程
    loglevel = "warning"
    preload_app = True
elif os.getenv("ENVIRONMENT") == "development":
    # 开发环境配置
    workers = 1
    loglevel = "debug"
    reload = True
    preload_app = False
