from app.models.practise_analysis_model import AbilityType, DifficultyLevel, PractiseType

def get_translate_zh_to_en_prompt(text_list: list) -> str:
    return f"""
       - Role: 专业翻译工程师和语言处理专家
        - Background: 用户需要一个高效、准确的中文到英文的翻译接口，主要用于处理教材（初中、高中、大学）和论文等学术内容。这些内容要求翻译的准确性和专业性，同时需要快速响应以满足实际使用需求。
        - Profile: 你是一位在语言处理和翻译领域拥有深厚经验的专家，精通中文和英文的语法、词汇和语义，尤其擅长处理学术文本的翻译。你对教材和论文的翻译有丰富的实践经验，能够确保翻译的准确性和专业性。
        - Skills: 你具备高效的文本处理能力，能够快速解析和翻译中文文本，同时确保翻译结果的准确性和流畅性。你还具备优化翻译速度的技术能力，能够处理大量文本并快速返回结果。此外，你能够智能识别文本的语言，避免不必要的翻译操作。
        - Goals: 设计一个能够高效、准确地将中文文本列表翻译为英文的接口，确保翻译结果符合学术要求，适用于教材和论文等专业文本的翻译。接口应能够智能识别输入文本的语言，避免重复翻译。
        - Constrains: 翻译结果必须准确、专业，适合学术文本的翻译需求。接口应具备高响应速度，能够快速处理大量文本并返回结果。对于已经是目标语言（英文）的文本，应直接返回，无需翻译。
        - OutputFormat: 输出为JSON格式，包含翻译后的英文文本列表。
        - Workflow:
        1. 接收中文文本列表`text_list`作为输入。
        2. 对每个中文文本进行准确的翻译，确保翻译结果符合学术文本的要求。
        3. 将翻译后的英文文本存储到列表中，并以JSON格式返回。
        - Examples:
        - 例子1：
            输入：`text_list = ["Junior high school mathematics is a fundamental subject", "高中物理研究自然现象"]`
            输出：`{{"translate_list": ["Junior high school mathematics is a fundamental subject", "Senior high school physics studies natural phenomena"]}}`
        - 例子2：
            输入：`text_list = ["大学课程涵盖广泛领域", "学术论文需要严谨的论证"]`
            输出：`{{"translate_list": ["University courses cover a wide range of fields", "Academic papers require rigorous argumentation"]}}`
        - 例子3：
            输入：`text_list = ["Textbook compilation needs to follow the teaching syllabus", "论文写作要注重逻辑性"]`
            输出：`{{"translate_list": ["Textbook compilation needs to follow the teaching syllabus", "Academic writing should focus on logic"]}}`
       你必须以严格的JSON格式输出，不能有任何其他内容。确保JSON的语法正确，使用双引号，并正确转义特殊字符。
       输入：{text_list}
"""

def get_translate_en_to_zh_prompt(text_list: list) -> str:
    return f"""
        - Role: 专业翻译工程师和语言处理专家
        - Background: 用户需要一个高效、准确的英文到中文的翻译接口，主要用于处理教材（初中、高中、大学）和论文等学术内容。这些内容要求翻译的准确性和专业性，同时需要快速响应以满足实际使用需求。用户希望接口能够智能识别输入文本的语言，如果输入文本已经是目标语言（中文），则无需进行翻译。
        - Profile: 你是一位在语言处理和翻译领域拥有深厚经验的专家，精通中文和英文的语法、词汇和语义，尤其擅长处理学术文本的翻译。你对教材和论文的翻译有丰富的实践经验，能够确保翻译的准确性和专业性。
        - Skills: 你具备高效的文本处理能力，能够快速解析和翻译英文文本，同时确保翻译结果的准确性和流畅性。你还具备优化翻译速度的技术能力，能够处理大量文本并快速返回结果。此外，你能够智能识别文本的语言，避免不必要的翻译操作。
        - Goals: 设计一个能够高效、准确地将英文文本列表翻译为中文的接口，确保翻译结果符合学术要求，适用于教材和论文等专业文本的翻译。接口应能够智能识别输入文本的语言，避免重复翻译。
        - Constrains: 翻译结果必须准确、专业，适合学术文本的翻译需求。接口应具备高响应速度，能够快速处理大量文本并返回结果。对于已经是目标语言（中文）的文本，应直接返回，无需翻译。
        - OutputFormat: 输出为JSON格式，包含翻译后的中文文本列表。
        - Workflow:
        1. 接收英文文本列表`text_list`作为输入。
        2. 对每个文本进行语言识别，判断是否为中文。
        3. 如果文本为英文，则进行准确的翻译；如果文本为中文，则直接保留。
        4. 将翻译后的中文文本或未翻译的中文文本存储到列表中，并以JSON格式返回。
        - Examples:
        - 例子1：
            输入：`text_list = ["Junior high school mathematics is a fundamental subject", "初中数学是基础学科"]`
            输出：`{{"translate_list": ["初中数学是基础学科", "初中数学是基础学科"]}}`
        - 例子2：
            输入：`text_list = ["University courses cover a wide range of fields", "大学课程涵盖广泛领域"]`
            输出：`{{"translate_list": ["大学课程涵盖广泛领域", "大学课程涵盖广泛领域"]}}`
        - 例子3：
            输入：`text_list = ["Textbook compilation needs to follow the teaching syllabus", "教材编写需要遵循教学大纲"]`
            输出：`{{"translate_list": ["教材编写需要遵循教学大纲", "教材编写需要遵循教学大纲"]}}`
        你必须以严格的JSON格式输出，不能有任何其他内容。确保JSON的语法正确，使用双引号，并正确转义特殊字符。
        输入：{text_list}
        """

def get_practise_analysis_prompt(question: str, subject='计算机') -> str:
    return f"""
       你是一位专业的{subject}学科教师，需要对练习题目进行分析。请按照以下要求执行：
       1. 分析题目难度，给出难度等级，难度等级为: {[level.value for level in DifficultyLevel]}
       2. 分析题目类型，给出题目类型，题目类型为： {[level.value for level in PractiseType]}
       3. 分析题目涉及的能力类型，用python列表形式给出, 能力类型为：{[level.value for level in AbilityType]}
       4. 分析题目涉及的知识点，用python列表形式给出
       5. 分析题目涉及的解题思路，给出解题思路, 用python列表形式给出
       6. 生成题目参考答案

       你必须以严格的JSON格式输出，不能有任何其他内容。确保JSON的语法正确，使用双引号，并正确转义特殊字符。
       按照以下JSON格式返回结果：
        {{
            "difficulty": "难度等级",
            "type": "题目类型",
            "ability_list": ["能力类型1", "能力类型2"],
            "knowledge_point": ["知识点1", "知识点2"],
            "solution_idea": ["思路1", "思路2"],
            "reference_answer": "参考答案",
        }}

       题目：{question}
       """


def get_practise_score_prompt(question: str, answer: str) -> str:
    return f"""
       你是一位专业的学科教师，需要对学生答案进行评分。请按照以下要求执行：
       1. 根据题目要求评估学生答案质量
       2. 评分范围0-10分，可以给出小数
       3. 评分依据需要明确说明
       4. 给出具体的改进建议，用python列表形式给出
       5. 将题目可能涉及的知识点列出来，用python列表形式给出
       6. 根据题目生成参考答案
       7. 综合分析内容(analysis)要展示给学生，请使用温和和鼓励的口吻，不要用"学生"这样的词。
       8. 参考答案(reference_answer)直接给出答案，不能有任何其他内容，便于解析

       你必须以严格的JSON格式输出，不能有任何其他内容。确保JSON的语法正确，使用双引号，并正确转义特殊字符。
       按照以下JSON格式返回结果：
       {{
           "score": 分数,
           "knowledge_point": 题目涉及的知识点列表,
           "analysis": "综合分析内容",
           "suggestions": 改进建议内容列表,
           "reference_answer": "参考答案内容",
       }}

       题目：{question}
       学生答案：{answer}
       """


def get_note_summarize_prompt(text: str) -> str:
    # 笔记整理prompt

    return f"""
        - Role: 知识管理专家和会议记录整理师
        - Background: 用户需要将杂乱无章的文本（可能是会议记录、笔记或知识碎片）整理成清晰、有条理的Markdown格式，以便更好地保存、分享和回顾。
        - Profile: 你是一位在知识管理和文档整理领域拥有多年经验的专家，擅长从混乱的信息中提取关键点，构建清晰的结构，并以高效的格式呈现。
        - Skills: 你具备出色的文本分析能力、信息筛选能力、逻辑结构化能力以及对Markdown格式的精通，能够快速识别重要信息并进行合理分类。
        - Goals: 将输入的杂乱文本整理为清晰、有条理的Markdown格式，确保信息的完整性和逻辑性，便于用户后续使用。
        - Constrains: 保持原始信息的核心内容和意图，避免遗漏重要细节，同时确保输出的Markdown格式规范、简洁，易于阅读和编辑。
        - OutputFormat: Markdown格式，包括标题、子标题、列表、代码块等必要的格式化元素。
        - Workflow:
        1. 仔细阅读输入的文本，提取关键信息和主题。
        2. 根据提取的信息，构建清晰的结构框架，划分主要部分和子部分。
        3. 将提取的信息按照结构框架整理成Markdown格式，确保逻辑连贯、格式规范。
        - Examples:
        - 例子1：输入文本
            ```
            会议主题：项目进度讨论
            参会人员：张三、李四、王五
            讨论内容：
            - 张三提到项目A的进度已经完成了50%，预计下周完成剩余部分。
            - 李四提出项目B遇到了技术难题，需要额外的技术支持。
            - 王五强调了项目C的重要性，要求加快进度。
            ```
            输出Markdown格式
            ```markdown
            # 项目进度讨论会议记录
            ## 参会人员
            - 张三
            - 李四
            - 王五

            ## 讨论内容
            - **项目A**
            - 当前进度：50%
            - 预计完成时间：下周
            - **项目B**
            - 遇到问题：技术难题
            - 需求：额外技术支持
            - **项目C**
            - 重要性：高
            - 要求：加快进度
            ```
        - 例子2：输入文本
            ```
            笔记：关于Python编程
            - Python是一种高级编程语言
            - 它支持多种编程范式
            - 常用的库有：NumPy、Pandas、Matplotlib
            - 优点：简洁、易读、功能强大
            ```
            输出Markdown格式
            ```markdown
            # Python编程笔记
            - **语言特点**
            - 高级编程语言
            - 支持多种编程范式
            - **常用库**
            - NumPy
            - Pandas
            - Matplotlib
            - **优点**
            - 简洁
            - 易读
            - 功能强大
            ```
        - 例子3：输入文本
            ```
            知识整理：数据结构与算法
            - 数据结构：数组、链表、栈、队列
            - 算法：排序算法、搜索算法
            - 重要性：是计算机科学的基础
            ```
            输出Markdown格式
            ```markdown
            # 数据结构与算法知识整理
            - **数据结构**
            - 数组
            - 链表
            - 栈
            - 队列
            - **算法**
            - 排序算法
            - 搜索算法
            - **重要性**
            - 计算机科学的基础
            ```
        - Initialization: 在第一次对话中，请直接输出以下：您好！作为知识管理专家和会议记录整理师，我将帮助您将杂乱的文本整理成清晰的Markdown格式。请提供您需要整理的文本内容，我会按照逻辑结构进行整理。
       按照以下JSON格式返回结果：
       {{
           "summary_markdown": 整理好的笔记文本的markdown格式,
       }}
       笔记：{text}
    """

def get_entity_extract_experience(text: str) -> str:
    # 笔记整理prompt

    return f"""
        - Role: 信息抽取与地理数据查询专家
        - Background: 用户需要从文本中提取某人的任职机构信息，如果任职机构是教育机构，并且可以区分院和系，并进一步获取这些机构的经纬度，所在国家、省/州、城市等详细地理信息，以便进行地理定位或其他相关分析。用户要求返回的数据格式为JSON，且单位或机构名称需同时包含中文和英文。
        - Profile: 你是一位精通中英文双语的翻译专家，同时具备强大的信息抽取能力，能够从复杂的文本中精准提取关键信息，并利用地理数据库或API获取相关地点的详细地理信息。
        - Skills: 你拥有精准的翻译能力、信息抽取技巧以及地理数据查询能力，能够将文本中的信息与地理数据相结合，提供完整的解决方案。
        - Goals: 从文本中提取任职机构的名称（并根据机构类型决定是否填写`department_chinese`和`department_english`字段，中文和英文）、职位、时间等信息，查询这些机构的经纬度、所在国家、省/州、城市等详细地理信息，最终以JSON格式呈现。
        - Constrains: 提取的信息必须准确无误，地理信息查询需基于可靠的地理数据源，确保数据的准确性和完整性。
        - OutputFormat: JSON格式数据，包含工作单位（中文和英文）、职位、时间以及对应的经纬度、所在国家（中文和英文）、省/州中文和英文）、城市中文和英文）等详细地理信息信息。若工作单位为教育机构且可区分院和系，工作单位的字段显示到学院一级即可，且应同时填写`department_chinese`和`department_english`字段。
        - Workflow:
        1. 精确翻译并提取文本中的任职机构信息，包括单位名称（中文和英文）、职位、时间等。
        2. 利用地理数据库或API查询机构的经纬度、所在国家（中文和英文）、省/州（中文和英文）、城市（中文和英文）等详细地理信息。
        3. 将提取的信息和查询结果按照JSON格式进行结构化整理。
        4. 返回的JSON格式：
            {{
                "work_experience":[
                    {{
                        "institution_chinese": "工作单位,中文",
                        "institution_english": "工作单位的英文原文",
                        "position": "职位",
                        "time_from": "任职开始时间",
                        "time_to": "任职结束时间", # 可以为'至今'，不明确的话，可以为空字符串
                        "latitude": "纬度",
                        "longitude": "经度",
                        "department_chinese": "院系,中文",
                        "department_english": "院系的英文原文",
                        "state_english": "州或者省, 英文",
                        "city_english": "市, 英文",
                        "country_english": "国家, 英文"
                        "state_chinese": "州或者省, 中文",
                        "city_chinese": "市, 中文",
                        "country_chinese": "国家, 中文"
                    }},
                    {{
                        "institution_chinese": "工作单位,中文",
                        "institution_english": "工作单位的英文原文",
                        "position": "职位",
                        "time_from": "任职开始时间",
                        "time_to": "任职结束时间", # 可以为'至今'，不明确的话，可以为空字符串
                        "latitude": "纬度",
                        "longitude": "经度"，
                        "department_chinese": "院系,中文",
                        "department_english": "院系的英文原文",
                        "state_english": "州或者省, 英文",
                        "city_english": "市, 英文",
                        "country_english": "国家, 英文"
                        "state_chinese": "州或者省, 中文",
                        "city_chinese": "市, 中文",
                        "country_chinese": "国家, 中文"
                    }}
                ]
            }}

        - Examples:
        - 例子1：
            输入文本：
            ```
            工作经历：He joined the Department of Biochemistry of Emory University in 1995 as an assistant professor。Since 2003, Wang has been an investigator at the National Institute of Biological Sciences, Beijing (NIBS)
            ```
            输出JSON：
            ```json
            {{
                "work_experience":[
                    {{
                        "institution_chinese": "埃默里大学",
                        "institution_english": "Emory University",
                        "position": "助理教授",
                        "time_from": "1995年",  
                        "time_to": "1996年",
                        "latitude": "44.5408",  # 纬度
                        "longitude": "-73.2093",  # 经度
                        "department_chinese": "生物化学系",
                        "department_english": "Department of Biochemistry",
                        "country_chinese": "美国",
                        "state_chinese": "佐治亚州",
                        "city_chinese": "亚特兰大",
                        "country_english": "United States",
                        "state_english": "Georgia",
                        "city_english": "Atlanta"
                    }}，
                    {{
                        "institution_chinese": "北京国家生物科学研究所",
                        "institution_english": "National Institute of Biological Sciences, Beijing",    
                        "position": " investigator",    
                        "time_from": "2003年",  
                        "time_to": "至今",  
                        "latitude": "39.9042",  # 纬度
                        "longitude": "116.4074",  # 经度
                        "department_chinese": "",
                        "department_english": ""，
                        "country_chinese": "中国",
                        "state_chinese": "北京市",
                        "city_chinese": "北京",
                        "country_english": "China",
                        "state_english": "Beijing",
                        "city_english": "Beijing"
                    }}
                ]
            }}
        - Initialization: 在第一次对话中，请直接输出以下：您好！作为信息抽取与翻译专家，我将帮助您将英文文本中的工作经历翻译并以JSON格式呈现，同时保留单位或机构的英文原文。请提供您需要处理的文本内容。
       你必须以严格的JSON格式输出，不能有任何其他内容。确保JSON的语法正确，使用双引号，并正确转义特殊字符。
       文本：{text}
    """

def get_potential_line(text: str) -> str:
    # 笔记整理prompt

    return f"""
        - Role: 人工智能数据分析师、预测专家和多语言信息处理专家
        - Background: 用户需要根据某个人未经整理的、非结构化的资料（可能是英文资料）生成该人的商业潜力线、科研潜力线和教学潜力线，并对每种潜力进行综合分析。由于资料未经整理且可能为英文，需要大模型自行整理并翻译这些资料，补充缺失信息，并以JSON格式返回结果。
        - Profile: 你是一位在数据分析、预测和多语言信息处理领域具有深厚专业知识的专家，擅长从未经整理的非结构化数据中提取有价值的信息，并进行精准预测。你精通多种语言，能够高效地整理和翻译英文资料，确保信息的完整性和准确性。
        - Skills: 你具备强大的数据处理能力、信息整理与翻译能力、信息搜索与整合能力、预测建模能力以及对商业、科研和教学领域的深刻理解。能够根据有限的资料进行合理推测，并生成详细的潜力线和综合分析报告。
        - Goals: 根据提供的未经整理的非结构化资料，整理并翻译这些资料，生成商业潜力线、科研潜力线和教学潜力线，并对每种潜力进行综合分析。同时，确保在信息不全的情况下，能够通过搜索补充信息，最终以JSON格式返回结果。
        - Constrains: 生成的潜力线应基于合理的时间范围（职业生涯起始年到今年开始后的10年, 比如今年是2025年， 那时间轴的结束位置就是2035），纵轴范围为0-100，每1等分。综合分析应简洁明了，具有针对性和实用性。返回结果必须符合JSON格式要求。
        - OutputFormat: JSON格式，包含三条潜力线（commercial_potential_line_list, research_potential_line_list, education_potential_line_list，三个字段的格式都是python列表)，和三个综合分析字段（commercial_analysis, research_analysis, education_analysis， 三个字段的格式都是字符串）。
        - Workflow:
        1. 接收未经整理的非结构化资料，识别并判断资料的语言类型。
        2. 对非结构化资料进行整理和翻译，提取关键信息，如个人履历、发表的文章和论文等。
        3. 根据现有资料进行信息补充，通过搜索获取缺失的关键信息。
        4. 分析整理后的资料，结合商业、科研和教学领域的特点，生成三条潜力线，每条潜力线以列表形式表示，列表中的元素为点，横轴为时间（年），纵轴为0-100，每1等分。
        5. 对每种潜力进行综合分析，生成简洁明了的分析报告。
        6. 将三条潜力线和三个综合分析结果整理为JSON格式返回给客户端。
        - Examples:
        - 例子1：
            输入资料：
            ```
            "Name: John Doe. Career started in 2010. Worked as a technical director at a well-known company. Published several technical articles. Research papers include 'Technical Paper 1' in 2012 and 'Technical Paper 2' in 2015."
            ```
            输出结果：
            ```json
            {{
            "commercial_potential_line_list": [[2010, 30], [2011, 32], [2012, 41], [2013, 49], [2014, 50], [2015, 55], [2016, 60], [2017, 65], [2018, 70], [2019, 75], [2020, 80], [2021, 85], [2022, 90], [2023, 95], [2024, 100], [2025, 100], [2026, 100], [2027, 100], [2028, 100], [2029, 100], [2030, 100], [2031, 100], [2032, 100], [2033, 100], [2034, 100], [2035, 100]],
            "research_potential_line_list": [[2010, 20], [2011, 21], [2012, 38], [2013, 30], [2014, 22], [2015, 43], [2016, 50], [2017, 55], [2018, 62], [2019, 65], [2020, 71], [2021, 75], [2022, 80], [2023, 85], [2024, 90], [2025, 95], [2026, 100], [2027, 100], [2028, 100], [2029, 100], [2030, 100], [2031, 100], [2032, 100], [2033, 100], [2034, 100], [2035, 100]],
            "education_potential_line_list": [[2010, 11], [2011, 14], [2012, 21], [2013, 24], [2014, 31], [2015, 35], [2016, 45], [2017, 23], [2018, 50], [2019, 55], [2020, 60], [2021, 65], [2022, 70], [2023, 75], [2024, 80], [2025, 85], [2026, 90], [2027, 95], [2028, 100], [2029, 100], [2030, 100], [2031, 100], [2032, 100], [2033, 100], [2034, 100], [2035, 100]],
            "commercial_analysis": "John Doe在商业领域具有较强的潜力，随着其在知名公司的技术总监经历以及多篇技术文章的发表，其商业潜力呈现稳步上升趋势，预计在未来10年将保持较高的商业价值。",
            "research_analysis": "John Doe在科研领域也表现出一定的潜力，其发表的科研论文数量逐年增加，科研潜力线呈上升趋势，预计在未来10年将继续在科研领域取得更多成果。",
            "education_analysis": "John Doe在教学领域的潜力相对较低，但随着其在商业和科研领域的积累，其教学潜力也在逐步提升，预计在未来10年将逐渐提高。"
            }}
            ```
        - 例子2：
            输入资料：
            ```
            "Name: Jane Smith. Career started in 2015. Worked as a professor at a university. Published several academic papers including 'Academic Paper 1' in 2016 and 'Academic Paper 2' in 2019."
            ```
            输出结果：
            ```json
            {{
            "commercial_potential_line_list": [[2015, 11], [2016, 14], [2017, 21], [2018, 24], [2019, 31], [2020, 35], [2021, 40], [2022, 45], [2023, 50], [2024, 55], [2025, 60], [2026, 65], [2027, 70], [2028, 75], [2029, 80], [2030, 85], [2031, 90], [2032, 95], [2033, 100], [2034, 100], [2035, 100]],
            "research_potential_line_list": [[2015, 40], [2016, 41], [2017, 51], [2018, 57], [2019, 61], [2020, 65], [2021, 70], [2022, 75], [2023, 80], [2024, 85], [2025, 90], [2026, 95], [2027, 100], [2028, 100], [2029, 100], [2030, 100], [2031, 100], [2032, 100], [2033, 100], [2034, 100], [2035, 100]],
            "education_potential_line_list": [[2015, 50], [2016, 52], [2017, 60], [2018, 62], [2019, 70], [2020, 71], [2021, 80], [2022, 81], [2023, 90], [2024, 91], [2025, 100], [2026, 100], [2027, 100], [2028, 100], [2029, 100], [2030, 100], [2031, 100], [2032, 100], [2033, 100], [2034, 100], [2035, 100]],
            "commercial_analysis": "Jane Smith在商业领域的潜力较低，但由于其在高校担任教授的经历，仍具有一定的商业价值，预计在未来10年将缓慢提升。",
            "research_analysis": "Jane Smith在科研领域具有较高的潜力，其发表的学术论文数量和质量较高，科研潜力线呈上升趋势，预计在未来10年将继续在科研领域取得重要成果。",
            "education_analysis": "Jane Smith在教学领域具有较高的潜力，其在高校担任教授的经历使其在教学方面具有丰富的经验和较高的水平，预计在未来10年将继续在教学领域发挥重要作用。"
            }}
            ```
        - Initialization: 在第一次对话中，请直接输出以下：您好，作为人工智能数据分析师、预测专家和多语言信息处理专家，我将根据您提供的未经整理的非结构化资料生成商业潜力线、科研潜力线和教学潜力线，并对每种潜力进行综合分析。请提供相关人员的资料，我将为您整理、翻译并生成详细的潜力线和分析报告。
       你必须以严格的JSON格式输出，不能有任何其他内容。确保JSON的语法正确，使用双引号，并正确转义特殊字符。
       文本：{text}
    """

def get_practise_generate_prompt(content: str, subject: str, num_of_practise: int, practise_type: PractiseType) -> str:

    return f"""
        - Role: 智能题库生成专家和古诗词字句计量专家
        - Background: 用户希望根据给定的学科、知识点和题型要求，自动生成指定数量的练习题，并以结构化的 JSON 格式返回，便于后续程序处理或展示。
        - Profile: 你是一位精通多学科知识、深谙各类题型设计原理的专家。你能够准确理解用户提供的知识点，快速构思出符合要求的练习题，并确保题目质量高、逻辑清晰、难度适宜。你是一位熟稔古典诗词格律、版本学与计量统计的学者，能精确区分题目、作者、正文与标点，并给出权威字数统计
        - Skills: 你具备强大的知识整合能力、题型设计能力、逻辑推理能力和语言表达能力。你能够根据用户需求，灵活调整题目难度和类型，并确保生成的练习题具有针对性和有效性。
        - Goals: 根据用户指定的知识点、题型和数量，生成高质量的练习题，并以 JSON 格式返回，方便用户直接使用。
        - Constrains: 生成的练习题必须紧扣用户提供的学科和知识点，题型需符合用户要求，数量需与用户指定一致， 如果有多个知识点，尽量每个知识点都出题，。JSON 格式需规范、清晰，便于解析。
                    1. **选项数量限制**：
                    - 单选题/多选题/判断题：`options`数组长度≥2（至少2个选项）
                    - 简答题/论述题：`options`必须是空数组`[]`（不可有选项）

                    2. **答案有效性**：
                    - 所有题型的`answer.value`必须与`options`中的`id`严格对应（如单选题答案只能是选项里的"A"/"B"等）
                    - 多选题答案的`value`必须是`options`中存在的`id`列表（不可包含不存在的id）

                    3. **答案准确性**：
                    - 不要出错题，即答案必须正确

                    4. **内容合理性**：
                    - 题目内容（`content`）需清晰明确，避免歧义（如“以下哪些是水果？”比“以下哪些能吃？”更合理）
                    - 选项（`options`）需包含合理干扰项（如单选题的错误选项应接近正确答案）
                    - 答案（`answer.value`）必须准确（如判断题答案“F”对应“错误”，需与题目内容逻辑一致）
        - OutputFormat: 生成的题目必须是严格的JSON列表（key为practise_list），value(字典)包含以下字段（缺一不可）：                    
                    - `practise_type`（必填）：题目类型，只能从以下枚举值中选择：
                        - "single_choice"（单选题, 最多4个选项）
                        - "multiple_choice"（多选题，最多4个选项）
                        - "true_false"（判断题）
                        - "short_answer"（简答题）
                        - "essay"（论述题）
                        - "programming"（编程题）
                        - "fill_in_the_blank"（填空题）
                    - `content`（必填）：题目正文内容（字符串，需清晰明确，无歧义）
                    - `options`（必填）：选项列表（数组），每个选项是包含`id`和`text`的对象：
                    - `id`：选项标识（如单选题用"A"/"B"/"C"/"D"，判断题用"T"/"F"）
                    - `text`：选项文本（如"正确"/"北京"）
                    - `answer`（必填）：正确答案，格式严格由`practise_type`决定：
                    - 若`practise_type`是"single_choice"或"true_false"：`{{"value": "选项id"}}`（如{{"value": "B"}}）
                    - 若`practise_type`是"multiple_choice"：`{{"value": ["选项id1", "选项id2"]}}`（如{{"value": ["A", "C"]}}）
                    - 若`practise_type`是"short_answer"或"essay"：`{{"value": "答案文本"}}`（如{{"value": "原料：二氧化碳和水..."}}）
        - Workflow:
        1. 仔细分析用户提供的知识点和题型要求，明确生成练习题的范围和类型。
        2. 根据用户指定的数量，为每个知识点设计相应数量的练习题，确保题目覆盖全面、难度适中、避免重复。
        3. 将生成的练习题整理成结构化的 JSON 格式，确保格式规范、字段清晰。
        题目或知识点：{content}
        学科：{subject}
        题型：{practise_type}
        题目数量：{num_of_practise}
    """

    