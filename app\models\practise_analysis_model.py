from enum import Enum
from typing import Union

from pydantic import BaseModel, Field
from pydantic.v1 import validator


# 枚举类型定义
class DifficultyLevel(str, Enum):
    easy = "简单"
    medium = "中等"
    hard = "困难"
    extreme = "极难"


class AbilityType(str, Enum):
    remembrance = "识记"
    understanding = "理解"
    application = "应用"
    analysis = "分析"
    evaluation = "综合"
    calculation = "计算"
    reasoning = "推理"
    logic = "逻辑"
    programming = "编程"
    design = "设计"



class PractiseType(str, Enum):
    single_choice = "单选题"
    multiple_choice = "多选题"
    true_false = "判断题"
    short_answer = "简答题"
    essay = "论述题"
    calculation = "计算题"
    programming = "编程题"
    fill_in_the_blank = "填空题"
    logic = "逻辑题"
    reasoning = "推理题"
    analysis = "分析题"
    evaluation = "评价题"
    application = "应用题"
    design = "设计题"
    problem_solving = "问题求解"
    data_analysis = "数据分析"
    data_visualization = "数据可视化"
    data_structure = "数据结构"
    algorithm = "算法"
    database = "数据库"
    network = "网络"
    operating_system = "操作系统"
    computer_architecture = "计算机体系结构"
    software_development = "软件开发"
    software_testing = "软件测试"
    software_engineering = "软件工程"
    computer_network = "计算机网络"
    computer_graphics = "计算机图形学"
    artificial_intelligence = "人工智能"
    machine_learning = "机器学习"
    deep_learning = "深度学习"
    natural_language_processing = "自然语言处理"
    computer_vision = "计算机视觉"


# 请求模型
class AnalysisRequest(BaseModel):
    """
    试题分析请求模型
    - content: 试题内容（必填）
    - subject: 学科分类（如数学/物理/历史）
    - options: 选择题的选项（可选）
    """
    content: str = Field(...,
                       min_length=10,
                       example="计算抛物线y=x²在x=2处的切线方程",
                       description="需包含完整题目内容和条件")
    subject: str = Field("通用",
                       example="数学",
                       description="科目分类可提高分析准确性")

    llm_params: dict = Field(
        {},
        example={
            "temperature": 0.0,
            "top_p": 0.9,
        },
        description="llm模型参数"
    )


# 智能分析响应模型
class AnalysisResponse(BaseModel):
    """
    试题结构化分析结果
    - difficulty: 试题难度分层
    - type: 分析题目类型
    - ability_list: 分析题目涉及的能力类型
    - knowledge_point: 分析题目涉及的知识点
    - solution_idea: 分析题目涉及的解题思路
    - reference_answer: 生成的参考答案
    """
    difficulty: DifficultyLevel
    knowledge_point: list[str]
    solution_idea: list[str]
    reference_answer: str

    ability_list: list[Union[AbilityType, str]]
    @validator('ability_list', pre=True, each_item=True)
    def validate_ability_type(cls, v):
        try:
            return AbilityType(v)  # 尝试转换为枚举
        except ValueError:
            return v  # 保留原始字符串

    type: Union[PractiseType, str]
    @validator('type', pre=True, each_item=True)
    def validate_practise_type(cls, v):
        try:
            return PractiseType(v)  # 尝试转换为枚举
        except ValueError:
            return v  # 保留原始字符串

