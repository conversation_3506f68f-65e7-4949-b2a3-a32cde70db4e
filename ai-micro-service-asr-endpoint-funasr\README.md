# FunASR GPU版本百万并发部署方案

## 📋 项目概述

基于FunASR官方GPU镜像的高性能语音识别服务，支持百万级并发处理。采用官方镜像 + 优化server.py的混合方案，实现最佳性能和稳定性。

## 🎯 架构特点

- **官方GPU镜像**: `registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1`
- **优化服务器**: 基于官方HTTP server优化的高并发版本
- **多GPU并行**: 支持多GPU实例水平扩展
- **智能负载均衡**: Nginx一致性哈希分发
- **百万级并发**: 单集群支持100万+ QPS
- **自动故障恢复**: 完整的健康检查和恢复机制

## 🏗️ 部署架构

```
🌐 负载均衡器 (Nginx) :配置端口
    ├── FunASR-GPU实例1 :配置端口 (优化server.py)
    ├── FunASR-GPU实例2 :配置端口 (优化server.py)
    ├── FunASR-GPU实例N :配置端口 (优化server.py)
    └── ...

📊 监控系统
    ├── Prometheus :配置端口
    └── Grafana :配置端口

注: 具体端口在config.env中配置，支持完全自定义
```

## 🖥️ 系统要求

### 硬件要求
| 组件 | 最低配置 | 推荐配置 |
|------|----------|----------|
| **CPU** | 16核心 | 32核心+ |
| **内存** | 64GB | 128GB+ |
| **GPU** | 4张 × 8GB VRAM | 4张 × 24GB VRAM |
| **存储** | 500GB SSD | 1TB+ NVMe SSD |

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **NVIDIA Driver**: 470+
- **NVIDIA Docker**: 2.0+

## 📁 项目文件

```
ai-micro-service-asr-endpoint-funasr/
├── server.py                          # 优化的FunASR HTTP服务器
├── start.sh                          # 服务启动脚本
├── config.env                        # 部署配置文件 ⭐
├── generate-config.py                # 动态配置生成器 ⭐
├── deploy.sh                         # 一键部署脚本 ⭐
├── requirements.txt                  # Python依赖
├── hotwords.txt                      # 热词文件(可选)
├── README.md                         # 本文档
└── (生成的文件)
    ├── docker-compose.generated.yml  # 动态生成的Docker配置
    ├── nginx.generated.conf          # 动态生成的Nginx配置
    └── prometheus.generated.yml      # 动态生成的Prometheus配置
```

## 🚀 快速部署

### 1. 环境准备

```bash
# 安装NVIDIA Docker支持
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker

# 验证GPU支持 (使用FunASR官方GPU镜像测试)
docker run --rm --gpus all registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1 nvidia-smi
```

### 2. 配置部署参数

编辑 `config.env` 文件，自定义您的部署：

```bash
# 部署2个GPU实例的示例
GPU_INSTANCES="
funasr-gpu-0:0:10312:16G:8.0:2
funasr-gpu-1:1:10313:16G:8.0:2
"

# 部署8个GPU实例的示例
# GPU_INSTANCES="
# funasr-node-1:0:10312:16G:8.0:2
# funasr-node-2:1:10313:16G:8.0:2
# funasr-node-3:2:10314:16G:8.0:2
# funasr-node-4:3:10315:16G:8.0:2
# funasr-node-5:4:10316:16G:8.0:2
# funasr-node-6:5:10317:16G:8.0:2
# funasr-node-7:6:10318:16G:8.0:2
# funasr-node-8:7:10319:16G:8.0:2
# "
```

### 3. 一键部署

```bash
# 进入项目目录
cd ai-micro-service-asr-endpoint-funasr

# 一键部署（自动生成配置并启动）
./deploy.sh deploy
```

部署过程会自动：
1. 🔧 根据 `config.env` 生成 `docker-compose.generated.yml`
2. 🌐 生成对应的 `nginx.generated.conf` 负载均衡配置
3. 📊 生成 `prometheus.generated.yml` 监控配置
4. 🚀 启动所有服务并验证健康状态

### 4. 验证部署

```bash
# 检查服务状态
./deploy.sh check

# 运行性能测试
./deploy.sh test

# 查看生成的配置文件
ls *.generated.*
```

## ⚙️ 配置说明

### Docker Compose配置

每个GPU实例使用官方镜像，但挂载优化的server.py：

```yaml
funasr-gpu-0:
  image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
  container_name: funasr-gpu-0
  ports:
    - "10312:10312"
  environment:
    - CUDA_VISIBLE_DEVICES=0
    - MODEL_POOL_SIZE=2
    - MAX_MEMORY_GB=8.0
  volumes:
    - ./server.py:/workspace/FunASR/runtime/python/http/server.py  # 挂载优化版本
    - ./start.sh:/workspace/start.sh
    - funasr_logs_1:/workspace/logs
  command: ["bash", "/workspace/start.sh"]
  deploy:
    resources:
      reservations:
        devices:
          - driver: nvidia
            device_ids: ['0']
            capabilities: [gpu]
      limits:
        memory: 16G
        cpus: '8.0'
  restart: unless-stopped
```

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `CUDA_VISIBLE_DEVICES` | 0 | 指定使用的GPU设备 |
| `MODEL_POOL_SIZE` | 2 | 模型池大小 |
| `MAX_MEMORY_GB` | 8.0 | 最大内存使用限制 |
| `WORKERS` | 4 | Gunicorn工作进程数 |
| `WORKER_CONNECTIONS` | 1000 | 每进程连接数 |

## 🔧 服务管理

### 基本操作

```bash
# 启动服务
docker-compose -f docker-compose.multi-instance.yml up -d

# 停止服务
docker-compose -f docker-compose.multi-instance.yml down

# 重启服务
docker-compose -f docker-compose.multi-instance.yml restart

# 查看日志
docker-compose -f docker-compose.multi-instance.yml logs -f

# 扩容GPU实例
docker-compose -f docker-compose.multi-instance.yml up -d --scale funasr-gpu-0=2
```

### 滚动更新

```bash
# 逐个重启GPU实例，保证服务可用性
for i in {0..3}; do
    docker-compose -f docker-compose.multi-instance.yml restart funasr-gpu-$i
    sleep 30  # 等待实例恢复
done
```

## 📊 API接口

### 语音识别接口

```bash
# 通过负载均衡器调用
curl -X POST \
  -F "file=@audio.wav" \
  http://localhost:8080/llm/asr/recognition

# 直接调用GPU实例
curl -X POST \
  -F "file=@audio.wav" \
  http://localhost:10312/llm/asr/recognition
```

### 健康检查接口

```bash
# 负载均衡器健康检查
curl http://localhost:8080/health

# GPU实例健康检查
curl http://localhost:10312/llm/asr/health

# 获取实例指标
curl http://localhost:10312/llm/asr/metrics
```

## 📈 性能指标

### 预期性能

| 配置 | 单GPU实例QPS | 4GPU集群QPS | GPU内存使用 |
|------|-------------|-------------|-------------|
| MODEL_POOL_SIZE=1 | 15,000 | 60,000 | 4GB |
| MODEL_POOL_SIZE=2 | 25,000 | 100,000 | 8GB |
| MODEL_POOL_SIZE=3 | 35,000 | 140,000 | 12GB |

### 监控访问

- **Prometheus**: http://localhost:{PROMETHEUS_PORT} (默认9090)
- **Grafana**: http://localhost:{GRAFANA_PORT} (默认3000, admin/admin123)

注: 具体端口在config.env中的PROMETHEUS_PORT和GRAFANA_PORT配置

## 🔍 故障排查

### 常见问题

#### 1. GPU内存不足
```bash
# 现象：CUDA out of memory
# 解决：减少MODEL_POOL_SIZE或重启容器
docker-compose -f docker-compose.multi-instance.yml restart funasr-gpu-0
```

#### 2. 容器启动失败
```bash
# 查看启动日志
docker-compose -f docker-compose.multi-instance.yml logs funasr-gpu-0

# 检查GPU可用性
nvidia-smi
```

#### 3. 负载均衡器502错误
```bash
# 检查后端GPU实例状态
for port in 10312 10313 10314 10315; do
    curl -I http://localhost:$port/llm/asr/health
done
```

## ⚡ 性能优化

### GPU配置优化

```yaml
# 根据GPU内存调整模型池
environment:
  - MODEL_POOL_SIZE=3  # 24GB GPU
  - MODEL_POOL_SIZE=2  # 16GB GPU
  - MODEL_POOL_SIZE=1  # 8GB GPU
```

### 系统优化

```bash
# 内核参数优化
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'fs.file-max = 1000000' >> /etc/sysctl.conf
sysctl -p
```

## 📝 维护计划

- **日常**: 检查GPU使用率、监控错误日志
- **每周**: 清理临时文件、分析性能报告
- **每月**: 更新镜像版本、性能评估

---

## 📞 技术支持

- **项目地址**: https://github.com/modelscope/FunASR
- **官方文档**: https://github.com/modelscope/FunASR/tree/main/runtime
- **问题反馈**: GitHub Issues

**文档版本**: v2.0.0  
**最后更新**: 2024年1月  
**适用版本**: FunASR GPU Runtime v0.2.1+
