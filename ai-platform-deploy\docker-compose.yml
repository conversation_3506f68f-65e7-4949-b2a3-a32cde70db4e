version: '3.8'

services:
  # AI服务网关
  gateway:
    image: yourorg/ai-gateway:latest
    # 或使用本地构建
    # build: https://github.com/yourorg/ai-gateway.git
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DEBUG=false
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TTS微服务
  tts-service:
    image: yourorg/ai-tts-service:latest
    # 或使用本地构建
    # build: https://github.com/yourorg/ai-tts-service.git
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - TTS_API_KEY=${TTS_API_KEY}
      - TTS_BASE_URL=${TTS_BASE_URL}
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 教育平台
  education-app:
    image: yourorg/ai-education-app:latest
    # 或使用本地构建
    # build: https://github.com/yourorg/ai-education-app.git
    ports:
      - "8100:8100"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 医疗平台
  medical-app:
    image: yourorg/ai-medical-app:latest
    # 或使用本地构建
    # build: https://github.com/yourorg/ai-medical-app.git
    ports:
      - "8200:8200"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8200/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - ai-platform
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:

networks:
  ai-platform:
    driver: bridge
