#!/usr/bin/env python3
"""
FunASR GPU集群配置生成器
根据config.env动态生成docker-compose.yml和nginx.conf
"""

import os
import sys
from pathlib import Path


def load_config():
    """加载配置文件"""
    config = {}
    config_file = Path("config.env")
    
    if not config_file.exists():
        print("❌ config.env文件不存在")
        sys.exit(1)
    
    with open(config_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                config[key.strip()] = value.strip().strip('"')
    
    return config


def parse_gpu_instances(gpu_instances_str):
    """解析GPU实例配置"""
    instances = []
    
    for line in gpu_instances_str.strip().split('\n'):
        line = line.strip()
        if line and not line.startswith('#'):
            parts = line.split(':')
            if len(parts) >= 6:
                instances.append({
                    'name': parts[0],
                    'gpu_id': parts[1],
                    'port': parts[2],
                    'memory': parts[3],
                    'cpu': parts[4],
                    'model_pool_size': parts[5]
                })
    
    return instances


def generate_docker_compose(config, instances):
    """生成docker-compose.yml"""
    
    compose_content = f"""version: '3.8'

# FunASR GPU集群 - 动态生成配置
# 实例数量: {len(instances)}个

services:
"""
    
    # 生成GPU实例配置
    for i, instance in enumerate(instances):
        compose_content += f"""  # GPU实例{i+1} - {instance['name']}
  {instance['name']}:
    image: {config['FUNASR_IMAGE']}
    container_name: {instance['name']}
    ports:
      - "{instance['port']}:{instance['port']}"
    environment:
      - CUDA_VISIBLE_DEVICES={instance['gpu_id']}
      - MODEL_POOL_SIZE={instance['model_pool_size']}
      - MAX_MEMORY_GB={config['MAX_MEMORY_GB']}
      - WORKERS={config['WORKERS']}
      - WORKER_CONNECTIONS={config['WORKER_CONNECTIONS']}
      - PYTHONUNBUFFERED=1
      - PORT={instance['port']}
    volumes:
      - ./server.py:/workspace/FunASR/runtime/python/http/server.py
      - ./start.sh:/workspace/start.sh
      - ./requirements.txt:/workspace/requirements.txt
      - funasr_logs_{i+1}:/workspace/logs
      - funasr_temp_{i+1}:/tmp/funasr
    working_dir: /workspace
    command: ["bash", "/workspace/start.sh"]
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['{instance['gpu_id']}']
              capabilities: [gpu]
        limits:
          memory: {instance['memory']}
          cpus: '{instance['cpu']}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{instance['port']}/llm/asr/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - funasr-network

"""
    
    # 生成负载均衡器配置
    depends_on = [instance['name'] for instance in instances]
    compose_content += f"""  # Nginx负载均衡器
  funasr-loadbalancer:
    image: nginx:alpine
    container_name: funasr-lb
    ports:
      - "{config['NGINX_PORT']}:80"
    volumes:
      - ./nginx.generated.conf:/etc/nginx/nginx.conf:ro
    depends_on:
{chr(10).join(f'      - {name}' for name in depends_on)}
    restart: unless-stopped
    networks:
      - funasr-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: funasr-prometheus
    ports:
      - "{config['PROMETHEUS_PORT']}:9090"
    volumes:
      - ./prometheus.generated.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
    networks:
      - funasr-network

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: funasr-grafana
    ports:
      - "{config['GRAFANA_PORT']}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - funasr-network

volumes:
{chr(10).join(f'  funasr_logs_{i+1}:' for i in range(len(instances)))}
{chr(10).join(f'  funasr_temp_{i+1}:' for i in range(len(instances)))}
  prometheus_data:
  grafana_data:

networks:
  funasr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
"""
    
    return compose_content


def generate_nginx_conf(config, instances):
    """生成nginx.conf"""
    
    # 生成upstream配置
    upstream_servers = []
    health_servers = []
    
    for instance in instances:
        upstream_servers.append(f"        server {instance['name']}:{instance['port']} weight={config['LB_WEIGHT']} max_fails={config['LB_MAX_FAILS']} fail_timeout={config['LB_FAIL_TIMEOUT']};")
        health_servers.append(f"        server {instance['name']}:{instance['port']};")
    
    # 根据负载均衡算法生成配置
    if config['LB_ALGORITHM'] == 'hash':
        lb_method = "hash $request_body consistent;"
    elif config['LB_ALGORITHM'] == 'least_conn':
        lb_method = "least_conn;"
    else:
        lb_method = ""  # round_robin是默认的
    
    nginx_content = f"""events {{
    worker_connections 4096;
    use epoll;
    multi_accept on;
}}

http {{
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format funasr_access '$remote_addr - $remote_user [$time_local] '
                             '"$request" $status $body_bytes_sent '
                             '"$http_referer" "$http_user_agent" '
                             'rt=$request_time uct="$upstream_connect_time" '
                             'uht="$upstream_header_time" urt="$upstream_response_time" '
                             'upstream="$upstream_addr"';
    
    access_log /var/log/nginx/funasr_access.log funasr_access;
    error_log  /var/log/nginx/funasr_error.log warn;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        application/json
        application/javascript
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # FunASR GPU集群配置
    upstream funasr_cluster {{
        # 负载均衡算法: {config['LB_ALGORITHM']}
        {lb_method}
        
        # {len(instances)}个GPU实例
{chr(10).join(upstream_servers)}
        
        # 连接保持
        keepalive 64;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }}
    
    # 健康检查上游
    upstream funasr_health {{
{chr(10).join(health_servers)}
    }}
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=funasr_limit:10m rate=50r/s;
    limit_conn_zone $binary_remote_addr zone=funasr_conn:10m;
    
    # 主服务器配置
    server {{
        listen 80;
        server_name localhost;
        
        # 基本配置
        client_body_timeout 300s;
        client_header_timeout 60s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 限流限连
        limit_req zone=funasr_limit burst=100 nodelay;
        limit_conn funasr_conn 20;
        
        # 健康检查端点
        location /health {{
            access_log off;
            return 200 "FunASR Load Balancer OK\\n";
            add_header Content-Type text/plain;
        }}
        
        # 集群健康检查
        location /cluster/health {{
            proxy_pass http://funasr_health/llm/asr/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }}
        
        # ASR识别接口
        location /llm/asr/recognition {{
            proxy_pass http://funasr_cluster;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-funasr-lb";
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            proxy_temp_file_write_size 64k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }}
        
        # 指标接口
        location /llm/asr/metrics {{
            proxy_pass http://funasr_cluster;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }}
        
        # Nginx状态页面
        location /nginx_status {{
            stub_status on;
            access_log off;
            allow **********/16;
            deny all;
        }}
        
        # 默认路由
        location / {{
            proxy_pass http://funasr_cluster;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }}
    }}
"""
    
    # 生成直接访问各实例的配置
    for i, instance in enumerate(instances):
        direct_port = 8001 + i
        nginx_content += f"""
    # 直接访问{instance['name']} (调试用)
    server {{
        listen {direct_port};
        server_name localhost;
        
        location / {{
            proxy_pass http://{instance['name']}:{instance['port']};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-{instance['name']}";
        }}
    }}"""
    
    nginx_content += "\n}\n"
    
    return nginx_content


def generate_prometheus_yml(config, instances):
    """生成prometheus.yml"""
    
    targets = [f"'{instance['name']}:{instance['port']}'" for instance in instances]
    
    prometheus_content = f"""global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # FunASR GPU实例监控
  - job_name: 'funasr-gpu-instances'
    static_configs:
      - targets: 
{chr(10).join(f'          - {target}' for target in targets)}
    metrics_path: '/llm/asr/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Nginx负载均衡器监控
  - job_name: 'funasr-loadbalancer'
    static_configs:
      - targets: ['funasr-loadbalancer:80']
    metrics_path: '/nginx_status'
    scrape_interval: 15s

  # 系统监控 (如果有node_exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s
"""
    
    return prometheus_content


def main():
    """主函数"""
    print("🔧 生成FunASR GPU集群配置...")
    
    # 加载配置
    config = load_config()
    
    # 解析GPU实例配置
    if 'GPU_INSTANCES' not in config:
        print("❌ config.env中未找到GPU_INSTANCES配置")
        sys.exit(1)
    
    instances = parse_gpu_instances(config['GPU_INSTANCES'])
    
    if not instances:
        print("❌ 未找到有效的GPU实例配置")
        sys.exit(1)
    
    print(f"📊 检测到 {len(instances)} 个GPU实例:")
    for i, instance in enumerate(instances):
        print(f"  {i+1}. {instance['name']} - GPU:{instance['gpu_id']} - 端口:{instance['port']}")
    
    # 生成配置文件
    print("\n📝 生成配置文件...")
    
    # 生成docker-compose.yml
    compose_content = generate_docker_compose(config, instances)
    with open('docker-compose.generated.yml', 'w', encoding='utf-8') as f:
        f.write(compose_content)
    print("  ✅ docker-compose.generated.yml")
    
    # 生成nginx.conf
    nginx_content = generate_nginx_conf(config, instances)
    with open('nginx.generated.conf', 'w', encoding='utf-8') as f:
        f.write(nginx_content)
    print("  ✅ nginx.generated.conf")
    
    # 生成prometheus.yml
    prometheus_content = generate_prometheus_yml(config, instances)
    with open('prometheus.generated.yml', 'w', encoding='utf-8') as f:
        f.write(prometheus_content)
    print("  ✅ prometheus.generated.yml")
    
    print(f"\n🎉 配置生成完成！")
    print(f"📋 实例数量: {len(instances)}")
    print(f"🌐 负载均衡器端口: {config['NGINX_PORT']}")
    print(f"📊 监控端口: Prometheus({config['PROMETHEUS_PORT']}), Grafana({config['GRAFANA_PORT']})")
    print(f"\n🚀 部署命令:")
    print(f"  docker-compose -f docker-compose.generated.yml up -d")


if __name__ == "__main__":
    main()
