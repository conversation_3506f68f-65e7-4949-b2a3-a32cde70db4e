import app.core.forward_route_config as route_config
from app.services.load_balancer_service import LoadBalancerService

from fastapi import HTTPException

load_balancer = LoadBalancerService()

def create_forward_url(model_name, path, business_type=None) -> str:
    config = route_config.ROUTE_MAPPING.get("/llm/openai", {})
    if model_name is None:
        if business_type is None:
            model_name = config.get("default_business_model_dict", {}).get("default", None)
        else:
            model_name = config.get("default_business_model_dict", {}).get(business_type, None)

    # 使用负载均衡器选择服务器
    server_url = load_balancer.get_server_for_model(model_name, config)
    
    if server_url is None:
        raise HTTPException(400, "未配置的模型或所有服务器不可用")

    target_url = server_url + "/chat_forward" + path
    return target_url, model_name

def create_api_forward_url(prefix, path, model=None) -> tuple:
    config = route_config.ROUTE_MAPPING.get(prefix, {})
    if model is None:
        model = config.get("default", None)

    # 使用负载均衡器选择服务器
    server_url = load_balancer.get_server_for_model(model, config)
    
    if server_url is None:
        raise HTTPException(400, "未配置的模型或所有服务器不可用")

    target_url = server_url + "/api_forward" + path
    return target_url, config, model



def generate_sk_api_key(random_length: int = 32) -> str:
    """
    生成以 'sk-' 开头的安全API Key
    
    参数:
        random_length: 随机部分的长度（默认32位）
        
    返回:
        str: 生成的API Key（格式：sk-<随机字符>）
    """
    import secrets
    import string
    # 固定前缀
    prefix = "sk-"
    
    # 可用字符集：大小写字母 + 数字（共62个字符）
    chars = string.ascii_letters + string.digits
    
    # 生成高安全性的随机字符串（使用secrets模块）
    random_part = ''.join(secrets.choice(chars) for _ in range(random_length))
    
    # 拼接前缀和随机部分
    return f"{prefix}{random_part}"
