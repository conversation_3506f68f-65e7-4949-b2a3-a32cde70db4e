from fastapi import APIRouter
from app.services.entity_extract_experience_service import EntityExtractExperienceService
from app.models.entity_extract_experience_model import EntityExtractExperienceRequest, EntityExtractExperienceResponse

router = APIRouter()
service = EntityExtractExperienceService()


@router.post("/llm/matrix/entityExtract/experience", response_model=EntityExtractExperienceResponse)
async def entity_extract_experience(request: EntityExtractExperienceRequest):
    return await service.extract_experience(request.text, request.llm_params)
