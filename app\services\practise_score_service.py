from fastapi import HTTPException

from app.llm.vllm import <PERSON><PERSON><PERSON><PERSON>
from app.services.abs_service import AbsService
from app.core.config import settings
import app.utils.prompts as prompts


class PractiseScoreService(AbsService):
    def __init__(self):
        super().__init__()
        self.client = VLLMClient(settings.VLLM_ENDPOINT)

    async def practise_score(self, question: str, answer: str, llm_params: dict = {}) -> dict:
        print('question:', question)
        print('answer:', answer)
        prompt = prompts.get_practise_score_prompt(question, answer)
        # 构造动态提示词
        print('prompt:', prompt)

        if 'temperature' not in llm_params.keys():
            llm_params['temperature'] = 0.01
        if 'top_p' not in llm_params.keys():
            llm_params['top_p'] = 0.001
        if 'top_k' not in llm_params.keys():
            llm_params['top_k'] = 1
        try:
            response_dict = await self.client.generate(prompt, llm_params)
            self.logger.info(f"模型响应: {response_dict}")
            return self._parse_response(response_dict)
        except TimeoutError as e:
            raise HTTPException(status_code=504, detail="模型响应超时")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"试题分析服务内部错误, {e}")

    def _parse_response(self, response_dict) -> dict:
        try:
            model_response = self.safe_json_parse(response_dict["choices"][0]["message"]["content"])
        except Exception as e:
            self.logger.error(f"JSON解析错误: {e}")
            raise HTTPException(status_code=500, detail="Internal Server Error, safe_json_parse error")

        # 验证返回字段
        required_fields = ["score", "analysis", "suggestions", "reference_answer", "knowledge_point"]
        for field in required_fields:
            if field not in model_response:
                raise HTTPException(
                    status_code=500,
                    detail=f"模型响应缺少必要字段: {field}"
                )

        return model_response