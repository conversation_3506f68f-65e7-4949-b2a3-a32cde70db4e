version: '3.8'

services:
  # API网关
  gateway:
    build: ./gateway
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/ai_gateway
      - CONSUL_HOST=consul
      - PROMETHEUS_ENABLED=true
      - JAEGER_ENABLED=true
    depends_on:
      - redis
      - postgres
      - consul
    volumes:
      - ./gateway:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - ai-gateway

  # OCR服务
  ocr-service:
    build: ./services/ocr-service
    ports:
      - "8001:8001"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_HOST=consul
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/ocr-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8001 --reload
    networks:
      - ai-gateway

  # TTS服务
  tts-service:
    build: ./services/tts-service
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_HOST=consul
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/tts-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8002 --reload
    networks:
      - ai-gateway

  # ASR服务
  asr-service:
    build: ./services/asr-service
    ports:
      - "8003:8003"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_HOST=consul
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/asr-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8003 --reload
    networks:
      - ai-gateway

  # LLM服务
  llm-service:
    build: ./services/llm-service
    ports:
      - "8004:8004"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_HOST=consul
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/llm-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8004 --reload
    networks:
      - ai-gateway

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - ai-gateway

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ai_gateway
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-gateway

  # 服务发现
  consul:
    image: consul:1.15
    ports:
      - "8500:8500"
    command: consul agent -dev -client=0.0.0.0 -ui
    volumes:
      - consul_data:/consul/data
    networks:
      - ai-gateway

  # 监控 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - ai-gateway

  # 监控 - Grafana
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-gateway

  # 链路追踪 - Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - ai-gateway

  # 消息队列 - Kafka
  kafka:
    image: confluentinc/cp-kafka:latest
    ports:
      - "9092:9092"
    environment:
      - KAFKA_BROKER_ID=1
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
    depends_on:
      - zookeeper
    networks:
      - ai-gateway

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    ports:
      - "2181:2181"
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
    networks:
      - ai-gateway

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - gateway
    networks:
      - ai-gateway

volumes:
  redis_data:
  postgres_data:
  consul_data:
  prometheus_data:
  grafana_data:

networks:
  ai-gateway:
    driver: bridge
