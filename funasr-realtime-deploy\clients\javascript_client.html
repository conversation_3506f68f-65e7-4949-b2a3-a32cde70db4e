<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FunASR WebSocket实时语音转写</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .config-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .config-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .config-section input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.recording {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        
        .result-item {
            margin: 5px 0;
            padding: 8px;
            border-radius: 3px;
        }
        
        .result-item.final {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        .result-item.partial {
            background-color: #e2e3e5;
            border-left: 4px solid #6c757d;
        }
        
        .timestamp {
            font-size: 12px;
            color: #6c757d;
            margin-right: 10px;
        }
        
        .file-upload {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
        }
        
        .file-upload input[type="file"] {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 FunASR WebSocket实时语音转写</h1>
        
        <!-- 配置区域 -->
        <div class="config-section">
            <label for="serverUrl">WebSocket服务器地址:</label>
            <input type="text" id="serverUrl" value="ws://localhost:8080/ws/asr">
        </div>
        
        <!-- 状态显示 -->
        <div id="status" class="status disconnected">未连接</div>
        
        <!-- 控制按钮 -->
        <div class="button-group">
            <button id="connectBtn" class="btn-primary">连接服务器</button>
            <button id="startBtn" class="btn-success" disabled>开始录音</button>
            <button id="stopBtn" class="btn-danger" disabled>停止录音</button>
            <button id="clearBtn" class="btn-secondary">清空结果</button>
        </div>
        
        <!-- 文件上传测试 -->
        <div class="file-upload">
            <h3>📁 文件上传测试</h3>
            <input type="file" id="audioFile" accept="audio/*">
            <button id="uploadBtn" class="btn-primary">上传识别</button>
        </div>
        
        <!-- 识别结果 -->
        <div class="results">
            <h3>🗣️ 识别结果:</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        class FunASRWebSocketClient {
            constructor() {
                this.websocket = null;
                this.mediaRecorder = null;
                this.audioStream = null;
                this.isConnected = false;
                this.isRecording = false;
                
                this.initializeElements();
                this.bindEvents();
            }
            
            initializeElements() {
                this.serverUrlInput = document.getElementById('serverUrl');
                this.statusDiv = document.getElementById('status');
                this.connectBtn = document.getElementById('connectBtn');
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.resultsDiv = document.getElementById('results');
                this.audioFileInput = document.getElementById('audioFile');
                this.uploadBtn = document.getElementById('uploadBtn');
            }
            
            bindEvents() {
                this.connectBtn.addEventListener('click', () => this.toggleConnection());
                this.startBtn.addEventListener('click', () => this.startRecording());
                this.stopBtn.addEventListener('click', () => this.stopRecording());
                this.clearBtn.addEventListener('click', () => this.clearResults());
                this.uploadBtn.addEventListener('click', () => this.uploadFile());
            }
            
            async toggleConnection() {
                if (this.isConnected) {
                    this.disconnect();
                } else {
                    await this.connect();
                }
            }
            
            async connect() {
                const serverUrl = this.serverUrlInput.value;
                
                try {
                    this.updateStatus('连接中...', 'recording');
                    
                    this.websocket = new WebSocket(serverUrl);
                    
                    this.websocket.onopen = () => {
                        this.isConnected = true;
                        this.updateStatus('已连接', 'connected');
                        this.connectBtn.textContent = '断开连接';
                        this.connectBtn.className = 'btn-danger';
                        this.startBtn.disabled = false;
                        
                        // 发送初始配置
                        const config = {
                            mode: "2pass",
                            chunk_size: [5, 10, 5],
                            chunk_interval: 10,
                            wav_name: "microphone",
                            wav_format: "PCM"
                        };
                        
                        this.websocket.send(JSON.stringify(config));
                        this.addResult('✅ 连接成功，配置已发送', true);
                    };
                    
                    this.websocket.onmessage = (event) => {
                        this.handleMessage(event.data);
                    };
                    
                    this.websocket.onclose = () => {
                        this.isConnected = false;
                        this.updateStatus('连接已断开', 'disconnected');
                        this.connectBtn.textContent = '连接服务器';
                        this.connectBtn.className = 'btn-primary';
                        this.startBtn.disabled = true;
                        this.stopBtn.disabled = true;
                    };
                    
                    this.websocket.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.addResult('❌ WebSocket连接错误', true);
                    };
                    
                } catch (error) {
                    console.error('连接失败:', error);
                    this.updateStatus('连接失败', 'disconnected');
                    this.addResult(`❌ 连接失败: ${error.message}`, true);
                }
            }
            
            disconnect() {
                if (this.websocket) {
                    this.websocket.close();
                }
                
                if (this.isRecording) {
                    this.stopRecording();
                }
            }
            
            async startRecording() {
                try {
                    this.audioStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true
                        }
                    });
                    
                    this.mediaRecorder = new MediaRecorder(this.audioStream, {
                        mimeType: 'audio/webm;codecs=opus'
                    });
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0 && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                            // 发送音频数据
                            this.websocket.send(event.data);
                        }
                    };
                    
                    this.mediaRecorder.start(100); // 每100ms发送一次数据
                    this.isRecording = true;
                    
                    this.updateStatus('正在录音...', 'recording');
                    this.startBtn.disabled = true;
                    this.stopBtn.disabled = false;
                    
                    this.addResult('🎤 开始录音...', true);
                    
                } catch (error) {
                    console.error('录音启动失败:', error);
                    this.addResult(`❌ 录音启动失败: ${error.message}`, true);
                }
            }
            
            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                }
                
                if (this.audioStream) {
                    this.audioStream.getTracks().forEach(track => track.stop());
                    this.audioStream = null;
                }
                
                this.updateStatus('已连接', 'connected');
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                
                this.addResult('⏹️ 录音已停止', true);
            }
            
            handleMessage(data) {
                try {
                    const result = JSON.parse(data);
                    const text = result.text || '';
                    const isFinal = result.is_final || false;
                    
                    if (text) {
                        this.addResult(text, isFinal);
                    }
                    
                } catch (error) {
                    // 可能是二进制数据
                    console.log('收到非JSON消息:', data);
                }
            }
            
            addResult(text, isFinal = false) {
                const resultItem = document.createElement('div');
                resultItem.className = `result-item ${isFinal ? 'final' : 'partial'}`;
                
                const timestamp = new Date().toLocaleTimeString();
                const status = isFinal ? '【最终】' : '【临时】';
                
                resultItem.innerHTML = `
                    <span class="timestamp">${timestamp}</span>
                    <strong>${status}</strong> ${text}
                `;
                
                this.resultsDiv.appendChild(resultItem);
                this.resultsDiv.scrollTop = this.resultsDiv.scrollHeight;
            }
            
            clearResults() {
                this.resultsDiv.innerHTML = '<h3>🗣️ 识别结果:</h3>';
            }
            
            updateStatus(message, type) {
                this.statusDiv.textContent = message;
                this.statusDiv.className = `status ${type}`;
            }
            
            async uploadFile() {
                const file = this.audioFileInput.files[0];
                if (!file) {
                    alert('请选择音频文件');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', file);
                
                const serverUrl = this.serverUrlInput.value.replace('ws://', 'http://').replace('/ws/asr', '');
                const uploadUrl = `${serverUrl}/fileASR`;
                
                try {
                    this.addResult('📤 正在上传文件...', true);
                    
                    const response = await fetch(uploadUrl, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    this.addResult(`📥 文件识别结果: ${JSON.stringify(result, null, 2)}`, true);
                    
                } catch (error) {
                    console.error('文件上传失败:', error);
                    this.addResult(`❌ 文件上传失败: ${error.message}`, true);
                }
            }
        }
        
        // 初始化客户端
        document.addEventListener('DOMContentLoaded', () => {
            new FunASRWebSocketClient();
        });
    </script>
</body>
</html>
