"""
AI服务平台客户端SDK
为业务项目提供统一的AI服务接入
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

import httpx
from pydantic import BaseModel


class ServiceType(Enum):
    """服务类型"""
    OCR = "ocr"
    TTS = "tts"
    ASR = "asr"
    LLM = "llm"
    TRANSLATE = "translate"


@dataclass
class AIResult:
    """AI服务结果"""
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    cost_info: Dict[str, Any]
    error: Optional[str] = None


class OCRResult(AIResult):
    """OCR结果"""
    @property
    def text(self) -> str:
        """提取的文本"""
        return self.data.get("text", "")
    
    @property
    def regions(self) -> List[Dict]:
        """文本区域"""
        return self.data.get("regions", [])
    
    @property
    def confidence(self) -> float:
        """置信度"""
        return self.data.get("confidence", 0.0)


class TTSResult(AIResult):
    """TTS结果"""
    @property
    def audio_url(self) -> str:
        """音频URL"""
        return self.data.get("audio_url", "")
    
    @property
    def audio_data(self) -> bytes:
        """音频数据"""
        return self.data.get("audio_data", b"")
    
    @property
    def duration(self) -> float:
        """音频时长"""
        return self.data.get("duration", 0.0)


class ASRResult(AIResult):
    """ASR结果"""
    @property
    def text(self) -> str:
        """识别的文本"""
        return self.data.get("text", "")
    
    @property
    def segments(self) -> List[Dict]:
        """分段结果"""
        return self.data.get("segments", [])
    
    @property
    def confidence(self) -> float:
        """置信度"""
        return self.data.get("confidence", 0.0)


class LLMResult(AIResult):
    """LLM结果"""
    @property
    def content(self) -> str:
        """生成的内容"""
        return self.data.get("content", "")
    
    @property
    def usage(self) -> Dict[str, int]:
        """Token使用情况"""
        return self.data.get("usage", {})
    
    @property
    def finish_reason(self) -> str:
        """完成原因"""
        return self.data.get("finish_reason", "")


class TranslateResult(AIResult):
    """翻译结果"""
    @property
    def translated_text(self) -> str:
        """翻译后的文本"""
        return self.data.get("translated_text", "")
    
    @property
    def source_language(self) -> str:
        """源语言"""
        return self.data.get("source_language", "")
    
    @property
    def target_language(self) -> str:
        """目标语言"""
        return self.data.get("target_language", "")


class AIServicesClient:
    """AI服务平台客户端"""
    
    def __init__(
        self, 
        base_url: str, 
        api_key: str, 
        tenant_id: str,
        business_domain: str = "general",
        timeout: int = 30
    ):
        """
        初始化客户端
        
        Args:
            base_url: AI服务平台地址
            api_key: API密钥
            tenant_id: 租户ID
            business_domain: 业务域 (education, medical, general)
            timeout: 请求超时时间
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.tenant_id = tenant_id
        self.business_domain = business_domain
        
        self.session = httpx.AsyncClient(
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
        )
    
    async def close(self):
        """关闭客户端"""
        await self.session.aclose()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def _call_service(
        self, 
        service_type: ServiceType, 
        model: str, 
        input_data: Dict[str, Any],
        options: Dict[str, Any] = None
    ) -> AIResult:
        """调用AI服务"""
        request_data = {
            "service_type": service_type.value,
            "model": model,
            "input_data": input_data,
            "options": options or {},
            "business_context": {
                "domain": self.business_domain,
                "tenant_id": self.tenant_id
            }
        }
        
        try:
            response = await self.session.post(
                f"{self.base_url}/api/v1/ai/process",
                json=request_data
            )
            response.raise_for_status()
            
            result = response.json()
            
            # 根据服务类型返回相应的结果对象
            result_class = {
                ServiceType.OCR: OCRResult,
                ServiceType.TTS: TTSResult,
                ServiceType.ASR: ASRResult,
                ServiceType.LLM: LLMResult,
                ServiceType.TRANSLATE: TranslateResult
            }.get(service_type, AIResult)
            
            return result_class(
                success=result["success"],
                data=result["data"],
                metadata=result["metadata"],
                cost_info=result["cost_info"]
            )
            
        except httpx.HTTPStatusError as e:
            error_detail = e.response.json() if e.response.content else str(e)
            return AIResult(
                success=False,
                data={},
                metadata={},
                cost_info={},
                error=str(error_detail)
            )
        except Exception as e:
            return AIResult(
                success=False,
                data={},
                metadata={},
                cost_info={},
                error=str(e)
            )
    
    # OCR服务
    async def ocr(
        self, 
        image_data: Union[bytes, str], 
        model: str = "auto",
        language: str = "auto",
        enhance_formula: bool = False
    ) -> OCRResult:
        """
        光学字符识别
        
        Args:
            image_data: 图片数据（bytes）或图片URL（str）
            model: 模型名称，auto为自动选择
            language: 语言，auto为自动检测
            enhance_formula: 是否增强公式识别
        """
        if isinstance(image_data, bytes):
            input_data = {"image_data": image_data.hex()}
        else:
            input_data = {"image_url": image_data}
        
        options = {
            "language": language,
            "enhance_formula": enhance_formula
        }
        
        return await self._call_service(ServiceType.OCR, model, input_data, options)
    
    # TTS服务
    async def tts(
        self, 
        text: str, 
        model: str = "auto",
        voice: str = "auto",
        speed: float = 1.0,
        pitch: float = 0.0
    ) -> TTSResult:
        """
        文本转语音
        
        Args:
            text: 要转换的文本
            model: 模型名称
            voice: 声音类型
            speed: 语速
            pitch: 音调
        """
        input_data = {"text": text}
        options = {
            "voice": voice,
            "speed": speed,
            "pitch": pitch
        }
        
        return await self._call_service(ServiceType.TTS, model, input_data, options)
    
    # ASR服务
    async def asr(
        self, 
        audio_data: Union[bytes, str], 
        model: str = "auto",
        language: str = "auto",
        format: str = "wav"
    ) -> ASRResult:
        """
        语音识别
        
        Args:
            audio_data: 音频数据（bytes）或音频URL（str）
            model: 模型名称
            language: 语言
            format: 音频格式
        """
        if isinstance(audio_data, bytes):
            input_data = {"audio_data": audio_data.hex()}
        else:
            input_data = {"audio_url": audio_data}
        
        options = {
            "language": language,
            "format": format
        }
        
        return await self._call_service(ServiceType.ASR, model, input_data, options)
    
    # LLM服务
    async def chat(
        self, 
        messages: List[Dict[str, str]], 
        model: str = "auto",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        stream: bool = False
    ) -> LLMResult:
        """
        大语言模型对话
        
        Args:
            messages: 对话消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否流式返回
        """
        input_data = {"messages": messages}
        options = {
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }
        
        return await self._call_service(ServiceType.LLM, model, input_data, options)
    
    async def complete(
        self, 
        prompt: str, 
        model: str = "auto",
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> LLMResult:
        """
        文本补全
        
        Args:
            prompt: 提示文本
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
        """
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(messages, model, temperature, max_tokens)
    
    # 翻译服务
    async def translate(
        self, 
        text: str, 
        target_language: str,
        source_language: str = "auto",
        model: str = "auto"
    ) -> TranslateResult:
        """
        文本翻译
        
        Args:
            text: 要翻译的文本
            target_language: 目标语言
            source_language: 源语言，auto为自动检测
            model: 翻译模型
        """
        input_data = {"text": text}
        options = {
            "source_language": source_language,
            "target_language": target_language
        }
        
        return await self._call_service(ServiceType.TRANSLATE, model, input_data, options)
    
    # 批量处理
    async def batch_process(
        self, 
        requests: List[Dict[str, Any]]
    ) -> List[AIResult]:
        """
        批量处理请求
        
        Args:
            requests: 请求列表，每个请求包含service_type, model, input_data, options
        """
        tasks = []
        for req in requests:
            service_type = ServiceType(req["service_type"])
            task = self._call_service(
                service_type,
                req.get("model", "auto"),
                req["input_data"],
                req.get("options", {})
            )
            tasks.append(task)
        
        return await asyncio.gather(*tasks)
    
    # 获取服务信息
    async def get_available_services(self) -> Dict[str, Any]:
        """获取可用服务列表"""
        try:
            response = await self.session.get(f"{self.base_url}/api/v1/services")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    async def get_quota_status(self) -> Dict[str, Any]:
        """获取配额状态"""
        try:
            response = await self.session.get(f"{self.base_url}/api/v1/quota")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}


# 便捷函数
async def create_client(
    base_url: str, 
    api_key: str, 
    tenant_id: str,
    business_domain: str = "general"
) -> AIServicesClient:
    """创建AI服务客户端"""
    return AIServicesClient(base_url, api_key, tenant_id, business_domain)


# 教育领域专用客户端
class EducationAIClient(AIServicesClient):
    """教育领域AI客户端"""
    
    def __init__(self, base_url: str, api_key: str):
        super().__init__(base_url, api_key, "matrix_education", "education")
    
    async def process_homework_image(self, image_data: bytes) -> OCRResult:
        """处理作业图片"""
        return await self.ocr(
            image_data, 
            model="paddle_ocr",
            enhance_formula=True
        )
    
    async def generate_voice_explanation(self, text: str) -> TTSResult:
        """生成语音解释"""
        return await self.tts(
            text,
            model="edge_tts",
            voice="zh-CN-XiaoxiaoNeural",
            speed=0.9
        )


# 医疗领域专用客户端
class MedicalAIClient(AIServicesClient):
    """医疗领域AI客户端"""
    
    def __init__(self, base_url: str, api_key: str):
        super().__init__(base_url, api_key, "medical_platform", "medical")
    
    async def process_medical_image(self, image_data: bytes) -> OCRResult:
        """处理医疗图像"""
        return await self.ocr(
            image_data,
            model="paddle_ocr",
            language="zh-en"
        )
    
    async def analyze_medical_text(self, text: str) -> LLMResult:
        """分析医疗文本"""
        messages = [
            {"role": "system", "content": "你是一个专业的医疗AI助手。"},
            {"role": "user", "content": f"请分析以下医疗文本：{text}"}
        ]
        return await self.chat(messages, model="qwen3-32b")
