#!/usr/bin/env python3
"""
TTS微服务性能测试脚本
测试Gunicorn多进程部署的并发性能
"""

import asyncio
import aiohttp
import time
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
import argparse


class TTSPerformanceTest:
    def __init__(self, base_url="http://localhost:8000", concurrent_users=100):
        self.base_url = base_url
        self.concurrent_users = concurrent_users
        self.results = []
        
    async def single_tts_request(self, session, text, request_id):
        """单个TTS请求"""
        start_time = time.time()
        
        try:
            payload = {
                "text": f"{text} - 请求{request_id}",
                "voice": "zh-CN-XiaoxiaoNeural",
                "options": {
                    "speed": 1.0,
                    "pitch": 0.0,
                    "volume": 1.0
                }
            }
            
            headers = {
                "Content-Type": "application/json",
                "X-Tenant-ID": f"test-{request_id % 10}"
            }
            
            async with session.post(
                f"{self.base_url}/api/v1/tts",
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                result = await response.json()
                end_time = time.time()
                
                return {
                    "request_id": request_id,
                    "status_code": response.status,
                    "response_time": end_time - start_time,
                    "success": response.status == 200,
                    "cache_hit": result.get("metadata", {}).get("cache_hit", False),
                    "error": None
                }
                
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "status_code": 0,
                "response_time": end_time - start_time,
                "success": False,
                "cache_hit": False,
                "error": str(e)
            }
    
    async def concurrent_test(self, duration_seconds=60):
        """并发测试"""
        print(f"🚀 开始并发测试...")
        print(f"并发用户数: {self.concurrent_users}")
        print(f"测试时长: {duration_seconds}秒")
        print(f"目标URL: {self.base_url}")
        
        connector = aiohttp.TCPConnector(
            limit=self.concurrent_users * 2,
            limit_per_host=self.concurrent_users * 2
        )
        
        async with aiohttp.ClientSession(connector=connector) as session:
            start_time = time.time()
            request_id = 0
            tasks = []
            
            # 测试文本列表
            test_texts = [
                "这是一个TTS性能测试",
                "人工智能语音合成技术",
                "多进程并发处理能力验证",
                "Gunicorn工作进程负载均衡",
                "高并发场景下的响应时间测试"
            ]
            
            while time.time() - start_time < duration_seconds:
                # 控制并发数
                if len(tasks) < self.concurrent_users:
                    text = test_texts[request_id % len(test_texts)]
                    task = asyncio.create_task(
                        self.single_tts_request(session, text, request_id)
                    )
                    tasks.append(task)
                    request_id += 1
                
                # 收集完成的任务
                if tasks:
                    done, pending = await asyncio.wait(
                        tasks, 
                        timeout=0.1, 
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    for task in done:
                        result = await task
                        self.results.append(result)
                        tasks.remove(task)
            
            # 等待剩余任务完成
            if tasks:
                remaining_results = await asyncio.gather(*tasks, return_exceptions=True)
                for result in remaining_results:
                    if isinstance(result, dict):
                        self.results.append(result)
        
        print(f"✅ 并发测试完成，共发送 {len(self.results)} 个请求")
    
    def analyze_results(self):
        """分析测试结果"""
        if not self.results:
            print("❌ 没有测试结果")
            return
        
        # 基本统计
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r["success"])
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100
        
        # 响应时间统计
        response_times = [r["response_time"] for r in self.results if r["success"]]
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = 0
            min_response_time = max_response_time = 0
        
        # 缓存命中率
        cache_hits = sum(1 for r in self.results if r.get("cache_hit", False))
        cache_hit_rate = (cache_hits / total_requests) * 100 if total_requests > 0 else 0
        
        # QPS计算
        if response_times:
            test_duration = max(response_times) if response_times else 1
            qps = successful_requests / test_duration if test_duration > 0 else 0
        else:
            qps = 0
        
        # 错误统计
        error_types = {}
        for result in self.results:
            if not result["success"] and result["error"]:
                error_type = type(result["error"]).__name__
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        # 输出结果
        print("\n" + "="*60)
        print("📊 TTS微服务性能测试报告")
        print("="*60)
        
        print(f"\n📈 基本统计:")
        print(f"  总请求数:     {total_requests:,}")
        print(f"  成功请求数:   {successful_requests:,}")
        print(f"  失败请求数:   {failed_requests:,}")
        print(f"  成功率:       {success_rate:.2f}%")
        print(f"  QPS:          {qps:.2f}")
        
        print(f"\n⏱️  响应时间统计 (秒):")
        print(f"  平均响应时间: {avg_response_time:.3f}")
        print(f"  中位数:       {median_response_time:.3f}")
        print(f"  P95:          {p95_response_time:.3f}")
        print(f"  P99:          {p99_response_time:.3f}")
        print(f"  最小值:       {min_response_time:.3f}")
        print(f"  最大值:       {max_response_time:.3f}")
        
        print(f"\n💾 缓存统计:")
        print(f"  缓存命中数:   {cache_hits:,}")
        print(f"  缓存命中率:   {cache_hit_rate:.2f}%")
        
        if error_types:
            print(f"\n❌ 错误统计:")
            for error_type, count in error_types.items():
                print(f"  {error_type}: {count}")
        
        # 性能评估
        print(f"\n🎯 性能评估:")
        if success_rate >= 99:
            print("  ✅ 成功率: 优秀")
        elif success_rate >= 95:
            print("  ⚠️ 成功率: 良好")
        else:
            print("  ❌ 成功率: 需要改进")
        
        if avg_response_time <= 0.5:
            print("  ✅ 响应时间: 优秀")
        elif avg_response_time <= 1.0:
            print("  ⚠️ 响应时间: 良好")
        else:
            print("  ❌ 响应时间: 需要改进")
        
        if qps >= 1000:
            print("  ✅ QPS: 优秀")
        elif qps >= 500:
            print("  ⚠️ QPS: 良好")
        else:
            print("  ❌ QPS: 需要改进")


async def main():
    parser = argparse.ArgumentParser(description="TTS微服务性能测试")
    parser.add_argument("--url", default="http://localhost:8000", help="测试URL")
    parser.add_argument("--concurrent", type=int, default=100, help="并发用户数")
    parser.add_argument("--duration", type=int, default=60, help="测试时长(秒)")
    
    args = parser.parse_args()
    
    print("🧪 TTS微服务Gunicorn多进程性能测试")
    print("="*50)
    
    tester = TTSPerformanceTest(args.url, args.concurrent)
    
    try:
        await tester.concurrent_test(args.duration)
        tester.analyze_results()
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        if tester.results:
            tester.analyze_results()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
