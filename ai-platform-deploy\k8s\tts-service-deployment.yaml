apiVersion: apps/v1
kind: Deployment
metadata:
  name: tts-service
  namespace: ai-platform
  labels:
    app: tts-service
    tier: microservice
spec:
  replicas: 10  # 10个副本分布在10台服务器上
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 3
      maxUnavailable: 2
  selector:
    matchLabels:
      app: tts-service
  template:
    metadata:
      labels:
        app: tts-service
        tier: microservice
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - tts-service
              topologyKey: kubernetes.io/hostname
      containers:
      - name: tts-service
        image: yourorg/ai-tts-service:latest
        ports:
        - containerPort: 8002
          name: http
        env:
        - name: REDIS_URL
          value: "redis://redis-cluster:6379"
        - name: TTS_BACKENDS
          value: "http://tts-gpu-cluster:8080"  # 指向算力集群
        - name: LOG_LEVEL
          value: "INFO"
        - name: WORKERS
          value: "2"
        resources:
          requests:
            memory: "1Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "4000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
      nodeSelector:
        node-type: "microservice"  # 部署到微服务节点

---
apiVersion: v1
kind: Service
metadata:
  name: tts-service
  namespace: ai-platform
  labels:
    app: tts-service
spec:
  type: ClusterIP
  ports:
  - port: 8002
    targetPort: 8002
    protocol: TCP
    name: http
  selector:
    app: tts-service

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tts-service-hpa
  namespace: ai-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tts-service
  minReplicas: 8
  maxReplicas: 30
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: requests_per_second
      target:
        type: AverageValue
        averageValue: "1000"  # 每个Pod处理1000 RPS
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
