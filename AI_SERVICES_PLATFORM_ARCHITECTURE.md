# AI基础服务平台架构设计

## 🎯 项目背景

将OCR、ASR、TTS、大模型、翻译等基础AI服务从业务项目中拆分，构建独立的AI服务平台，供教育、医疗等多个业务项目共享使用。

## 🏗️ 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    业务应用层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   教育平台      │    医疗平台     │    其他业务平台         │
│   (Matrix)      │   (Medical)     │    (Future)             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                  AI服务网关层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │ 负载均衡    │ │ 服务发现    │ │ 统一认证 & 限流         ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                  AI基础服务层                               │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐│
│ │   OCR   │ │   TTS   │ │   ASR   │ │   LLM   │ │ 翻译服务││
│ │  服务   │ │  服务   │ │  服务   │ │  服务   │ │        ││
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                  AI算法引擎层                               │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐│
│ │PaddleOCR│ │EdgeTTS  │ │FunASR   │ │Qwen3    │ │百度翻译 ││
│ │Tesseract│ │CosyVoice│ │Whisper  │ │GPT      │ │谷歌翻译 ││
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 📦 项目拆分方案

### 1. AI服务平台 (ai-services-platform)
**独立部署的AI基础服务平台**

```
ai-services-platform/
├── gateway/                    # API网关
│   ├── main.py
│   ├── routers/
│   ├── middleware/
│   └── config/
├── services/                   # 微服务
│   ├── ocr-service/
│   ├── tts-service/
│   ├── asr-service/
│   ├── llm-service/
│   └── translate-service/
├── shared/                     # 共享组件
│   ├── auth/
│   ├── cache/
│   ├── monitoring/
│   └── utils/
├── infrastructure/             # 基础设施
│   ├── docker/
│   ├── kubernetes/
│   └── monitoring/
└── docs/                      # 文档
```

### 2. 教育平台 (matrix-education)
**专注教育业务逻辑**

```
matrix-education/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── practise_generate.py    # 出题服务
│   │       ├── practise_analysis.py    # 题目分析
│   │       └── note_summarize.py       # 笔记总结
│   ├── services/
│   │   ├── education_agent.py          # 教育智能体
│   │   └── ai_client.py               # AI服务客户端
│   └── models/
└── config/
    └── ai_services.yaml               # AI服务配置
```

### 3. 医疗平台 (medical-platform)
**专注医疗业务逻辑**

```
medical-platform/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── diagnosis_assist.py     # 诊断辅助
│   │       ├── medical_record.py       # 病历处理
│   │       └── drug_analysis.py        # 药物分析
│   ├── services/
│   │   ├── medical_agent.py           # 医疗智能体
│   │   └── ai_client.py               # AI服务客户端
│   └── models/
└── config/
    └── ai_services.yaml               # AI服务配置
```

## 🔧 技术实现方案

### 1. AI服务平台核心特性

#### A. 统一API接口
```python
# 标准化的AI服务接口
class AIServiceRequest(BaseModel):
    service_type: str  # ocr, tts, asr, llm, translate
    model: str
    input_data: Dict[str, Any]
    options: Dict[str, Any] = {}
    business_context: Dict[str, Any] = {}  # 业务上下文

class AIServiceResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    cost_info: Dict[str, Any]  # 成本信息
```

#### B. 多租户支持
```python
# 租户配置
class TenantConfig(BaseModel):
    tenant_id: str
    name: str
    quota_limits: Dict[str, int]  # 配额限制
    allowed_services: List[str]   # 允许的服务
    priority_level: int           # 优先级
    cost_model: str              # 计费模式
```

#### C. 智能路由
```python
# 基于业务场景的智能路由
class ServiceRouter:
    def route_request(self, request: AIServiceRequest, tenant: str) -> str:
        """根据业务场景、负载、成本选择最优服务"""
        if request.business_context.get("domain") == "education":
            # 教育场景优化
            return self.select_education_optimized_service(request)
        elif request.business_context.get("domain") == "medical":
            # 医疗场景优化
            return self.select_medical_optimized_service(request)
        else:
            # 通用场景
            return self.select_general_service(request)
```

### 2. 业务平台集成

#### A. AI服务客户端SDK
```python
# 统一的AI服务客户端
class AIServicesClient:
    def __init__(self, base_url: str, api_key: str, tenant_id: str):
        self.base_url = base_url
        self.api_key = api_key
        self.tenant_id = tenant_id
        self.session = httpx.AsyncClient()
    
    async def ocr(self, image_data: bytes, model: str = "auto") -> OCRResult:
        """OCR服务"""
        request = AIServiceRequest(
            service_type="ocr",
            model=model,
            input_data={"image": image_data},
            business_context={"domain": "education"}
        )
        return await self._call_service(request)
    
    async def tts(self, text: str, voice: str = "auto") -> TTSResult:
        """TTS服务"""
        request = AIServiceRequest(
            service_type="tts",
            model="edge_tts",
            input_data={"text": text, "voice": voice},
            business_context={"domain": "education"}
        )
        return await self._call_service(request)
    
    async def llm_chat(self, messages: List[dict], model: str = "auto") -> LLMResult:
        """LLM对话服务"""
        request = AIServiceRequest(
            service_type="llm",
            model=model,
            input_data={"messages": messages},
            business_context={"domain": "education"}
        )
        return await self._call_service(request)
```

#### B. 配置管理
```yaml
# config/ai_services.yaml
ai_services:
  base_url: "https://ai-platform.your-domain.com"
  api_key: "${AI_SERVICES_API_KEY}"
  tenant_id: "matrix_education"
  
  # 服务配置
  services:
    ocr:
      default_model: "paddle_ocr"
      fallback_models: ["tesseract", "easyocr"]
      cache_ttl: 3600
    
    tts:
      default_model: "edge_tts"
      fallback_models: ["cosyvoice"]
      cache_ttl: 7200
    
    llm:
      default_model: "qwen3-8b"
      fallback_models: ["qwen3-32b"]
      cache_ttl: 1800
  
  # 业务优化
  business_optimization:
    education:
      ocr:
        prefer_chinese: true
        enhance_formula: true
      tts:
        prefer_child_voice: true
        speed_adjustment: 0.9
```

## 🚀 迁移实施计划

### Phase 1: AI服务平台搭建 (2-3周)

#### 1.1 创建AI服务平台项目
```bash
# 创建新项目
mkdir ai-services-platform
cd ai-services-platform

# 初始化项目结构
mkdir -p {gateway,services,shared,infrastructure,docs}
mkdir -p services/{ocr-service,tts-service,asr-service,llm-service,translate-service}
```

#### 1.2 实现核心网关
- 统一API接口
- 多租户认证
- 负载均衡
- 监控指标

#### 1.3 迁移基础服务
- 从现有项目复制服务代码
- 标准化接口格式
- 添加多租户支持

### Phase 2: 业务平台改造 (1-2周)

#### 2.1 创建AI客户端SDK
```python
# 在教育平台中
from ai_services_sdk import AIServicesClient

class EducationAIService:
    def __init__(self):
        self.ai_client = AIServicesClient(
            base_url=settings.AI_SERVICES_URL,
            api_key=settings.AI_SERVICES_API_KEY,
            tenant_id="matrix_education"
        )
    
    async def process_homework_image(self, image: bytes) -> dict:
        """处理作业图片"""
        # 使用AI平台的OCR服务
        ocr_result = await self.ai_client.ocr(
            image_data=image,
            model="paddle_ocr",
            options={"enhance_formula": True}
        )
        return ocr_result
```

#### 2.2 替换现有调用
- 将直接的后端调用替换为AI客户端调用
- 保持业务逻辑不变
- 添加错误处理和降级

### Phase 3: 灰度切换 (1周)

#### 3.1 双写模式
```python
class HybridAIService:
    def __init__(self):
        self.old_service = OldOCRService()
        self.new_service = AIServicesClient(...)
        self.use_new_service = settings.USE_NEW_AI_PLATFORM
    
    async def ocr(self, image: bytes) -> dict:
        if self.use_new_service:
            try:
                return await self.new_service.ocr(image)
            except Exception as e:
                logger.error(f"New service failed: {e}")
                # 降级到旧服务
                return await self.old_service.process(image)
        else:
            return await self.old_service.process(image)
```

#### 3.2 逐步切换
- 先切换非关键功能
- 监控性能和错误率
- 逐步增加新平台流量比例

### Phase 4: 完全迁移 (1周)

#### 4.1 移除旧代码
- 删除原有的转发服务
- 清理不需要的依赖
- 更新文档

#### 4.2 优化配置
- 调整缓存策略
- 优化网络配置
- 完善监控

## 💰 成本效益分析

### 优势
1. **资源共享**: 多个项目共享AI服务，降低总体成本
2. **统一管理**: 集中管理AI模型和配置
3. **专业化**: AI团队专注AI服务优化
4. **可扩展**: 新项目可快速接入
5. **成本控制**: 统一计费和配额管理

### 投入
1. **开发成本**: 2-3人月
2. **基础设施**: 独立的AI服务集群
3. **运维成本**: 额外的监控和维护

### 预期收益
1. **开发效率**: 新项目接入时间从2周缩短到2天
2. **运维效率**: 统一运维，降低50%运维成本
3. **资源利用**: 提高30%资源利用率
4. **业务专注**: 业务团队专注业务逻辑

## 🎯 关键建议

1. **渐进式迁移**: 避免大爆炸式改造，确保业务连续性
2. **向后兼容**: 保持API兼容性，便于回滚
3. **监控先行**: 完善的监控体系，及时发现问题
4. **文档完善**: 详细的接入文档和SDK文档
5. **团队协作**: AI平台团队和业务团队密切配合

这个方案可以让您的AI服务从项目级别提升到平台级别，为未来的多项目发展奠定坚实基础。
