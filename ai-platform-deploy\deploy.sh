#!/bin/bash

# AI服务平台一键部署脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 AI服务平台部署脚本${NC}"
echo "================================"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker环境检查通过${NC}"

# 创建环境配置文件
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 创建环境配置文件...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}⚠️  请编辑 .env 文件，配置您的后端服务地址${NC}"
    echo -e "${YELLOW}⚠️  特别是 TTS_API_KEY 和 TTS_BASE_URL${NC}"
fi

# 拉取最新镜像
echo -e "${YELLOW}📥 拉取最新镜像...${NC}"
docker-compose pull

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"

services=(
    "http://localhost:8000/health:AI网关"
    "http://localhost:8002/health:TTS服务"
    "http://localhost:8100/health:教育平台"
    "http://localhost:8200/health:医疗平台"
)

all_healthy=true

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-2)
    name=$(echo $service | cut -d: -f3)
    
    if curl -f -s $url > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $name 健康检查通过${NC}"
    else
        echo -e "${RED}❌ $name 健康检查失败${NC}"
        all_healthy=false
    fi
done

# 显示部署结果
echo ""
if [ "$all_healthy" = true ]; then
    echo -e "${GREEN}🎉 AI服务平台部署成功！${NC}"
else
    echo -e "${YELLOW}⚠️  部分服务可能还在启动中，请稍后再试${NC}"
fi

echo ""
echo "📋 服务地址："
echo "  🌐 AI服务网关:     http://localhost:8000"
echo "  🔧 TTS微服务:      http://localhost:8002"
echo "  📚 教育平台:       http://localhost:8100"
echo "  🏥 医疗平台:       http://localhost:8200"
echo ""
echo "📖 API文档："
echo "  🔗 AI网关API:      http://localhost:8000/docs"
echo "  🔗 TTS服务API:     http://localhost:8002/docs"
echo "  🔗 教育平台API:    http://localhost:8100/docs"
echo "  🔗 医疗平台API:    http://localhost:8200/docs"
echo ""
echo "🧪 快速测试："
echo "  curl http://localhost:8000/health"
echo "  curl http://localhost:8000/api/v1/services"
echo ""
echo "🔧 管理命令："
echo "  查看状态: docker-compose ps"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  更新服务: docker-compose pull && docker-compose up -d"
echo ""
echo -e "${GREEN}✨ AI服务平台已就绪！${NC}"
