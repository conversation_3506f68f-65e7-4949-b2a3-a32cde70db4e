# AI TTS Service - 文本转语音微服务

## 📋 项目简介

独立的TTS微服务，支持多种语音模型和说话人，提供高质量的文本转语音功能。

## 🚀 快速开始

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### Docker部署
```bash
# 构建镜像
docker build -t ai-tts-service .

# 运行容器
docker run -p 8002:8002 \
  -e REDIS_URL=redis://localhost:6379 \
  ai-tts-service
```

## 📖 API文档

启动服务后访问：http://localhost:8002/docs

### 主要接口

#### 文本转语音
```bash
POST /text_to_speech
Content-Type: application/json

{
  "text": "要转换的文本",
  "speaker": "zh-CN-XiaoxiaoNeural",
  "speed": 1.0,
  "pitch": 0.0,
  "volume": 1.0,
  "stream": false
}
```

#### 查询语音列表
```bash
POST /query_speakers
Content-Type: application/json

{
  "gender": "Female",
  "model": "edge",
  "language": "zh-CN"
}
```

#### 统一处理接口（兼容网关）
```bash
POST /process
Content-Type: application/json

{
  "text": "Hello World",
  "speaker": "zh-CN-XiaoxiaoNeural",
  "options": {
    "speed": 1.0,
    "pitch": 0.0,
    "volume": 1.0
  }
}
```

## ⚙️ 配置说明

### 环境变量
- `REDIS_URL`: Redis连接地址
- `DEBUG`: 调试模式
- `LOG_LEVEL`: 日志级别

### 后端配置
在 `main.py` 中修改 `TTS_BACKENDS` 配置：

```python
TTS_BACKENDS = [
    {
        "name": "matrix_tts",
        "base_url": "https://your-tts-server.com",
        "api_key": "your-api-key",
        "weight": 1,
        "status": "active"
    }
]
```

## 🔧 功能特性

- ✅ 多种语音模型支持
- ✅ 语音参数调节（语速、音调、音量）
- ✅ 流式和非流式输出
- ✅ Redis缓存
- ✅ 语音列表查询
- ✅ 健康检查
- ✅ 错误处理

## 📊 支持的语音

### 中文语音
- zh-CN-XiaoxiaoNeural (晓晓-女声)
- zh-CN-YunxiNeural (云希-男声)
- zh-CN-YunyangNeural (云扬-男声)

### 英文语音
- en-US-AriaNeural (Aria-女声)
- en-US-GuyNeural (Guy-男声)

## 🧪 测试

```bash
# 健康检查
curl http://localhost:8002/health

# 测试TTS
curl -X POST http://localhost:8002/text_to_speech \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello World",
    "speaker": "zh-CN-XiaoxiaoNeural"
  }'

# 查询语音列表
curl -X POST http://localhost:8002/query_speakers \
  -H "Content-Type: application/json" \
  -d '{
    "gender": "Female",
    "model": "edge"
  }'
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request
