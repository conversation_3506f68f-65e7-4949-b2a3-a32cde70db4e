"""
教育平台 - 基于AI服务平台
"""

import os
import sys
import time
from typing import Dict, Any, List

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 导入AI SDK
sys.path.append('/app/sdk')
from ai_client import AIClient


# 请求模型
class GenerateQuestionRequest(BaseModel):
    """出题请求"""
    topic: str = Field(..., description="知识点")
    subject: str = Field(..., description="学科")
    difficulty: str = Field(default="medium", description="难度")
    count: int = Field(default=5, description="题目数量")


# 创建应用
app = FastAPI(
    title="Education Platform",
    description="教育平台 - 基于AI服务平台",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化AI客户端
ai_gateway_url = os.getenv("AI_GATEWAY_URL", "http://gateway:8000")
ai_client = AIClient(ai_gateway_url, tenant_id="education")


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    print("🚀 教育平台启动中...")
    
    # 测试AI服务连接
    try:
        services = await ai_client.get_services()
        print(f"✅ AI服务连接成功: {services}")
    except Exception as e:
        print(f"⚠️ AI服务连接失败: {e}")
    
    print("✅ 教育平台启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    await ai_client.close()


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "education-app",
        "version": "1.0.0",
        "timestamp": time.time()
    }


@app.post("/api/homework/ocr")
async def process_homework(file: UploadFile = File(...)):
    """作业图片识别"""
    try:
        # 读取图片
        image_data = await file.read()
        
        # 调用OCR服务
        result = await ai_client.ocr(image_data=image_data)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"OCR失败: {result.error}")
        
        return {
            "success": True,
            "text": result.text,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/questions/generate")
async def generate_questions(request: GenerateQuestionRequest):
    """智能出题"""
    try:
        # 构建提示
        prompt = f"""
        请生成{request.count}道关于"{request.topic}"的{request.subject}题目，难度为{request.difficulty}。
        
        要求：
        1. 题目内容准确
        2. 难度适中
        3. 包含答案
        4. 格式规范
        
        请以JSON格式返回题目列表。
        """
        
        # 调用LLM服务
        result = await ai_client.complete(prompt)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"生成失败: {result.error}")
        
        return {
            "success": True,
            "questions": result.content,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/voice/generate")
async def generate_voice(text: str = Form(...)):
    """生成语音解释"""
    try:
        # 调用TTS服务
        result = await ai_client.tts(text, voice="zh-CN-XiaoxiaoNeural")
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"TTS失败: {result.error}")
        
        return {
            "success": True,
            "audio_url": result.audio_url,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/chat")
async def chat_with_ai(message: str = Form(...)):
    """AI助教对话"""
    try:
        messages = [
            {"role": "system", "content": "你是一个专业的教育AI助手，帮助学生学习。"},
            {"role": "user", "content": message}
        ]
        
        # 调用LLM服务
        result = await ai_client.chat(messages)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"对话失败: {result.error}")
        
        return {
            "success": True,
            "reply": result.content,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/stats")
async def get_stats():
    """获取统计信息"""
    try:
        services = await ai_client.get_services()
        return {
            "platform": "education",
            "ai_services": services,
            "uptime": time.time() - getattr(app.state, 'start_time', time.time())
        }
    except Exception as e:
        return {"error": str(e)}


# 记录启动时间
@app.middleware("http")
async def add_startup_time(request, call_next):
    if not hasattr(app.state, 'start_time'):
        app.state.start_time = time.time()
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8100,
        reload=True
    )
