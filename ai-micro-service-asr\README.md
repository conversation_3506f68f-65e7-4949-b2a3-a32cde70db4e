# AI ASR Service - 语音识别微服务

## 📋 项目简介

独立的ASR微服务，支持多种音频格式和语音识别模型，提供高质量的语音转文字功能。

## 🚀 快速开始

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main.py
```

### Docker部署
```bash
# 构建镜像
docker build -t ai-micro-service-asr .

# 运行容器
docker run --name ai-micro-service-asr --network=host -it --privileged=true -p 50100:50100 \
  -e REDIS_URL=redis://**********:6379 \
  ai-micro-service-asr bash start.sh
```


### 主要接口

#### 语音识别 (JSON)
```bash
POST /recognition
Content-Type: application/json

{
  "audio_url": "https://example.com/audio.wav",
  "language": "zh",
  "model": "whisper"
}
```

#### 语音识别 (文件上传)
```bash
POST /recognition/file
Content-Type: multipart/form-data

file: <audio_file>
language: zh
model: whisper
```

#### 统一处理接口（兼容网关）
```bash
POST /process
Content-Type: application/json

{
  "audio_url": "https://example.com/audio.wav",
  "language": "zh",
  "model": "whisper",
  "options": {
    "format": "wav",
    "sample_rate": 16000,
    "channels": 1
  }
}
```


## ⚙️ 配置说明

### 环境变量
- `REDIS_URL`: Redis连接地址
- `DEBUG`: 调试模式
- `LOG_LEVEL`: 日志级别
- `WORKERS`: Gunicorn工作进程数
- `TIMEOUT`: 请求超时时间（ASR需要较长时间）

### 后端配置
在 `main.py` 中修改 `ASR_BACKENDS` 配置：

```python
ASR_BACKENDS = [
    {
        "name": "matrix_asr",
        "base_url": "https://t-matrix.nibs.ac.cn/mko",
        "api_key": "your-api-key",
        "weight": 1,
        "status": "active",
        "endpoint": "/llm/asr/recognition"
    }
]
```

## 🔧 功能特性

- ✅ 多种音频格式支持 (wav, mp3)
- ✅ 多语言识别 (中文、英文、自动检测)
- ✅ 文件上传和URL识别
- ✅ Redis缓存优化
- ✅ Gunicorn多进程部署
- ✅ 健康检查和监控
- ✅ 错误处理和重试

## 📊 支持的格式

### 音频格式
- WAV (推荐)
- MP3

### 识别语言
- zh: 中文
- en: 英文
- auto: 自动检测

### 识别模型
- funasr: 阿里FunASR模型

## 🧪 测试

```bash
# 健康检查
curl http://localhost:50100/health

# 测试语音识别 (URL)
curl -X POST http://localhost:50100/recognition \
  -H "Content-Type: application/json" \
  -d '{
    "audio_url": "https://example.com/test.wav",
    "language": "zh",
    "model": "whisper"
  }'

# 测试语音识别 (文件上传)
curl -X POST http://localhost:50100/recognition/file \
  -F "file=@test.wav" \
  -F "language=zh" \
  -F "model=whisper"


### 通过AI网关调用
```bash
curl -X POST http://localhost:8000/api/v1/asr \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: your-tenant" \
  -d '{
    "audio_url": "https://example.com/audio.wav",
    "language": "zh"
  }'
```

### 直接调用ASR服务
```bash
curl -X POST http://localhost:50100/process \
  -H "Content-Type: application/json" \
  -d '{
    "audio_url": "https://example.com/audio.wav",
    "language": "zh"
  }'
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request
