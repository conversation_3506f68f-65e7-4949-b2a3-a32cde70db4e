"""
基于 LangGraph 0.5.4 的出题服务
利用图状态机实现复杂的出题工作流
集成 LangSmith 监控和追踪
"""

import json
import asyncio
import os
from typing import Dict, Any, List, TypedDict, Annotated
from app.services.abs_service import AbsService
from app.core.teacher_manager import teacher_manager, TeacherType
from app.models.practise_analysis_model import DifficultyLevel
from app.models.practise_generate_model import PractiseType, PractiseGenerateResponseList

# LangSmith 配置
def setup_langsmith_tracing():
    """设置 LangSmith 追踪，包含错误处理"""
    try:
        os.environ.setdefault("LANGCHAIN_TRACING_V2", "true")
        os.environ.setdefault("LANGCHAIN_PROJECT", "LangGraph-Question-Generation")

        # 检查 API Key 是否有效
        api_key = os.environ.get("LANGCHAIN_API_KEY")
        if api_key and len(api_key) > 10:
            print("🔑 LangSmith API Key 已配置")
            return True
        else:
            print("⚠️ LangSmith API Key 未配置或无效，将使用本地日志模式")
            # 禁用远程追踪，只保留本地日志
            os.environ["LANGCHAIN_TRACING_V2"] = "false"
            return False
    except Exception as e:
        print(f"❌ LangSmith 配置失败: {e}")
        os.environ["LANGCHAIN_TRACING_V2"] = "false"
        return False

# 初始化 LangSmith
# langsmith_enabled = setup_langsmith_tracing()
langsmith_enabled = False

try:
    from langgraph.graph import StateGraph, START, END
    from langgraph.graph.message import add_messages
    from langgraph.prebuilt import ToolNode
    from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
    from langchain_core.tools import tool
    from langchain_openai import ChatOpenAI
    from langchain_core.runnables import RunnableConfig
    from langsmith import traceable
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    print("⚠️ LangGraph 或 LangSmith 未安装")

    # 创建空的装饰器作为回退
    def traceable(*args, **kwargs):
        def decorator(func):
            return func
        if len(args) == 1 and callable(args[0]):
            return args[0]
        return decorator
    
    def tool(func):
        return func


class QuestionGenerationState(TypedDict):
    """出题工作流状态"""
    messages: Annotated[List, add_messages]
    content: str
    subject: str
    practise_type: str
    difficulty: str
    num_of_practise: int
    knowledge_point_id: str
    teacher_info: Dict[str, Any]
    existing_questions: List[str]
    generated_questions: List[Dict[str, Any]]
    current_step: str
    error_message: str


class LangGraphPractiseService(AbsService):
    """基于 LangGraph 的出题服务"""
    
    def __init__(self):
        super().__init__()
        
        if not LANGGRAPH_AVAILABLE:
            raise ImportError("LangGraph 未安装，无法使用此服务")
        
        # 配置 LLM
        self.llm = ChatOpenAI(
            model="Qwen3-8B",
            base_url="http://61.172.167.136:10307/v1",
            api_key="EMPTY",
            temperature=0.7,
            max_tokens=4000,
        )
        
        # 创建工具
        self.tools = self._create_tools()
        
        # 创建工作流图
        self.workflow = self._create_workflow()
    
    def _create_tools(self):
        """创建工具集"""
        
        @tool
        async def query_existing_questions(knowledge_point_id: str) -> str:
            """根据知识点ID查询数据库中已有的相关题目，用于避免重复出题"""
            try:
                # 这里应该调用实际的数据库查询
                # 暂时返回模拟数据
                existing_questions = [
                    "《咏鹅》的作者是谁？",
                    "这首诗描写了什么动物？",
                    "诗中'白毛浮绿水'描写了什么场景？"
                ]
                
                result = {
                    'knowledge_point_id': knowledge_point_id,
                    'existing_questions': existing_questions,
                    'count': len(existing_questions),
                    'message': f'找到 {len(existing_questions)} 道相关题目，请确保新题目不重复'
                }
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                return json.dumps({'error': str(e)}, ensure_ascii=False)
        
        @tool
        async def analyze_subject_content(content: str, subject: str) -> str:
            """分析学科内容并推荐合适的老师和教学策略"""
            try:
                # 选择老师
                teacher_type = teacher_manager.select_teacher_by_subject(subject, content)
                teacher_profile = teacher_manager.get_teacher_profile(teacher_type)
                
                result = {
                    'recommended_teacher': teacher_profile.name,
                    'teacher_expertise': teacher_profile.expertise,
                    'teacher_skills': teacher_profile.skills[:5],
                    'subject_analysis': f'该内容属于{teacher_profile.name}的专业领域',
                    'teaching_suggestions': [
                        f'运用{teacher_profile.skills[0]}进行教学',
                        f'结合{teacher_profile.skills[1]}提升理解',
                        f'通过{teacher_profile.skills[2]}加深印象'
                    ]
                }
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                return json.dumps({'error': str(e)}, ensure_ascii=False)
        
        @tool
        async def validate_question_format(question_data: str) -> str:
            """验证题目格式是否符合要求"""
            try:
                question = json.loads(question_data)
                
                required_fields = ['practise_type', 'content', 'options', 'answer']
                missing_fields = [field for field in required_fields if field not in question]
                
                if missing_fields:
                    return json.dumps({
                        'valid': False,
                        'errors': f'缺少必需字段: {missing_fields}'
                    }, ensure_ascii=False)
                
                # 检查题型特定要求
                practise_type = question['practise_type']
                options = question['options']
                answer = question['answer']
                
                if practise_type in ['单选题', '多选题']:
                    if not options or len(options) < 2:
                        return json.dumps({
                            'valid': False,
                            'errors': '选择题必须有至少2个选项'
                        }, ensure_ascii=False)
                
                return json.dumps({
                    'valid': True,
                    'message': '题目格式验证通过'
                }, ensure_ascii=False)
                
            except Exception as e:
                return json.dumps({
                    'valid': False,
                    'errors': f'格式验证失败: {str(e)}'
                }, ensure_ascii=False)
        
        return [query_existing_questions, analyze_subject_content, validate_question_format]
    
    def _create_workflow(self):
        """创建 LangGraph 工作流"""
        
        # 创建工具节点
        tool_node = ToolNode(self.tools)
        
        # 定义工作流节点
        @traceable(name="analyze_requirements")
        async def analyze_requirements(state: QuestionGenerationState, config: RunnableConfig = None):
            """分析出题需求"""
            print(f"🎯 [LangSmith] 开始执行: analyze_requirements")
            print(f"📝 [LangSmith] 输入状态: content={state['content']}, subject={state['subject']}")

            try:
                # 选择合适的老师
                teacher_type = teacher_manager.select_teacher_by_subject(state["subject"], state["content"])
                teacher_profile = teacher_manager.get_teacher_profile(teacher_type)

                print(f"👨‍🏫 [LangSmith] 选择老师: {teacher_profile.name}")
                
                # 获取难度指导
                try:
                    difficulty_level = DifficultyLevel(state["difficulty"])
                except ValueError:
                    difficulty_level = DifficultyLevel.medium
                
                difficulty_guidance = teacher_manager.get_difficulty_guidance(teacher_type, difficulty_level)
                difficulty_example = teacher_manager.get_difficulty_examples(teacher_type, difficulty_level)
                
                # 更新状态
                state["teacher_info"] = {
                    "name": teacher_profile.name,
                    "expertise": teacher_profile.expertise,
                    "skills": teacher_profile.skills,
                    "difficulty_guidance": difficulty_guidance,
                    "difficulty_example": difficulty_example
                }
                state["current_step"] = "requirements_analyzed"

                require_analysis = f"""
已完成需求分析：
- 选择老师：{teacher_profile.name}
- 专业领域：{teacher_profile.expertise}
- 难度级别：{state["difficulty"]}
- 题目类型：{state["practise_type"]}
- 题目数量：{state["num_of_practise"]}

接下来将查询已有题目以避免重复。
                """
                
                # 添加分析消息
                analysis_message = AIMessage(content=require_analysis)
                print(f'📋 [LangSmith] 需求分析完成: {require_analysis}')
                print(f"✅ [LangSmith] analyze_requirements 完成，状态: {state['current_step']}")

                state["messages"].append(analysis_message)
                return state

            except Exception as e:
                print(f"❌ [LangSmith] analyze_requirements 失败: {str(e)}")
                state["error_message"] = f"需求分析失败: {str(e)}"
                state["current_step"] = "error"
                return state
        
        @traceable
        async def query_existing(state: QuestionGenerationState, config: RunnableConfig = None):
            """查询已有题目"""
            print(f"🔍 [LangSmith] 开始执行: query_existing")
            print(f"📝 [LangSmith] 知识点ID: {state.get('knowledge_point_id', 'None')}")

            try:
                if state["knowledge_point_id"]:
                    # 直接调用查询工具
                    query_tool = self.tools[0]  # query_existing_questions
                    result = await query_tool.ainvoke(state["knowledge_point_id"])

                    print(f"📊 [LangSmith] 查询结果: {result[:100]}...")

                    # 添加查询结果消息
                    query_message = AIMessage(content=f"已查询知识点 '{state['knowledge_point_id']}' 的已有题目：{result}")
                    state["messages"].append(query_message)

                state["current_step"] = "existing_queried"
                print(f"✅ [LangSmith] query_existing 完成，状态: {state['current_step']}")
                return state

            except Exception as e:
                print(f"❌ [LangSmith] query_existing 失败: {str(e)}")
                state["error_message"] = f"查询已有题目失败: {str(e)}"
                state["current_step"] = "error"
                return state
        
        @traceable(name="generate_questions")
        async def generate_questions(state: QuestionGenerationState, config: RunnableConfig = None):
            """生成题目"""
            print(f"🎨 [LangSmith] 开始执行: generate_questions")
            print(f"📝 [LangSmith] 题目要求: {state['practise_type']}, 难度: {state['difficulty']}, 数量: {state['num_of_practise']}")

            try:
                teacher_info = state["teacher_info"]

                # 构建生成提示
                generation_prompt = f"""
你是{teacher_info["name"]}，专门负责{teacher_info["expertise"]}相关的出题工作。

任务要求：
- 知识点：{state["content"]}
- 学科：{state["subject"]}
- 题目类型：{state["practise_type"]}
- 题目难度：{state["difficulty"]}
- 题目数量：{state["num_of_practise"]}

{teacher_info["difficulty_guidance"]}

难度示例参考：
题目示例：{teacher_info["difficulty_example"]["example"]}
设计说明：{teacher_info["difficulty_example"]["explanation"]}

请严格按照以下JSON格式生成题目：
{{
  "practise_list": [
    {{
      "practise_type": "{state["practise_type"]}",
      "content": "题目内容",
      "options": [...],  // 选择题有选项，其他题型为空数组
      "answer": {{"value": "答案"}}
    }}
  ]
}}

请确保题目质量高，符合{state["difficulty"]}难度要求，避免与已有题目重复。
                """

                print(f"📋 [LangSmith] 生成提示长度: {len(generation_prompt)} 字符")

                generation_message = HumanMessage(content=generation_prompt)
                state["messages"].append(generation_message)

                # 生成题目
                print(f"🤖 [LangSmith] 调用 LLM 生成题目...")
                response = await self.llm.ainvoke(state["messages"])
                state["messages"].append(response)

                print(f"📝 [LangSmith] LLM 响应长度: {len(response.content)} 字符")

                # 解析生成的题目
                try:
                    import re
                    json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group()
                        result = json.loads(json_str)
                        state["generated_questions"] = result.get("practise_list", [])
                        print(f"✅ [LangSmith] 成功解析 {len(state['generated_questions'])} 道题目")
                    else:
                        print(f"⚠️ [LangSmith] 未找到 JSON 格式，响应内容: {response.content[:200]}...")
                        state["generated_questions"] = []
                except Exception as e:
                    print(f"❌ [LangSmith] 题目解析失败: {e}")
                    self.logger.error(f"题目解析失败: {e}")
                    state["generated_questions"] = []

                state["current_step"] = "questions_generated"
                print(f"✅ [LangSmith] generate_questions 完成，状态: {state['current_step']}")
                return state

            except Exception as e:
                print(f"❌ [LangSmith] generate_questions 失败: {str(e)}")
                state["error_message"] = f"题目生成失败: {str(e)}"
                state["current_step"] = "error"
                return state
        
        @traceable(name="validate_questions")
        async def validate_questions(state: QuestionGenerationState, config: RunnableConfig = None):
            """验证题目质量"""
            print(f"🔍 [LangSmith] 开始执行: validate_questions")
            print(f"📝 [LangSmith] 待验证题目数量: {len(state['generated_questions'])}")

            try:
                validated_questions = []

                for i, question in enumerate(state["generated_questions"], 1):
                    print(f"🔍 [LangSmith] 验证题目 {i}: {question.get('content', '')[:50]}...")

                    # 直接调用验证工具
                    validate_tool = self.tools[2]  # validate_question_format
                    question_json = json.dumps(question, ensure_ascii=False)
                    result = await validate_tool.ainvoke(question_json)

                    # 解析验证结果
                    try:
                        validation_result = json.loads(result)
                        if validation_result.get("valid", False):
                            validated_questions.append(question)
                            print(f"✅ [LangSmith] 题目 {i} 验证通过")
                        else:
                            print(f"❌ [LangSmith] 题目 {i} 验证失败: {validation_result.get('errors', '未知错误')}")
                    except:
                        # 如果解析失败，默认通过验证
                        validated_questions.append(question)
                        print(f"⚠️ [LangSmith] 题目 {i} 验证结果解析失败，默认通过")

                state["generated_questions"] = validated_questions
                state["current_step"] = "questions_validated"

                print(f"📊 [LangSmith] 验证完成，通过 {len(validated_questions)} 道题目")

                # 添加完成消息
                completion_message = AIMessage(content=f"""
题目生成完成！
- 成功生成 {len(validated_questions)} 道题目
- 题目类型：{state["practise_type"]}
- 难度级别：{state["difficulty"]}
- 出题老师：{state["teacher_info"]["name"]}
                """)
                state["messages"].append(completion_message)

                print(f"✅ [LangSmith] validate_questions 完成，状态: {state['current_step']}")
                return state

            except Exception as e:
                print(f"❌ [LangSmith] validate_questions 失败: {str(e)}")
                state["error_message"] = f"题目验证失败: {str(e)}"
                state["current_step"] = "error"
                return state
        
        # 路由函数
        def route_next_step(state: QuestionGenerationState):
            """决定下一步操作"""
            current_step = state.get("current_step", "")
            
            if current_step == "error":
                return END
            elif current_step == "requirements_analyzed":
                return "query_existing"
            elif current_step == "existing_queried":
                return "generate_questions"
            elif current_step == "questions_generated":
                return "validate_questions"
            elif current_step == "questions_validated":
                return END
            else:
                return "analyze_requirements"
        
        # 构建图
        workflow = StateGraph(QuestionGenerationState)
        
        # 添加节点
        workflow.add_node("analyze_requirements", analyze_requirements)
        workflow.add_node("query_existing", query_existing)
        workflow.add_node("generate_questions", generate_questions)
        workflow.add_node("validate_questions", validate_questions)
        workflow.add_node("tools", tool_node)
        
        # 添加边
        workflow.add_edge(START, "analyze_requirements")
        workflow.add_conditional_edges("analyze_requirements", route_next_step)
        workflow.add_conditional_edges("query_existing", route_next_step)
        workflow.add_conditional_edges("generate_questions", route_next_step)
        workflow.add_conditional_edges("validate_questions", route_next_step)
        
        # 编译图
        return workflow.compile()

    def visualize_workflow(self, output_path: str = "workflow_graph.png"):
        """可视化工作流图"""
        try:
            # 生成图的可视化
            graph_image = self.workflow.get_graph().draw_mermaid_png()

            with open(output_path, "wb") as f:
                f.write(graph_image)

            print(f"✅ 工作流图已保存到: {output_path}")
            return output_path

        except Exception as e:
            print(f"❌ 工作流可视化失败: {e}")
            return None

    def get_workflow_mermaid(self) -> str:
        """获取工作流的 Mermaid 图表代码"""
        try:
            return self.workflow.get_graph().draw_mermaid()
        except Exception as e:
            return f"生成 Mermaid 图表失败: {e}"
    
    @traceable
    async def generate_questions_with_langgraph(self, content: str, subject: str,
                                              num_of_practise: int, practise_type: PractiseType,
                                              difficulty: str = "中等",
                                              knowledge_point_id: str = None) -> Dict[str, Any]:
        """使用 LangGraph 工作流生成题目"""

        print(f"🚀 [LangSmith] 开始 LangGraph 工作流")
        print(f"📝 [LangSmith] 输入参数: content={content}, subject={subject}, type={practise_type.value}, difficulty={difficulty}")

        try:
            # 初始化状态
            initial_state = QuestionGenerationState(
                messages=[],
                content=content,
                subject=subject,
                practise_type=practise_type.value,
                difficulty=difficulty,
                num_of_practise=num_of_practise,
                knowledge_point_id=knowledge_point_id or "",
                teacher_info={},
                existing_questions=[],
                generated_questions=[],
                current_step="start",
                error_message=""
            )

            print(f"📊 [LangSmith] 初始状态创建完成")

            # 运行工作流
            print(f"🔄 [LangSmith] 开始执行工作流...")
            final_state = await self.workflow.ainvoke(initial_state)

            print(f"🏁 [LangSmith] 工作流执行完成")
            print(f"📊 [LangSmith] 最终状态: {final_state['current_step']}")
            print(f"📝 [LangSmith] 生成题目数量: {len(final_state['generated_questions'])}")

            # 返回结果
            if final_state["error_message"]:
                print(f"❌ [LangSmith] 工作流执行失败: {final_state['error_message']}")
                return {
                    "practise_list": [],
                    "error": final_state["error_message"]
                }
            else:
                print(f"✅ [LangSmith] 工作流执行成功")
                return {
                    "practise_list": final_state["generated_questions"]
                }

        except Exception as e:
            print(f"❌ [LangSmith] 工作流异常: {str(e)}")
            self.logger.error(f"LangGraph 工作流执行失败: {e}")
            return {
                "practise_list": [],
                "error": str(e)
            }
    
    async def process(self, content: str, subject: str, num_of_practise: int,
                     practise_type: PractiseType, difficulty: str = "中等",
                     knowledge_point_id: str = None, llm_params: dict = {}) -> PractiseGenerateResponseList:
        """处理出题请求"""
        
        try:
            # 生成题目
            result = await self.generate_questions_with_langgraph(
                content=content,
                subject=subject,
                num_of_practise=num_of_practise,
                practise_type=practise_type,
                difficulty=difficulty,
                knowledge_point_id=knowledge_point_id
            )
            
            return self.wrap_response(data_dict=result)
            
        except Exception as e:
            self.logger.error(f"LangGraph 出题服务处理失败: {e}")
            return self.wrap_response({}, 500, f"出题服务处理失败: {e}")


# 工厂函数
def create_langgraph_service():
    """创建 LangGraph 出题服务实例"""
    if LANGGRAPH_AVAILABLE:
        return LangGraphPractiseService()
    else:
        raise ImportError("LangGraph 未安装，请运行: pip install langgraph langchain-openai")



