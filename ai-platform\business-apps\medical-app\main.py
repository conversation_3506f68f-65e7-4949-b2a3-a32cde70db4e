"""
医疗平台 - 基于AI服务平台
"""

import os
import sys
import time
from typing import Dict, Any, List

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 导入AI SDK
sys.path.append('/app/sdk')
from ai_client import AIClient


# 请求模型
class MedicalAnalysisRequest(BaseModel):
    """医疗分析请求"""
    symptoms: str = Field(..., description="症状描述")
    patient_info: Dict[str, Any] = Field(default_factory=dict, description="患者信息")


# 创建应用
app = FastAPI(
    title="Medical Platform",
    description="医疗平台 - 基于AI服务平台",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化AI客户端
ai_gateway_url = os.getenv("AI_GATEWAY_URL", "http://gateway:8000")
ai_client = AIClient(ai_gateway_url, tenant_id="medical")


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    print("🚀 医疗平台启动中...")
    
    # 测试AI服务连接
    try:
        services = await ai_client.get_services()
        print(f"✅ AI服务连接成功: {services}")
    except Exception as e:
        print(f"⚠️ AI服务连接失败: {e}")
    
    print("✅ 医疗平台启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    await ai_client.close()


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "medical-app",
        "version": "1.0.0",
        "timestamp": time.time()
    }


@app.post("/api/medical-record/ocr")
async def process_medical_record(file: UploadFile = File(...)):
    """病历图片识别"""
    try:
        # 读取图片
        image_data = await file.read()
        
        # 调用OCR服务
        result = await ai_client.ocr(image_data=image_data)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"OCR失败: {result.error}")
        
        return {
            "success": True,
            "text": result.text,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/diagnosis/assist")
async def diagnosis_assist(request: MedicalAnalysisRequest):
    """诊断辅助"""
    try:
        # 构建医疗提示
        prompt = f"""
        作为专业的医疗AI助手，请根据以下信息提供诊断建议：
        
        症状描述：{request.symptoms}
        患者信息：{request.patient_info}
        
        请提供：
        1. 可能的诊断
        2. 建议的检查项目
        3. 注意事项
        
        注意：这仅供参考，不能替代专业医生诊断。
        """
        
        # 调用LLM服务
        result = await ai_client.complete(prompt)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"分析失败: {result.error}")
        
        return {
            "success": True,
            "analysis": result.content,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/voice/medical-report")
async def generate_medical_voice(text: str = Form(...)):
    """生成医疗报告语音"""
    try:
        # 调用TTS服务
        result = await ai_client.tts(text, voice="zh-CN-YunxiNeural")
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"TTS失败: {result.error}")
        
        return {
            "success": True,
            "audio_url": result.audio_url,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/consultation")
async def medical_consultation(question: str = Form(...)):
    """医疗咨询"""
    try:
        messages = [
            {"role": "system", "content": "你是一个专业的医疗AI助手，提供医疗咨询服务。请注意，你的建议仅供参考，不能替代专业医生的诊断和治疗。"},
            {"role": "user", "content": question}
        ]
        
        # 调用LLM服务
        result = await ai_client.chat(messages)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"咨询失败: {result.error}")
        
        return {
            "success": True,
            "reply": result.content,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/voice/transcribe")
async def transcribe_medical_audio(file: UploadFile = File(...)):
    """医疗录音转文字"""
    try:
        # 读取音频
        audio_data = await file.read()
        
        # 调用ASR服务
        result = await ai_client.asr(audio_data=audio_data)
        
        if not result.success:
            raise HTTPException(status_code=500, detail=f"ASR失败: {result.error}")
        
        return {
            "success": True,
            "text": result.text,
            "metadata": result.metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/stats")
async def get_stats():
    """获取统计信息"""
    try:
        services = await ai_client.get_services()
        return {
            "platform": "medical",
            "ai_services": services,
            "uptime": time.time() - getattr(app.state, 'start_time', time.time())
        }
    except Exception as e:
        return {"error": str(e)}


# 记录启动时间
@app.middleware("http")
async def add_startup_time(request, call_next):
    if not hasattr(app.state, 'start_time'):
        app.state.start_time = time.time()
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8200,
        reload=True
    )
