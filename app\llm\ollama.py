import httpx
from tenacity import retry, wait_exponential
from app.core.config import settings

class OllamaClient:
    def __init__(self, endpoint: str):
        self.endpoint = endpoint

    @retry(wait=wait_exponential(min=1, max=3))
    async def generate(self, prompt: str, model: str) -> dict:
        async with httpx.AsyncClient(timeout=60) as client:
            client.headers.update({"Authorization": f"Bearer {settings.API_KEY}"})
            response = await client.post(
                f"{self.endpoint}/api/generate",
                json={
                    "model": model,
                    "prompt": prompt,
                    "stream": False,
                    "options": {"temperature": 0.0}
                }
            )
            response.raise_for_status()
            return response.json()
