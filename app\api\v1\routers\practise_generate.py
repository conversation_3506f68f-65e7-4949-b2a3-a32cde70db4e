from fastapi import APIRouter
from app.services.practise_generate_service import PractiseGenerateService
from app.models.practise_generate_model import PractiseGenerateRequest, PractiseGenerateResponseList
from app.services.langgraph_practise_service import LangGraphPractiseService

router = APIRouter()
service = PractiseGenerateService()


@router.post("/llm/matrix/practise/generate", response_model=PractiseGenerateResponseList)
async def practise_generate(request: PractiseGenerateRequest):
    """
    智能出题接口

    支持功能：
    - 多种题型：单选题、多选题、判断题、简答题、计算题等
    - 难度控制：简单、中等、困难、极难四个级别
    - 学科专业：根据学科自动选择专业老师出题
    - 精度模式：fast(快速单智能体) 或 high(高精度多智能体协作)
    - 防重复：支持知识点ID查询避免重复出题
    """
    return await service.process(
        content=request.content,
        subject=request.subject,
        num_of_practise=request.num_of_practise,
        practise_type=request.practise_type,
        difficulty=request.difficulty.value,  # 传递难度值
        precision_mode=request.precision_mode,
        knowledge_point_id=request.knowledge_point_id,
        llm_params=request.llm_params
    )
