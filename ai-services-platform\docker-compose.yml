version: '3.8'

services:
  # AI服务网关
  ai-gateway:
    build: 
      context: ./gateway
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=http://consul:8500
      - LOG_LEVEL=INFO
    depends_on:
      - redis
      - consul
    volumes:
      - ./gateway:/app
      - ./shared:/app/shared
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # OCR服务
  ocr-service:
    build:
      context: ./services/ocr-service
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=http://consul:8500
      - BACKEND_SERVERS=http://***************:20060,http://**************:10099
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/ocr-service:/app
      - ./shared:/app/shared
    command: uvicorn main:app --host 0.0.0.0 --port 8001 --workers 2
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G

  # ASR服务
  asr-service:
    build:
      context: ./services/asr-service
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=http://consul:8500
      - BACKEND_SERVERS=http://**************:10099
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/asr-service:/app
      - ./shared:/app/shared
    command: uvicorn main:app --host 0.0.0.0 --port 8002 --workers 2
    networks:
      - ai-platform
    restart: unless-stopped

  # TTS服务
  tts-service:
    build:
      context: ./services/tts-service
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=http://consul:8500
      - BACKEND_SERVERS=http://***************:20060
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/tts-service:/app
      - ./shared:/app/shared
    command: uvicorn main:app --host 0.0.0.0 --port 8003 --workers 2
    networks:
      - ai-platform
    restart: unless-stopped

  # LLM服务
  llm-service:
    build:
      context: ./services/llm-service
      dockerfile: Dockerfile
    ports:
      - "8004:8004"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=http://consul:8500
      - BACKEND_SERVERS=http://***************:20060,http://**************:10099
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/llm-service:/app
      - ./shared:/app/shared
    command: uvicorn main:app --host 0.0.0.0 --port 8004 --workers 2
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 16G
        reservations:
          cpus: '4.0'
          memory: 8G

  # 翻译服务
  translate-service:
    build:
      context: ./services/translate-service
      dockerfile: Dockerfile
    ports:
      - "8005:8005"
    environment:
      - REDIS_URL=redis://redis:6379
      - CONSUL_URL=http://consul:8500
    depends_on:
      - redis
      - consul
    volumes:
      - ./services/translate-service:/app
      - ./shared:/app/shared
    command: uvicorn main:app --host 0.0.0.0 --port 8005 --workers 2
    networks:
      - ai-platform
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 4gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./infrastructure/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G

  # Consul服务发现
  consul:
    image: consul:1.15
    ports:
      - "8500:8500"
    command: consul agent -dev -client=0.0.0.0 -ui
    volumes:
      - consul_data:/consul/data
    networks:
      - ai-platform
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
    networks:
      - ai-platform
    restart: unless-stopped

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-platform
    restart: unless-stopped

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
    networks:
      - ai-platform
    restart: unless-stopped

  # Nginx负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/conf.d:/etc/nginx/conf.d
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - ai-gateway
    networks:
      - ai-platform
    restart: unless-stopped

  # 教育平台
  matrix-education:
    build:
      context: ./business-apps/matrix-education
      dockerfile: Dockerfile
    ports:
      - "8100:8100"
    environment:
      - AI_GATEWAY_URL=http://ai-gateway:8000
      - TENANT_ID=matrix_education
      - BUSINESS_DOMAIN=education
    depends_on:
      - ai-gateway
    volumes:
      - ./business-apps/matrix-education:/app
      - ./shared/sdk:/app/ai_sdk
    command: uvicorn main:app --host 0.0.0.0 --port 8100
    networks:
      - ai-platform
    restart: unless-stopped

  # 医疗平台
  medical-platform:
    build:
      context: ./business-apps/medical-platform
      dockerfile: Dockerfile
    ports:
      - "8200:8200"
    environment:
      - AI_GATEWAY_URL=http://ai-gateway:8000
      - TENANT_ID=medical_platform
      - BUSINESS_DOMAIN=medical
    depends_on:
      - ai-gateway
    volumes:
      - ./business-apps/medical-platform:/app
      - ./shared/sdk:/app/ai_sdk
    command: uvicorn main:app --host 0.0.0.0 --port 8200
    networks:
      - ai-platform
    restart: unless-stopped

volumes:
  redis_data:
  consul_data:
  prometheus_data:
  grafana_data:

networks:
  ai-platform:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
