# FunASR官方GPU镜像额外依赖
# 官方镜像已包含: funasr, torch, fastapi, uvicorn等核心依赖

# 性能优化相关
psutil>=5.9.0                    # 系统资源监控
aiofiles>=23.2.0                 # 异步文件操作
uvloop>=0.19.0                   # 高性能事件循环

# 监控和指标
prometheus-client>=0.19.0        # Prometheus指标导出

# 日志和调试
structlog>=23.2.0                # 结构化日志

# 如果server.py中使用了其他库，请在此添加
# 例如：
# redis>=4.5.0                   # 如果使用Redis缓存
# requests>=2.31.0               # 如果需要HTTP客户端
