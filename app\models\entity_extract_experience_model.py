from pydantic import BaseModel, Field
from typing import List, Literal, Optional

class EntityExtractExperienceRequest(BaseModel):
    """
    笔记整理请求模型
    - note_list: 笔记文本列表 (必填)
    """
    text: str = Field(..., min_length=1, example=["这是文本"])
    llm_params: dict = Field(
        {},
        example={
            "model": "Qwen2-7B-Instruct",
            "stream": False,
            "max_tokens": 1000,
            "temperature": 0.0,
            "top_p": 1.0,
        },
        description="llm模型参数"
    )


# 评分响应模型
class EntityExtractExperienceResponse(BaseModel):
    work_experience: list = Field(..., example="翻译和抽取好的文本")


