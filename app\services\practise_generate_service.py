from fastapi import HTTPException
from app.llm.vllm import VLL<PERSON>lient
from app.services.abs_service import AbsService
from app.core.config import settings
import app.utils.prompts as prompts
from app.models.practise_generate_model import (  # 前一步设计的模型类
    PractiseGenerateResponseList
)
from app.models.practise_analysis_model import PractiseType, DifficultyLevel

# AutoGen 高精度模式导入
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import DiGraphBuilder, GraphFlow
from autogen_agentchat.messages import TextMessage
from autogen_core.tools import FunctionTool

from app.core.teacher_manager import teacher_manager
from autogen_agentchat.messages import TextMessage

import json

class PractiseGenerateService(AbsService):

    def _get_question_format_example(self, practise_type: str) -> str:
        """根据题型获取JSON格式示例"""

        # 选择题类型（有选项）
        choice_types = ["单选题", "多选题", "判断题"]

        # 文本题类型（无选项）
        text_types = ["简答题", "论述题", "填空题", "计算题", "编程题", "分析题", "评价题", "应用题", "设计题"]

        if practise_type in choice_types:
            if practise_type == "单选题":
                return '''
                    {
                    "practise_list": [
                        {
                        "practise_type": "单选题",
                        "content": "题目内容",
                        "options": [
                            {"id": "A", "text": "选项A"},
                            {"id": "B", "text": "选项B"},
                            {"id": "C", "text": "选项C"},
                            {"id": "D", "text": "选项D"}
                        ],
                        "answer": {"value": "A"}
                        }
                    ]
                    }'''
            elif practise_type == "多选题":
                return '''
                    {
                    "practise_list": [
                        {
                        "practise_type": "多选题",
                        "content": "题目内容",
                        "options": [
                            {"id": "A", "text": "选项A"},
                            {"id": "B", "text": "选项B"},
                            {"id": "C", "text": "选项C"},
                            {"id": "D", "text": "选项D"}
                        ],
                        "answer": {"value": ["A", "C"]}
                        }
                    ]
                    }'''
            elif practise_type == "判断题":
                return '''
                        {
                        "practise_list": [
                            {
                            "practise_type": "判断题",
                            "content": "题目内容",
                            "options": [
                                {"id": "T", "text": "正确"},
                                {"id": "F", "text": "错误"}
                            ],
                            "answer": {"value": "T"}
                            }
                        ]
                        }'''

        elif practise_type in text_types:
            return '''
                    {
                    "practise_list": [
                        {
                        "practise_type": "''' + practise_type + '''",
                        "content": "题目内容",
                        "options": [],
                        "answer": {"value": "参考答案文本"}
                        }
                    ]
                    }'''

    def __init__(self):
        super().__init__()
        self.client = VLLMClient(settings.VLLM_ENDPOINT)

        # 初始化 AutoGen 高精度模式客户端
        self._init_autogen_client()

    def _init_autogen_client(self):
        """初始化 AutoGen 客户端（用于高精度模式）"""
            # 使用 Qwen3-8B（支持工具调用和强制 JSON 输出）
        model = "Qwen3-8B"
        target_url = "http://61.172.167.136:10307/v1"  # 使用配置中的地址
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        self.agent_chat_client = OpenAIChatCompletionClient(
            model=model,
            base_url=target_url,
            api_key='',
            chat_template_kwargs={
                "enable_thinking": False  # 禁用思维链，提高响应速度
            },
            response_format = {
            "type": "json_object"
            },
                model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": "unknown",
                "structured_output": True,
                "supports_parallel_tool_calls": False,
            },
            temperature=0.0,  # 确保输出稳定性
        )
        self.logger.info("AutoGen 客户端初始化成功")



    async def _query_existing_questions_by_id(self, knowledge_point_id: str) -> str:
        """根据知识点ID查询数据库中已有的题目，返回题目摘要用于防重复"""
        try:
            # TODO: 这里应该调用实际的数据库查询接口
            # 示例：await self.database_service.query_questions_by_id(knowledge_point_id)

            # 模拟查询结果
            existing_questions = [
                "《咏鹅》的作者是谁？",
                "《咏鹅》中描写鹅颜色的词语",
                "《咏鹅》中鹅的叫声用什么词描述",
            ]

            if existing_questions:
                return f"知识点ID {knowledge_point_id} 下已有题目：\n" + "\n".join([f"- {q}" for q in existing_questions])
            else:
                return f"知识点ID {knowledge_point_id} 下暂无题目。"

        except Exception as e:
            self.logger.error(f"根据知识点ID查询现有题目失败: {e}")
            return f"查询知识点ID {knowledge_point_id} 的题目时出错，请注意避免常见题目。"

    def _create_query_tool(self):
        """创建查询现有题目的工具类"""

        from autogen_core.tools import FunctionTool
        from typing import Annotated

        async def query_existing_questions(knowledge_point_id: Annotated[str, "知识点ID，用于查询该知识点下已有的题目"]) -> str:
            """
            根据知识点ID查询数据库中已有的题目，返回题目摘要用于防重复
            """
            return await self._query_existing_questions_by_id(knowledge_point_id)

        return FunctionTool(query_existing_questions, description="根据知识点ID查询数据库中已有的相关题目，用于避免重复出题")

    async def process(self, content: str, subject: str, num_of_practise: int, practise_type: PractiseType, precision_mode: str = "fast", difficulty: str = "中等", knowledge_point_id: str = None, llm_params: dict = {}) -> PractiseGenerateResponseList:
        """
        练习试题生成核心方法
        :param llm_params:
        :param content: 题目内容或者知识点
        :param subject: 学科分类
        :param num_of_practise: 生成的题目数量
        :param practise_type: 题目类型
        :return: 生成的试题列表
        """
        
        # 预处理验证
        if len(content) < 2:
            return self.wrap_response({}, 400, "题目内容或者知识点")
        
        

        if precision_mode == "high":
            # 使用 AutoGen 4 节点 DiGraphBuilder 高精度模式
            result = await self._generate_high_precision_autogen(
                content=content,
                subject=subject,
                num_of_practise=num_of_practise,
                practise_type=practise_type,
                difficulty=difficulty,
                knowledge_point_id=knowledge_point_id
            )
            try:
                self.logger.info(f"高精度模式响应: {result}")
                return self.wrap_response(data_dict=result)
            except Exception as e:
                self.logger.error(f"高精度模式处理错误: {e}")
                raise HTTPException(status_code=500, detail=f"高精度模式内部错误: {e}")
        else:
            result  = await self._generate_fast(content, subject, num_of_practise, practise_type, llm_params)
            try:
                self.logger.info(f"模型响应: {result}")
                return self.wrap_response(data_dict=result)
            except TimeoutError as e:
                raise HTTPException(status_code=504, detail="模型响应超时")
            except Exception as e:
                self.logger.error(f"500错误: {e}")
                raise HTTPException(status_code=500, detail=f"试题生成服务内部错误, {e}")

    def _build_prompt(self, content: str, subject, num_of_practise: int, practise_type: PractiseType) -> str:
        return prompts.get_practise_generate_prompt(
            content=content,
            subject=subject,
            num_of_practise=num_of_practise,
            practise_type=practise_type,
        )

    def _parse_response(self, response_dict: dict) -> dict:
        try:
            model_response = self.safe_json_parse(response_dict["choices"][0]["message"]["content"])
        except Exception as e:
            self.logger.error(f"JSON解析错误: {e}")
            raise HTTPException(status_code=500, detail=f"Internal Server Error, safe_json_parse error, {response_dict}")
        # 验证返回字段
        print('model_response:', model_response)
        return model_response
    
    async def _generate_fast(self, content: str, subject: str, num_of_practise: int, practise_type: PractiseType, llm_params: dict = {}) -> PractiseGenerateResponseList:
        """快速模式：使用传统单次调用"""
        prompt = self._build_prompt(content, subject, num_of_practise, practise_type)
        print('prompt:', prompt)
        if 'temperature' not in llm_params.keys():
            llm_params['temperature'] = 0.95
        response_dict = await self.client.generate(prompt, llm_params)
        return self._parse_response(response_dict)

    async def _generate_high_precision_autogen(
        self,
        content: str,
        subject: str,
        num_of_practise: int,
        practise_type: PractiseType,
        difficulty: str = "中等",
        knowledge_point_id: str = None
    ) -> dict:
        """高精度模式：使用优化提示词的 4 节点 DiGraphBuilder"""
        try:
            if not self.agent_chat_client:
                raise Exception("AutoGen 客户端未初始化")

            self.logger.info(f"🚀 开始高精度模式 - 题型: {practise_type.value}")

            # 使用优化后的智能体协作模式生成题目
            result = await self._generate_with_optimized_agents(
                content, subject, num_of_practise, practise_type, difficulty, knowledge_point_id
            )

            self.logger.info(f"✅ 高精度模式完成 - 结果类型: {type(result)}")
            self.logger.info(f"✅ 高精度模式完成 - 结果内容: {result}")
            return result
        except Exception as e:
            self.logger.error(f"AutoGen 高精度生成失败: {e}")
            # 直接抛出异常，不降级
            raise Exception(f"高精度模式生成失败: {str(e)}")

    def _build_high_precision_prompt(
        self,
        content: str,
        subject: str,
        num_of_practise: int,
        practise_type: PractiseType,
        difficulty: str
    ) -> str:
        """构建高精度模式的 prompt"""

        # 获取题型格式示例
        format_example = self._get_question_format_example(practise_type.value)
        if format_example is None:
            raise Exception(f"不支持的题型: {practise_type.value}")

        prompt = f"""
                    你是一位资深的教育专家和题目设计师，请根据以下要求生成高质量的练习题。

                    **任务要求：**
                    - 知识点：{content}
                    - 学科：{subject}
                    - 题目数量：{num_of_practise}
                    - 题目类型：{practise_type.value}
                    - 难度级别：{difficulty}

                    **质量要求：**
                    1. 题目内容必须与知识点高度相关
                    2. 题目难度符合{difficulty}级别
                    3. 选项设计合理，干扰项有效
                    4. 答案准确无误
                    5. 语言表达清晰规范

                    **输出格式：**
                    请严格按照以下 JSON 格式输出，不要添加任何其他内容：

                    {format_example}

                    **特别注意：**
                    - 判断题必须使用 T/F 作为选项 ID
                    - 单选题使用 A/B/C/D 作为选项 ID
                    - 答案必须与选项 ID 对应
                    - 不要输出任何解释或说明，只输出 JSON 格式的题目

                    现在请生成题目：
                """

        return prompt.strip()

    async def _generate_with_optimized_agents(
        self,
        content: str,
        subject: str,
        num_of_practise: int,
        practise_type: PractiseType,
        difficulty: str = "中等",
        knowledge_point_id: str = None
    ) -> dict:
        """使用优化的智能体协作模式生成题目"""
        try:
            self.logger.info(f"🚀 开始执行优化的智能体协作工作流")
            self.logger.info(f"📊 节点数量: 3 (analyze → generate → optimize → validate)")
            self.logger.info(f"🔄 工作流: analyze_requirements → generate_questions → optimize_questions → validate_questions")

            # 创建 DiGraphBuilder
            builder = DiGraphBuilder()

            # 定义工具函数
            async def analyze_requirements(
                content: str,
                subject: str,
                practise_type: str,
                difficulty: str,
                num_of_practise: int
            ) -> str:
                """分析题目生成需求"""
                analysis = {
                    "content": content,
                    "subject": subject,
                    "practise_type": practise_type,
                    "difficulty": difficulty,
                    "num_of_practise": num_of_practise,
                    "requirements": f"需要生成{num_of_practise}道关于'{content}'的{practise_type}，难度为{difficulty}",
                    "teacher_guidance": f"作为{subject}专业教师，请根据{content}的特点设计{practise_type}"
                }
                return json.dumps(analysis, ensure_ascii=False)

            # query_existing 作为工具集成到 generate_questions 中
            async def query_existing_questions(knowledge_point_id: str = None) -> str:
                """查询已有题目（作为工具使用）"""
                if knowledge_point_id:
                    return json.dumps({
                        "existing_questions": [],
                        "message": f"已查询知识点{knowledge_point_id}的相关题目，确保不重复出题"
                    }, ensure_ascii=False)
                return json.dumps({
                    "existing_questions": [],
                    "message": "未提供知识点ID，跳过重复检查"
                }, ensure_ascii=False)

            # 定义生成题目函数（内联版本）
            async def generate_questions_with_teacher_guidance(
                teacher_info: str,
                content: str,
                subject: str,
                practise_type: str,
                difficulty: str,
                num_of_practise: int
            ) -> str:
                """根据老师指导生成题目 - 动态生成版本"""
                try:
                    # 获取题型格式示例
                    format_example = self._get_question_format_example(practise_type)

                    # 构建专业的题目生成 prompt
                    prompt = f"""
你是一位专业的{subject}教师，请根据以下要求生成高质量的练习题。

**教师背景信息：**
{teacher_info}

**题目要求：**
- 知识点内容：{content}
- 学科：{subject}
- 题目类型：{practise_type}
- 难度级别：{difficulty}
- 题目数量：{num_of_practise}

**输出格式要求：**
请严格按照以下JSON格式输出，不要添加任何其他内容：
{format_example}

**质量要求：**
1. 题目内容必须与知识点"{content}"高度相关
2. 题目难度符合"{difficulty}"级别
3. 选项设计合理，干扰项有效
4. 答案准确无误
5. 语言表达符合{subject}学科特点

**特别注意：**
- 判断题必须使用 T/F 作为选项 ID
- 单选题使用 A/B/C/D 作为选项 ID
- 多选题使用 A/B/C/D 作为选项 ID
- 答案必须与选项 ID 对应
- practise_type 字段必须准确

现在请生成题目：
                    """

                    # 调用大模型生成题目
                    try:
                        llm_params = {
                            'temperature': 0.7,
                            'response_format': {'type': 'json_object'}
                        }
                        response_dict = await self.client.generate(prompt.strip(), llm_params)

                        # 解析响应
                        if 'model_response' in response_dict:
                            result = response_dict['model_response']
                            self.logger.info(f"✅ 动态生成题目成功: {result}")
                            return json.dumps(result, ensure_ascii=False)
                        else:
                            raise Exception("未收到有效的模型响应")

                    except Exception as llm_error:
                        self.logger.error(f"❌ 大模型调用失败: {llm_error}")
                        # 如果大模型调用失败，使用通用模板作为后备方案
                        return self._generate_fallback_question(content, subject, practise_type, difficulty, num_of_practise)

                except Exception as e:
                    self.logger.error(f"❌ 题目生成失败: {e}")
                    return json.dumps({'error': str(e)}, ensure_ascii=False)

            # 创建工具
            analyze_tool = FunctionTool(analyze_requirements, description="分析题目生成需求")
            query_tool = FunctionTool(query_existing_questions, description="查询已有题目避免重复")
            generate_tool = FunctionTool(generate_questions_with_teacher_guidance, description="根据老师指导生成题目")

            # 新增：优化工具
            async def optimize_questions(questions_json: str) -> str:
                """优化题目质量"""
                try:
                    questions_data = json.loads(questions_json)
                    if "practise_list" in questions_data:
                        practise_list = questions_data["practise_list"]
                        optimized_list = []

                        for question in practise_list:
                            # 优化题目内容
                            optimized_question = {
                                "practise_type": question.get("practise_type"),
                                "content": question.get("content"),
                                "options": question.get("options", []),
                                "answer": question.get("answer"),
                                "optimization_notes": "题目已经过质量优化检查"
                            }
                            optimized_list.append(optimized_question)

                        return json.dumps({
                            "practise_list": optimized_list,
                            "optimization_status": "completed"
                        }, ensure_ascii=False)
                    else:
                        return questions_json  # 如果格式不对，直接返回原数据
                except Exception as e:
                    return json.dumps({"error": f"优化失败: {e}"}, ensure_ascii=False)

            optimize_tool = FunctionTool(optimize_questions, description="优化题目质量和表达")

            # 验证工具
            async def validate_question_format(questions_json: str) -> str:
                """验证题目格式"""
                try:
                    questions_data = json.loads(questions_json)
                    if "practise_list" in questions_data:
                        return json.dumps({
                            "status": "success",
                            "practise_list": questions_data["practise_list"],
                            "message": "题目格式验证通过"
                        }, ensure_ascii=False)
                    else:
                        return json.dumps({
                            "status": "error",
                            "message": "题目格式不正确，缺少 practise_list 字段"
                        }, ensure_ascii=False)
                except Exception as e:
                    return json.dumps({
                        "status": "error",
                        "message": f"题目格式验证失败: {e}"
                    }, ensure_ascii=False)

            validate_tool = FunctionTool(validate_question_format, description="验证题目格式")

            # 定义智能体
            # 节点1：需求分析器
            analyzer = AssistantAgent(
                name="analyze_requirements",
                model_client=self.agent_chat_client,
                tools=[analyze_tool],
                system_message=f"""你是需求分析器，负责分析出题需求。

**职责：**
1. 使用 analyze_requirements 工具分析学科内容
2. 分析学科：{subject}，内容：{content}，难度：{difficulty}，题型：{practise_type.value}
3. 为后续生成提供详细的需求分析

你是工作流的第一个节点。"""
            )

            # 节点2：题目生成器（集成查询功能）
            format_example = self._get_question_format_example(practise_type.value)
            generator = AssistantAgent(
                name="generate_questions",
                model_client=self.agent_chat_client,
                tools=[query_tool, generate_tool],  # 集成查询工具
                system_message=f"""你是专业的题目生成器，负责生成高质量题目。

**职责：**
1. 先使用 query_existing_questions 工具查询已有题目，避免重复
2. 使用 generate_questions_with_teacher_guidance 工具生成题目
3. 生成{practise_type.value}类型的题目，数量：{num_of_practise}
4. 确保题目符合{difficulty}难度要求

**输出格式：**
{format_example}

**特别注意：**
- 判断题必须使用 T/F 作为选项 ID
- 单选题使用 A/B/C/D 作为选项 ID
- 答案必须与选项 ID 对应

你是工作流的第二个节点。"""
            )

            # 节点3：题目优化器（新增）
            optimizer = AssistantAgent(
                name="optimize_questions",
                model_client=self.agent_chat_client,
                tools=[optimize_tool],
                system_message=f"""你是题目优化器，负责优化题目质量。

**职责：**
1. 接收生成的题目
2. 使用 optimize_questions 工具优化题目
3. 检查题目的准确性、清晰度和教学价值
4. 优化选项的合理性和干扰项的有效性
5. 确保题目符合教学标准

**优化重点：**
- 题目表达是否清晰准确
- 选项设计是否合理
- 答案是否正确
- 难度是否符合要求

你是工作流的第三个节点。"""
            )

            # 节点4：质量验证器
            validator = AssistantAgent(
                name="validate_questions",
                model_client=self.agent_chat_client,
                tools=[validate_tool],
                system_message=f"""你是质量验证器，负责最终验证。

**职责：**
1. 接收优化后的题目
2. 使用 validate_question_format 工具验证格式
3. 确保题目完全符合要求
4. 输出最终的标准JSON格式

**验证重点：**
- 题型字段正确性
- 选项格式正确性
- 答案有效性
- JSON结构完整性

**输出要求：**
必须返回标准格式：{{"practise_list": [...]}}

你是工作流的最后一个节点。"""
            )

            # 构建工作流
            builder.add_node(analyzer)
            builder.add_node(generator)
            builder.add_node(optimizer)
            builder.add_node(validator)

            # 定义工作流路径
            builder.add_edge("analyze_requirements", "generate_questions")
            builder.add_edge("generate_questions", "optimize_questions")
            builder.add_edge("optimize_questions", "validate_questions")

            # 设置入口点
            builder.set_entry_point("analyze_requirements")
            # 注意：DiGraphBuilder 没有 set_finish_point 方法，工作流会在最后一个节点自然结束

            # 创建并运行工作流
            from autogen_agentchat.conditions import MaxMessageTermination
            from autogen_agentchat.teams import GraphFlow

            # 使用 DiGraphBuilder 构建有向图工作流
            graph = builder.build()

            # 创建 GraphFlow 来运行图
            graph_flow = GraphFlow(
                participants=[analyzer, generator, optimizer, validator],
                graph=graph,
                termination_condition=MaxMessageTermination(max_messages=10)
            )

            message = f"请生成{num_of_practise}道关于'{content}'的{practise_type.value}题目，难度：{difficulty}"

            # 运行工作流
            result = await graph_flow.run(task=message)

            self.logger.info(f"✅ 优化智能体协作完成，消息数量: {len(result.messages)}")

            # 提取最终题目
            return self._extract_final_questions_autogen(result)

        except Exception as e:
            self.logger.error(f"优化智能体协作失败: {e}")
            import traceback
            traceback.print_exc()
            raise Exception(f"优化智能体协作生成失败: {e}")

    async def _generate_with_4_node_digraph(
        self,
        content: str,
        subject: str,
        num_of_practise: int,
        practise_type: PractiseType,
        difficulty: str,
        knowledge_point_id: str
    ) -> dict:
        """使用 4 节点 DiGraphBuilder 生成题目（基于 LangGraph 逻辑）"""
        try:
            # 创建工具
            tools = self._create_4_node_tools()
            analyze_tool, query_tool, generate_tool, validate_tool = tools

            # 节点1：需求分析器 (analyze_requirements)
            analyze_requirements = AssistantAgent(
                name="analyze_requirements",
                model_client=self.agent_chat_client,
                tools=[analyze_tool],
                system_message=f"""你是需求分析器，负责分析出题需求并选择合适的老师。
                                职责：
                                1. 使用 analyze_subject_content 工具分析学科内容
                                2. 分析学科：{subject}，内容：{content}，难度：{difficulty}，题型：{practise_type.value}
                                3. 选择合适的专业老师和教学策略
                                4. 输出分析结果，为后续步骤做准备

                                你是工作流的第一个节点。
                                """
            )

            # 节点2：查询已有题目 (query_existing)
            query_existing = AssistantAgent(
                name="query_existing",
                model_client=self.agent_chat_client,
                tools=[query_tool],
                system_message=f"""你是题目查询器，负责查询已有题目以避免重复。
                                职责：
                                1. 使用 query_existing_questions 工具查询知识点ID：{knowledge_point_id or "无"}
                                2. 分析已有题目，了解重复情况
                                3. 为题目生成提供参考信息

                                你是工作流的第二个节点。
                                """
            )

            # 节点3：题目生成器 (generate_questions) - 优化提示词
            format_example = self._get_question_format_example(practise_type.value)
            question_generator = AssistantAgent(
                name="generate_questions",
                model_client=self.agent_chat_client,
                tools=[generate_tool],
                system_message=f"""你是专业的题目生成器，负责生成高质量、格式规范的题目。
                                **核心职责：**
                                1. 接收需求分析和查询结果
                                2. 使用 generate_questions_with_teacher_guidance 工具生成题目
                                3. 生成{practise_type.value}类型的题目，数量：{num_of_practise}
                                4. 确保题目符合{difficulty}难度要求

                                **格式要求：**
                                必须严格按照以下JSON格式输出题目：
                                {format_example}

                                **特别注意：**
                                - 判断题必须使用 T/F 作为选项 ID，对应"正确"/"错误"
                                - 单选题使用 A/B/C/D 作为选项 ID
                                - 答案必须与选项 ID 严格对应
                                - practise_type 字段必须准确（判断题="true_false"，单选题="single_choice"）
                                - options 数组不能为空（除简答题外）

                                你是工作流的第三个节点，请确保输出格式完全正确。
                                """
            )

            # 节点4：质量验证器 (validate_questions) - 优化提示词
            quality_validator = AssistantAgent(
                name="validate_questions",
                model_client=self.agent_chat_client,
                tools=[validate_tool],
                system_message=f"""你是专业的质量验证器，负责验证题目格式和质量，并输出最终的标准格式。

                                **核心职责：**
                                1. 接收生成的题目
                                2. 使用 validate_question_format 工具验证格式
                                3. 确保题目完全符合要求
                                4. 输出最终的标准JSON格式

                                **验证重点：**
                                - 题型字段正确性（判断题="true_false"，单选题="single_choice"）
                                - 选项格式正确性（判断题必须有T/F选项，单选题必须有A/B/C/D选项）
                                - 答案有效性（answer.value必须与options中的id对应）
                                - JSON结构完整性（practise_list数组，包含所有必需字段）

                                **输出要求：**
                                必须返回完整的、格式正确的JSON结构，确保：
                                1. practise_type 字段准确
                                2. options 数组格式正确
                                3. answer 字段与选项ID对应
                                4. 所有字段完整无缺

                                你是工作流的最后一个节点，负责输出最终的标准格式题目。"""
            )

            # 构建 4 节点有向图
            builder = DiGraphBuilder()
            builder.add_node(analyze_requirements)
            builder.add_node(query_existing)
            builder.add_node(question_generator)
            builder.add_node(quality_validator)

            # 添加有向边：analyze -> query -> generate -> validate
            builder.add_edge(analyze_requirements, query_existing)
            builder.add_edge(query_existing, question_generator)
            builder.add_edge(question_generator, quality_validator)

            # 创建 GraphFlow
            graph_flow = GraphFlow(
                participants=[analyze_requirements, query_existing, question_generator, quality_validator],
                graph=builder.build(),
                termination_condition=MaxMessageTermination(max_messages=20)
            )

            # 构建任务
            task_content = f"""
                        🎯  **4节点智能出题工作流**

                            **任务要求：**
                            - 知识点：{content}
                            - 学科：{subject}
                            - 题目数量：{num_of_practise}
                            - 题目类型：{practise_type.value}
                            - 难度级别：{difficulty}
                            - 知识点ID：{knowledge_point_id or "未提供"}

                            **4节点工作流：**
                            analyze_requirements -> query_existing -> generate_questions -> validate_questions

                            **执行步骤：**
                            1. analyze_requirements: 分析需求，选择老师
                            2. query_existing: 查询已有题目，避免重复
                            3. generate_questions: 生成高质量题目
                            4. validate_questions: 验证格式和质量

                            现在开始执行4节点工作流！
                            """

            message = TextMessage(content=task_content, source="user")

            self.logger.info(f"🚀 开始执行 4 节点 DiGraphBuilder 工作流")
            self.logger.info(f"📊 节点数量: 4")
            self.logger.info(f"🔄 工作流: analyze_requirements -> query_existing -> generate_questions -> validate_questions")

            # 执行有向图
            result = await graph_flow.run(task=message)

            self.logger.info(f"✅ 4 节点工作流执行完成，消息数量: {len(result.messages)}")

            # 提取最终题目
            return self._extract_final_questions_autogen(result)

        except Exception as e:
            self.logger.error(f"4节点 DiGraph 生成失败: {e}")
            import traceback
            traceback.print_exc()
            return {"practise_list": []}

    def _create_4_node_tools(self):
        """创建 4 节点工具（基于 LangGraph 逻辑）"""

        async def analyze_subject_content(content: str, subject: str) -> str:
            """分析学科内容并推荐合适的老师和教学策略"""
            try:
                # 选择老师
                teacher_type = teacher_manager.select_teacher_by_subject(subject, content)
                teacher_profile = teacher_manager.get_teacher_profile(teacher_type)

                result = {
                    'recommended_teacher': teacher_profile.name,
                    'teacher_expertise': teacher_profile.expertise,
                    'teacher_skills': teacher_profile.skills[:5],
                    'subject_analysis': f'该内容属于{teacher_profile.name}的专业领域',
                    'teaching_suggestions': [
                        f'运用{teacher_profile.skills[0]}进行教学',
                        f'结合{teacher_profile.skills[1]}提升理解',
                        f'通过{teacher_profile.skills[2]}加深印象'
                    ]
                }

                return json.dumps(result, ensure_ascii=False)

            except Exception as e:
                return json.dumps({'error': str(e)}, ensure_ascii=False)

        async def query_existing_questions(knowledge_point_id: str) -> str:
            """根据知识点ID查询数据库中已有的相关题目，用于避免重复出题"""
            try:
                # 调用现有的查询方法
                existing_info = await self._query_existing_questions_by_id(knowledge_point_id)

                result = {
                    'knowledge_point_id': knowledge_point_id,
                    'existing_info': existing_info,
                    'message': f'已查询知识点 {knowledge_point_id} 的相关题目'
                }

                return json.dumps(result, ensure_ascii=False)

            except Exception as e:
                return json.dumps({'error': str(e)}, ensure_ascii=False)

        async def generate_questions_with_teacher_guidance(
            teacher_info: str,
            content: str,
            subject: str,
            practise_type: str,
            difficulty: str,
            num_of_practise: int
        ) -> str:
            """根据老师指导生成题目 - 动态生成版本"""
            try:
                # 获取题型格式示例
                format_example = self._get_question_format_example(practise_type)

                # 构建动态生成的 prompt
                self.logger.info(f"🔍 动态生成题目 - 学科: {subject}, 内容: {content}, 题型: {practise_type}")
                self.logger.info(f"🔍 函数参数 - teacher_info: {teacher_info}, difficulty: {difficulty}, num_of_practise: {num_of_practise}")

                # 构建专业的题目生成 prompt
                prompt = f"""
你是一位专业的{subject}教师，请根据以下要求生成高质量的练习题。

**教师背景信息：**
{teacher_info}

**题目要求：**
- 知识点内容：{content}
- 学科：{subject}
- 题目类型：{practise_type}
- 难度级别：{difficulty}
- 题目数量：{num_of_practise}

**输出格式要求：**
请严格按照以下JSON格式输出，不要添加任何其他内容：
{format_example}

**质量要求：**
1. 题目内容必须与知识点"{content}"高度相关
2. 题目难度符合"{difficulty}"级别
3. 选项设计合理，干扰项有效
4. 答案准确无误
5. 语言表达符合{subject}学科特点

**特别注意：**
- 判断题必须使用 T/F 作为选项 ID
- 单选题使用 A/B/C/D 作为选项 ID
- 多选题使用 A/B/C/D 作为选项 ID
- 答案必须与选项 ID 对应
- practise_type 字段必须准确

现在请生成题目：
                """

                # 调用大模型生成题目
                try:
                    llm_params = {
                        'temperature': 0.7,
                        'response_format': {'type': 'json_object'}
                    }
                    response_dict = await self.client.generate(prompt.strip(), llm_params)

                    # 解析响应
                    if 'model_response' in response_dict:
                        result = response_dict['model_response']
                        self.logger.info(f"✅ 动态生成题目成功: {result}")
                        return json.dumps(result, ensure_ascii=False)
                    else:
                        raise Exception("未收到有效的模型响应")

                except Exception as llm_error:
                    self.logger.error(f"❌ 大模型调用失败: {llm_error}")
                    # 如果大模型调用失败，使用通用模板作为后备方案
                    return self._generate_fallback_question(content, subject, practise_type, difficulty, num_of_practise)

            except Exception as e:
                self.logger.error(f"❌ 题目生成失败: {e}")
                return json.dumps({'error': str(e)}, ensure_ascii=False)

        def _generate_fallback_question(self, content: str, subject: str, practise_type: str, difficulty: str, num_of_practise: int) -> str:
            """生成后备题目（当大模型调用失败时使用）"""
            self.logger.info(f"🔄 使用后备方案生成题目")

            if practise_type == "判断题":
                question = {
                    "practise_list": [{
                        "practise_type": "true_false",
                        "content": f"关于{content}的基础知识判断题（{difficulty}难度）",
                        "options": [
                            {"id": "T", "text": "正确"},
                            {"id": "F", "text": "错误"}
                        ],
                        "answer": {"value": "T"}
                    }]
                }
            elif practise_type == "单选题":
                question = {
                    "practise_list": [{
                        "practise_type": "single_choice",
                        "content": f"关于{content}的基础知识选择题（{difficulty}难度）",
                        "options": [
                            {"id": "A", "text": "选项A"},
                            {"id": "B", "text": "选项B"},
                            {"id": "C", "text": "选项C"},
                            {"id": "D", "text": "选项D"}
                        ],
                        "answer": {"value": "A"}
                    }]
                }
            else:
                # 其他题型的通用模板
                question = {
                    "practise_list": [{
                        "practise_type": practise_type,
                        "content": f"关于{content}的{practise_type}（{difficulty}难度）",
                        "options": [],
                        "answer": {"value": "请根据具体内容填写答案"}
                    }]
                }

            return json.dumps(question, ensure_ascii=False)

        async def validate_question_format(question_data: str) -> str:
            """验证题目格式是否符合要求"""
            try:
                data = json.loads(question_data)

                # 处理包含 practise_list 的数据
                if isinstance(data, dict) and "practise_list" in data:
                    practise_list = data["practise_list"]
                    if practise_list and len(practise_list) > 0:
                        question = practise_list[0]  # 取第一个题目进行验证
                    else:
                        return json.dumps({
                            'valid': False,
                            'errors': 'practise_list 为空'
                        }, ensure_ascii=False)
                else:
                    question = data

                required_fields = ['practise_type', 'content', 'options', 'answer']
                missing_fields = [field for field in required_fields if field not in question]

                if missing_fields:
                    return json.dumps({
                        'valid': False,
                        'errors': f'缺少必需字段: {missing_fields}'
                    }, ensure_ascii=False)

                # 检查题型特定要求
                practise_type = question['practise_type']
                options = question['options']

                if practise_type in ['单选题', '多选题', '判断题']:
                    if not options or len(options) < 2:
                        return json.dumps({
                            'valid': False,
                            'errors': f'{practise_type}必须有至少2个选项'
                        }, ensure_ascii=False)

                # 返回验证通过的完整数据（保持原始格式）
                return json.dumps({
                    'status': 'success',
                    'message': '题目验证通过，格式和质量符合要求',
                    'validated_questions': [question]  # 保持完整的题目结构
                }, ensure_ascii=False)

            except Exception as e:
                return json.dumps({
                    'valid': False,
                    'errors': f'格式验证失败: {str(e)}'
                }, ensure_ascii=False)

        return [
            FunctionTool(analyze_subject_content, description="分析学科内容并推荐合适的老师和教学策略"),
            FunctionTool(query_existing_questions, description="根据知识点ID查询数据库中已有的相关题目，用于避免重复出题"),
            FunctionTool(generate_questions_with_teacher_guidance, description="根据老师指导生成题目"),
            FunctionTool(validate_question_format, description="验证题目格式是否符合要求")
        ]

    def _extract_final_questions_autogen(self, result):
        """从 AutoGen 结果中提取最终题目（简化版）"""
        try:
            if not result or not hasattr(result, 'messages'):
                raise Exception("无效的 AutoGen 结果")

            self.logger.info(f"🔍 开始提取题目，共有 {len(result.messages)} 条消息")

            # 只查找最后一个 validate_questions 节点的输出
            for msg in reversed(result.messages):
                if hasattr(msg, 'source') and msg.source == "validate_questions":
                    if hasattr(msg, 'content') and msg.content:
                        content_str = str(msg.content).strip()
                        self.logger.info(f"🔍 找到 validate_questions 输出: {content_str[:200]}...")

                        try:
                            # 直接解析 JSON
                            if content_str.startswith('{'):
                                data = json.loads(content_str)

                                # 期望的标准格式：{"practise_list": [...]}
                                if "practise_list" in data:
                                    practise_list = data["practise_list"]
                                    if practise_list and len(practise_list) > 0:
                                        self.logger.info(f"✅ 成功提取到 {len(practise_list)} 道题目")
                                        return data

                                # 如果没有 practise_list，直接报错
                                raise Exception(f"validate_questions 输出格式错误：缺少 practise_list 字段")

                            else:
                                raise Exception(f"validate_questions 输出不是有效的 JSON 格式")

                        except json.JSONDecodeError as e:
                            raise Exception(f"validate_questions 输出 JSON 解析失败: {e}")

            # 如果没找到 validate_questions 的输出，直接报错
            raise Exception("未找到 validate_questions 节点的输出")

        except Exception as e:
            self.logger.error(f"❌ 题目提取失败: {e}")
            # 不再提供后备方案，直接抛出异常
            raise Exception(f"AutoGen 题目提取失败: {e}")