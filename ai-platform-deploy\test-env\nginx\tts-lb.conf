events {
    worker_connections 2048;
    use epoll;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format tts_access '$remote_addr - $remote_user [$time_local] '
                         '"$request" $status $body_bytes_sent '
                         '"$http_referer" "$http_user_agent" '
                         'rt=$request_time uct="$upstream_connect_time" '
                         'uht="$upstream_header_time" urt="$upstream_response_time" '
                         'upstream="$upstream_addr" cache="$upstream_cache_status"';
    
    access_log /var/log/nginx/tts_access.log tts_access;
    error_log  /var/log/nginx/tts_error.log warn;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        application/json
        application/javascript
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # 上游服务器配置 - TTS多节点
    upstream tts_cluster {
        # 负载均衡策略: 加权轮询
        server tts-node-1:8002 weight=4 max_fails=3 fail_timeout=30s;  # 主节点，权重最高
        server tts-node-2:8002 weight=3 max_fails=3 fail_timeout=30s;  # 次节点
        server tts-node-3:8002 weight=2 max_fails=3 fail_timeout=30s;  # 备用节点
        
        # 连接保持
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }
    
    # 健康检查上游 (仅用于健康检查)
    upstream tts_health_check {
        server tts-node-1:8002;
        server tts-node-2:8002;
        server tts-node-3:8002;
    }
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=tts_limit:10m rate=100r/s;
    limit_conn_zone $binary_remote_addr zone=tts_conn:10m;
    
    # 主服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 基本配置
        client_max_body_size 100M;
        client_body_timeout 60s;
        client_header_timeout 60s;
        
        # 限流限连
        limit_req zone=tts_limit burst=200 nodelay;
        limit_conn tts_conn 50;
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "TTS Load Balancer OK\n";
            add_header Content-Type text/plain;
        }
        
        # TTS节点状态检查
        location /nodes/status {
            access_log off;
            proxy_pass http://tts_health_check/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }
        
        # TTS服务代理 - 主要入口
        location / {
            # 代理到TTS集群
            proxy_pass http://tts_cluster;
            
            # 请求头设置
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-tts-lb";
            
            # 超时配置
            proxy_connect_timeout 10s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # 缓冲配置
            proxy_buffering on;
            proxy_buffer_size 16k;
            proxy_buffers 8 16k;
            proxy_busy_buffers_size 32k;
            
            # 连接保持
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # 错误处理
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 10s;
        }
        
        # TTS文本转语音 - 优化路由
        location /text_to_speech {
            # 使用一致性哈希，相同文本路由到同一节点（利用缓存）
            proxy_pass http://tts_cluster;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Request-Type "tts";
            
            # TTS专用超时配置
            proxy_connect_timeout 15s;
            proxy_send_timeout 600s;  # TTS可能需要更长时间
            proxy_read_timeout 600s;
            
            # 缓存配置 (可选)
            proxy_cache_bypass $http_pragma $http_authorization;
            proxy_no_cache $http_pragma $http_authorization;
        }
        
        # 查询语音列表 - 轻量级请求
        location /query_speakers {
            proxy_pass http://tts_cluster;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Request-Type "query";
            
            # 快速响应配置
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # 统计信息
        location /stats {
            proxy_pass http://tts_cluster;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
        }
        
        # Nginx状态页面
        location /nginx_status {
            stub_status on;
            access_log off;
            allow **********/16;  # 仅允许内网访问
            deny all;
        }
    }
    
    # 直接访问各个节点的配置 (调试用)
    server {
        listen 8001;
        server_name localhost;
        
        location / {
            proxy_pass http://tts-node-1:8002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Node-Access "direct-node-1";
        }
    }
    
    server {
        listen 8002;
        server_name localhost;
        
        location / {
            proxy_pass http://tts-node-2:8002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Node-Access "direct-node-2";
        }
    }
    
    server {
        listen 8003;
        server_name localhost;
        
        location / {
            proxy_pass http://tts-node-3:8002;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Node-Access "direct-node-3";
        }
    }
}
