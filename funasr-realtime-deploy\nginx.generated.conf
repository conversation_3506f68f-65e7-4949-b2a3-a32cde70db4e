events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # 日志格式
    log_format funasr_realtime '$remote_addr - $remote_user [$time_local] '
                               '"$request" $status $body_bytes_sent '
                               '"$http_referer" "$http_user_agent" '
                               'rt=$request_time uct="$upstream_connect_time" '
                               'uht="$upstream_header_time" urt="$upstream_response_time" '
                               'upstream="$upstream_addr" upgrade="$http_upgrade"';
    
    access_log /var/log/nginx/funasr_access.log funasr_realtime;
    error_log  /var/log/nginx/funasr_error.log warn;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # WebSocket配置
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        application/json
        application/javascript
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # HTTP文件识别集群
    upstream funasr_http_cluster {
        # 2个HTTP实例
        server funasr-http-funasr-gpu-0:50300 weight=1 max_fails=3 fail_timeout=30s;
        server funasr-http-funasr-gpu-1:50302 weight=1 max_fails=3 fail_timeout=30s;
        
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }
    
    # WebSocket实时转写集群
    upstream funasr_wss_cluster {
        # 使用IP哈希确保WebSocket连接的粘性
        ip_hash;
        
        # 2个WebSocket实例
        server funasr-wss-funasr-gpu-0:50301 weight=1 max_fails=3 fail_timeout=30s;
        server funasr-wss-funasr-gpu-1:50303 weight=1 max_fails=3 fail_timeout=30s;
    }
    
    # 健康检查上游
    upstream funasr_health {
        server funasr-http-funasr-gpu-0:50300;
        server funasr-http-funasr-gpu-1:50302;
    }
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=funasr_http_limit:10m rate=100r/s;
    limit_req_zone $binary_remote_addr zone=funasr_wss_limit:10m rate=10r/s;
    limit_conn_zone $binary_remote_addr zone=funasr_conn:10m;
    
    # 主服务器配置
    server {
        listen 80;
        server_name localhost;
        
        # 基本配置
        client_body_timeout 300s;
        client_header_timeout 60s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "FunASR Realtime Load Balancer OK\n";
            add_header Content-Type text/plain;
        }
        
        # 集群健康检查
        location /cluster/health {
            proxy_pass http://funasr_health/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_connect_timeout 10s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # WebSocket实时转写接口
        location /ws/asr {
            limit_req zone=funasr_wss_limit burst=20 nodelay;
            limit_conn funasr_conn 10;
            
            proxy_pass http://funasr_wss_cluster;
            
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-funasr-realtime-lb";
            
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_connect_timeout 60s;
            
            proxy_buffering off;
            proxy_cache off;
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }
        
        # HTTP文件识别接口
        location /fileASR {
            limit_req zone=funasr_http_limit burst=200 nodelay;
            limit_conn funasr_conn 50;
            
            proxy_pass http://funasr_http_cluster;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Load-Balancer "nginx-funasr-realtime-lb";
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            proxy_temp_file_write_size 64k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }
        
        # 兼容接口
        location /llm/asr/recognition {
            limit_req zone=funasr_http_limit burst=200 nodelay;
            limit_conn funasr_conn 50;
            
            proxy_pass http://funasr_http_cluster/fileASR;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_buffering on;
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
            proxy_busy_buffers_size 64k;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_next_upstream_tries 3;
            proxy_next_upstream_timeout 60s;
        }
        
        # Nginx状态页面
        location /nginx_status {
            stub_status on;
            access_log off;
            allow **********/16;
            deny all;
        }
        
        # 默认路由
        location / {
            proxy_pass http://funasr_http_cluster;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }

    # 直接访问HTTP实例 - funasr-gpu-0
    server {
        listen 8001;
        server_name localhost;
        
        location / {
            proxy_pass http://funasr-http-funasr-gpu-0:50300;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-http-funasr-gpu-0";
        }
    }

    # 直接访问WebSocket实例 - funasr-gpu-0
    server {
        listen 8011;
        server_name localhost;

        location / {
            proxy_pass http://funasr-wss-funasr-gpu-0:50301;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-wss-funasr-gpu-0";
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
        }
    }
    # 直接访问HTTP实例 - funasr-gpu-1
    server {
        listen 8002;
        server_name localhost;
        
        location / {
            proxy_pass http://funasr-http-funasr-gpu-1:50302;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-http-funasr-gpu-1";
        }
    }

    # 直接访问WebSocket实例 - funasr-gpu-1
    server {
        listen 8012;
        server_name localhost;

        location / {
            proxy_pass http://funasr-wss-funasr-gpu-1:50303;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection $connection_upgrade;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Instance-Access "direct-wss-funasr-gpu-1";
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
        }
    }
}
