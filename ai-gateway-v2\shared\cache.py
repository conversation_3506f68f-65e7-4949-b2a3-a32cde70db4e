"""
高性能缓存管理器
支持多级缓存、智能失效、预热等功能
"""

import asyncio
import hashlib
import json
import pickle
import time
from typing import Any, Optional, Union, Callable, Dict
from functools import wraps

import aioredis
from cachetools import TTLCache
from pydantic import BaseModel

from gateway.config.settings import settings


class CacheKey:
    """缓存键管理"""
    
    @staticmethod
    def generate(service: str, method: str, params: dict) -> str:
        """生成缓存键"""
        # 创建参数的哈希值
        params_str = json.dumps(params, sort_keys=True, ensure_ascii=False)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        
        return f"ai_gateway:v2:{service}:{method}:{params_hash}"
    
    @staticmethod
    def pattern(service: str, method: str = "*") -> str:
        """生成缓存键模式"""
        return f"ai_gateway:v2:{service}:{method}:*"


class CacheStats(BaseModel):
    """缓存统计"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class CacheManager:
    """高性能缓存管理器"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        self.local_cache: TTLCache = TTLCache(
            maxsize=settings.CACHE_MAX_SIZE,
            ttl=300  # 本地缓存5分钟
        )
        self.stats: Dict[str, CacheStats] = {}
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化缓存连接"""
        if not settings.CACHE_ENABLED:
            return
        
        try:
            # 连接Redis
            self.redis = aioredis.from_url(
                settings.REDIS_URL,
                max_connections=settings.REDIS_MAX_CONNECTIONS,
                retry_on_timeout=settings.REDIS_RETRY_ON_TIMEOUT,
                decode_responses=False  # 保持二进制数据
            )
            
            # 测试连接
            await self.redis.ping()
            print("✅ Redis缓存连接成功")
            
        except Exception as e:
            print(f"❌ Redis缓存连接失败: {e}")
            self.redis = None
    
    async def close(self):
        """关闭缓存连接"""
        if self.redis:
            await self.redis.close()
    
    def _get_stats(self, service: str) -> CacheStats:
        """获取服务统计"""
        if service not in self.stats:
            self.stats[service] = CacheStats()
        return self.stats[service]
    
    async def get(self, key: str, service: str = "default") -> Optional[Any]:
        """获取缓存值"""
        if not settings.CACHE_ENABLED:
            return None
        
        stats = self._get_stats(service)
        
        try:
            # 1. 先查本地缓存
            if key in self.local_cache:
                stats.hits += 1
                return self.local_cache[key]
            
            # 2. 查Redis缓存
            if self.redis:
                data = await self.redis.get(key)
                if data:
                    # 反序列化
                    value = pickle.loads(data)
                    
                    # 更新本地缓存
                    self.local_cache[key] = value
                    
                    stats.hits += 1
                    return value
            
            # 缓存未命中
            stats.misses += 1
            return None
            
        except Exception as e:
            stats.errors += 1
            print(f"缓存获取错误: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        service: str = "default"
    ) -> bool:
        """设置缓存值"""
        if not settings.CACHE_ENABLED:
            return False
        
        stats = self._get_stats(service)
        ttl = ttl or settings.CACHE_DEFAULT_TTL
        
        try:
            # 1. 设置本地缓存
            self.local_cache[key] = value
            
            # 2. 设置Redis缓存
            if self.redis:
                # 序列化数据
                data = pickle.dumps(value)
                await self.redis.setex(key, ttl, data)
            
            stats.sets += 1
            return True
            
        except Exception as e:
            stats.errors += 1
            print(f"缓存设置错误: {e}")
            return False
    
    async def delete(self, key: str, service: str = "default") -> bool:
        """删除缓存"""
        if not settings.CACHE_ENABLED:
            return False
        
        stats = self._get_stats(service)
        
        try:
            # 1. 删除本地缓存
            self.local_cache.pop(key, None)
            
            # 2. 删除Redis缓存
            if self.redis:
                await self.redis.delete(key)
            
            stats.deletes += 1
            return True
            
        except Exception as e:
            stats.errors += 1
            print(f"缓存删除错误: {e}")
            return False
    
    async def clear_pattern(self, pattern: str, service: str = "default") -> int:
        """按模式清除缓存"""
        if not settings.CACHE_ENABLED or not self.redis:
            return 0
        
        try:
            # 获取匹配的键
            keys = await self.redis.keys(pattern)
            if keys:
                # 批量删除
                deleted = await self.redis.delete(*keys)
                
                # 清除本地缓存中的相关项
                for key in list(self.local_cache.keys()):
                    if key.encode() in keys:
                        self.local_cache.pop(key, None)
                
                return deleted
            return 0
            
        except Exception as e:
            print(f"模式清除错误: {e}")
            return 0
    
    async def get_or_set(
        self,
        key: str,
        func: Callable,
        ttl: Optional[int] = None,
        service: str = "default"
    ) -> Any:
        """获取缓存或执行函数设置缓存"""
        # 先尝试获取缓存
        value = await self.get(key, service)
        if value is not None:
            return value
        
        # 缓存未命中，执行函数
        if asyncio.iscoroutinefunction(func):
            value = await func()
        else:
            value = func()
        
        # 设置缓存
        if value is not None:
            await self.set(key, value, ttl, service)
        
        return value
    
    def cache_result(
        self,
        service: str,
        method: str,
        ttl: Optional[int] = None,
        key_func: Optional[Callable] = None
    ):
        """缓存装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                if not settings.CACHE_ENABLED:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    return func(*args, **kwargs)
                
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    params = {"args": args, "kwargs": kwargs}
                    cache_key = CacheKey.generate(service, method, params)
                
                # 获取或设置缓存
                async def execute_func():
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    return func(*args, **kwargs)
                
                return await self.get_or_set(cache_key, execute_func, ttl, service)
            
            return wrapper
        return decorator
    
    async def warm_up(self, service: str, warm_up_data: list):
        """缓存预热"""
        if not settings.CACHE_ENABLED:
            return
        
        print(f"开始预热 {service} 服务缓存...")
        
        for item in warm_up_data:
            key = item.get("key")
            value = item.get("value")
            ttl = item.get("ttl", settings.CACHE_DEFAULT_TTL)
            
            if key and value:
                await self.set(key, value, ttl, service)
        
        print(f"✅ {service} 服务缓存预热完成，预热 {len(warm_up_data)} 项")
    
    async def get_stats(self) -> Dict[str, dict]:
        """获取缓存统计信息"""
        result = {}
        
        for service, stats in self.stats.items():
            result[service] = {
                "hits": stats.hits,
                "misses": stats.misses,
                "sets": stats.sets,
                "deletes": stats.deletes,
                "errors": stats.errors,
                "hit_rate": stats.hit_rate
            }
        
        # 添加Redis信息
        if self.redis:
            try:
                redis_info = await self.redis.info("memory")
                result["redis"] = {
                    "used_memory": redis_info.get("used_memory"),
                    "used_memory_human": redis_info.get("used_memory_human"),
                    "maxmemory": redis_info.get("maxmemory"),
                    "maxmemory_human": redis_info.get("maxmemory_human")
                }
            except Exception as e:
                result["redis"] = {"error": str(e)}
        
        # 添加本地缓存信息
        result["local"] = {
            "size": len(self.local_cache),
            "maxsize": self.local_cache.maxsize,
            "currsize": self.local_cache.currsize
        }
        
        return result


# 全局缓存管理器实例
cache_manager = CacheManager()
