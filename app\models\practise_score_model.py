from pydantic import BaseModel, Field
from typing import List, Literal, Optional

# 评分请求模型
class GradingRequest(BaseModel):
    """
    评分请求模型
    - question: 题目内容 (必填)
    - answer: 学生回答 (必填)
    """
    question: str = Field(..., min_length=5, example="简述牛顿第一定律")
    answer: str = Field(..., min_length=1, example="物体在不受外力时保持静止或匀速直线运动")
    llm_params: dict = Field(
        {},
        example={
            "model": "Qwen2-7B-Instruct",
            "stream": False,
            "max_tokens": 1000,
            "temperature": 0.0,
            "top_p": 0.9,
        },
        description="llm模型参数"
    )


# 评分响应模型
class GradingResponse(BaseModel):
    """
    评分响应模型
    - score: 得分 (0-10)
    - analysis: 综合分析
    - suggestions: 改进建议列表
    - knowledge_point: # 知识点分析
    - reference_answer: 参考答案
    """
    score: float
    analysis: str
    suggestions: list[str]
    knowledge_point: list[str]
    reference_answer: str


