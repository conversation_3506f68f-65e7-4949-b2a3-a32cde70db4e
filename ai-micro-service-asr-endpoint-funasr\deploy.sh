#!/bin/bash

# FunASR GPU版本百万并发部署脚本
# 基于官方GPU镜像 + 优化server.py的部署方案

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🚀 FunASR GPU版本百万并发部署${NC}"
echo "基于官方GPU镜像 + 优化server.py"
echo "========================================"

# 检查环境
check_environment() {
    echo -e "${YELLOW}🔍 检查部署环境...${NC}"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装${NC}"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    
    # 检查NVIDIA Docker (使用官方FunASR GPU镜像测试)
    if ! docker run --rm --gpus all registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1 nvidia-smi &> /dev/null; then
        echo -e "${RED}❌ NVIDIA Docker支持未配置或FunASR GPU镜像无法访问GPU${NC}"
        exit 1
    fi
    
    # 检查GPU数量
    gpu_count=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
    echo -e "${CYAN}💻 检测到 $gpu_count 个GPU${NC}"
    
    if [ "$gpu_count" -lt 4 ]; then
        echo -e "${YELLOW}⚠️ 建议至少4个GPU用于最佳性能${NC}"
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 拉取官方GPU镜像
pull_official_image() {
    echo -e "${YELLOW}📥 拉取FunASR官方GPU镜像...${NC}"
    
    docker pull registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
    
    echo -e "${GREEN}✅ 官方GPU镜像拉取完成${NC}"
}

# 生成配置文件
generate_configs() {
    echo -e "${YELLOW}� 生成动态配置...${NC}"

    # 检查必要文件
    required_files=(
        "server.py"
        "start.sh"
        "config.env"
        "generate-config.py"
    )

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            echo -e "${RED}❌ 缺少必要文件: $file${NC}"
            exit 1
        fi
    done

    # 确保脚本可执行
    chmod +x start.sh generate-config.py

    # 生成配置文件
    python3 generate-config.py

    if [ ! -f "docker-compose.generated.yml" ]; then
        echo -e "${RED}❌ 配置生成失败${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ 动态配置生成完成${NC}"
}

# 部署GPU实例
deploy_gpu_instances() {
    echo -e "${YELLOW}🚀 部署FunASR GPU实例...${NC}"

    # 停止现有服务
    docker-compose -f docker-compose.generated.yml down 2>/dev/null || true

    # 启动GPU实例
    docker-compose -f docker-compose.generated.yml up -d

    echo -e "${GREEN}✅ GPU实例部署完成${NC}"
}

# 等待服务就绪
wait_for_services() {
    echo -e "${YELLOW}⏳ 等待GPU实例启动...${NC}"

    # 从生成的docker-compose文件中提取端口信息
    if [ ! -f "docker-compose.generated.yml" ]; then
        echo -e "${RED}❌ 未找到生成的配置文件${NC}"
        return 1
    fi

    # 提取所有GPU实例的端口
    gpu_ports=($(grep -E "^\s*-\s*\"[0-9]+:[0-9]+\"" docker-compose.generated.yml | sed 's/.*"\([0-9]*\):.*/\1/' | sort -u))

    if [ ${#gpu_ports[@]} -eq 0 ]; then
        echo -e "${RED}❌ 未找到GPU实例端口配置${NC}"
        return 1
    fi

    echo "检测到 ${#gpu_ports[@]} 个GPU实例端口: ${gpu_ports[*]}"

    for port in "${gpu_ports[@]}"; do
        echo -n "等待GPU实例 $port 启动..."
        for i in {1..180}; do  # 等待最多3分钟
            if curl -f -s http://localhost:$port/llm/asr/health > /dev/null 2>&1; then
                echo -e " ${GREEN}✅${NC}"
                break
            fi
            echo -n "."
            sleep 1
        done
    done
    
    # 等待负载均衡器 (从config.env获取端口)
    nginx_port=$(grep "NGINX_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "8080")
    echo -n "等待负载均衡器启动(端口$nginx_port)..."
    for i in {1..60}; do
        if curl -f -s http://localhost:$nginx_port/health > /dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            break
        fi
        echo -n "."
        sleep 1
    done
    
    echo -e "${GREEN}✅ 所有服务启动完成${NC}"
}

# 健康检查
health_check() {
    echo -e "${YELLOW}🔍 执行健康检查...${NC}"
    
    # 检查容器状态
    echo -e "\n${CYAN}📦 容器状态:${NC}"
    docker-compose -f docker-compose.generated.yml ps
    
    echo -e "\n${CYAN}🏥 GPU实例健康检查:${NC}"

    # 从生成的配置文件中提取实例信息
    if [ -f "docker-compose.generated.yml" ]; then
        # 提取容器名称和端口的映射
        while IFS= read -r line; do
            if [[ $line =~ container_name:\ (.+) ]]; then
                container_name="${BASH_REMATCH[1]}"
            elif [[ $line =~ -\ \"([0-9]+):[0-9]+\" ]] && [ -n "$container_name" ]; then
                port="${BASH_REMATCH[1]}"

                if curl -f -s http://localhost:$port/llm/asr/health > /dev/null 2>&1; then
                    # 获取详细信息
                    info=$(curl -s http://localhost:$port/llm/asr/health | jq -r '.gpu_memory_gb // "N/A"' 2>/dev/null || echo "N/A")
                    echo -e "  ${GREEN}✅ $container_name (端口$port): GPU内存 ${info}GB${NC}"
                else
                    echo -e "  ${RED}❌ $container_name (端口$port): 不可用${NC}"
                fi
                container_name=""  # 重置
            fi
        done < docker-compose.generated.yml
    else
        echo -e "  ${RED}❌ 未找到生成的配置文件${NC}"
    fi
    
    # 检查负载均衡器 (动态获取端口)
    echo -e "\n${CYAN}⚖️ 负载均衡器:${NC}"
    nginx_port=$(grep "NGINX_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "8080")
    if curl -f -s http://localhost:$nginx_port/health > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 负载均衡器(端口$nginx_port): 正常${NC}"
    else
        echo -e "  ${RED}❌ 负载均衡器(端口$nginx_port): 异常${NC}"
    fi

    # 检查监控服务 (动态获取端口)
    echo -e "\n${CYAN}📊 监控服务:${NC}"
    prometheus_port=$(grep "PROMETHEUS_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "9090")
    if curl -f -s http://localhost:$prometheus_port/-/healthy > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Prometheus(端口$prometheus_port): 正常${NC}"
    else
        echo -e "  ${RED}❌ Prometheus(端口$prometheus_port): 异常${NC}"
    fi

    grafana_port=$(grep "GRAFANA_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "3000")
    if curl -f -s http://localhost:$grafana_port/api/health > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Grafana(端口$grafana_port): 正常${NC}"
    else
        echo -e "  ${RED}❌ Grafana(端口$grafana_port): 异常${NC}"
    fi
    
    echo -e "${GREEN}✅ 健康检查完成${NC}"
}

# 性能测试
performance_test() {
    echo -e "${YELLOW}🧪 运行性能测试...${NC}"
    
    # 创建测试音频文件
    test_file="/tmp/test_audio.wav"
    dd if=/dev/zero of="$test_file" bs=1024 count=100 2>/dev/null
    
    # 获取负载均衡器端口
    nginx_port=$(grep "NGINX_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "8080")

    echo -e "${CYAN}测试配置:${NC}"
    echo "  并发连接: 50"
    echo "  测试时长: 30秒"
    echo "  目标: GPU负载均衡器(端口$nginx_port)"

    # 简单并发测试
    start_time=$(date +%s)
    success_count=0
    total_count=50

    for i in $(seq 1 $total_count); do
        if curl -X POST -F "file=@$test_file" \
           http://localhost:$nginx_port/llm/asr/recognition \
           -s -o /dev/null -w "%{http_code}" | grep -q "200"; then
            ((success_count++))
        fi &
        
        # 控制并发数
        if (( i % 10 == 0 )); then
            wait
        fi
    done
    
    wait
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    success_rate=$(echo "scale=2; $success_count * 100 / $total_count" | bc 2>/dev/null || echo "N/A")
    qps=$(echo "scale=2; $total_count / $duration" | bc 2>/dev/null || echo "N/A")
    
    echo -e "\n${CYAN}📊 测试结果:${NC}"
    echo "  总请求数: $total_count"
    echo "  成功请求: $success_count"
    echo "  成功率: ${success_rate}%"
    echo "  测试时长: ${duration}秒"
    echo "  QPS: $qps"
    
    # 清理测试文件
    rm -f "$test_file"
}

# 显示访问信息
show_access_info() {
    # 从配置文件获取端口信息
    nginx_port=$(grep "NGINX_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "8080")
    prometheus_port=$(grep "PROMETHEUS_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "9090")
    grafana_port=$(grep "GRAFANA_PORT=" config.env | cut -d= -f2 | tr -d '"' || echo "3000")

    echo -e "${GREEN}🎉 FunASR GPU版本百万并发部署成功！${NC}"
    echo ""
    echo -e "${CYAN}🌐 服务访问地址:${NC}"
    echo "  🔗 负载均衡器:     http://localhost:$nginx_port"
    echo "  📊 Prometheus:     http://localhost:$prometheus_port"
    echo "  📈 Grafana:        http://localhost:$grafana_port (admin/admin123)"
    echo ""
    echo -e "${CYAN}🔧 GPU实例直接访问:${NC}"

    # 动态显示GPU实例端口
    if [ -f "docker-compose.generated.yml" ]; then
        while IFS= read -r line; do
            if [[ $line =~ container_name:\ (.+) ]]; then
                container_name="${BASH_REMATCH[1]}"
            elif [[ $line =~ -\ \"([0-9]+):[0-9]+\" ]] && [ -n "$container_name" ]; then
                port="${BASH_REMATCH[1]}"
                echo "  📍 $container_name:      http://localhost:$port"
                container_name=""  # 重置
            fi
        done < docker-compose.generated.yml
    fi

    echo ""
    echo -e "${CYAN}🧪 测试命令:${NC}"
    echo "  # 通过负载均衡器测试"
    echo "  curl -X POST -F 'file=@test.wav' http://localhost:$nginx_port/llm/asr/recognition"
    echo ""
    echo "  # 查看GPU实例健康状态 (使用第一个实例)"
    first_port=$(grep -E "^\s*-\s*\"[0-9]+:[0-9]+\"" docker-compose.generated.yml | head -1 | sed 's/.*"\([0-9]*\):.*/\1/')
    if [ -n "$first_port" ]; then
        echo "  curl http://localhost:$first_port/llm/asr/health"
    fi
    echo ""
    echo "  # 查看集群状态"
    echo "  curl http://localhost:$nginx_port/cluster/health"
    echo ""
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "  查看状态: docker-compose -f docker-compose.generated.yml ps"
    echo "  查看日志: docker-compose -f docker-compose.generated.yml logs -f"
    echo "  重启服务: docker-compose -f docker-compose.generated.yml restart"
    echo "  停止服务: docker-compose -f docker-compose.generated.yml down"
    echo "  重新生成配置: python3 generate-config.py"
    echo ""
    echo -e "${CYAN}📊 架构总结:${NC}"
    echo "  🏗️ 架构: 官方GPU镜像 + 优化server.py"
    echo "  ⚡ GPU实例: 4个GPU × 多进程 = 百万并发"
    echo "  🔄 负载均衡: Nginx一致性哈希"
    echo "  🛡️ 故障隔离: GPU级别隔离"
    echo "  📊 监控完整: Prometheus + Grafana"
    echo ""
    echo -e "${GREEN}✨ FunASR GPU集群已就绪，开始处理百万级并发吧！${NC}"
}

# 主函数
main() {
    case $1 in
        "deploy")
            check_environment
            pull_official_image
            generate_configs
            deploy_gpu_instances
            wait_for_services
            health_check
            show_access_info
            ;;
        "test")
            performance_test
            ;;
        "check")
            health_check
            ;;
        "stop")
            docker-compose -f docker-compose.generated.yml down
            echo -e "${GREEN}✅ GPU实例已停止${NC}"
            ;;
        "clean")
            docker-compose -f docker-compose.generated.yml down -v
            docker system prune -f
            echo -e "${GREEN}✅ 环境已清理${NC}"
            ;;
        *)
            echo "用法: $0 {deploy|test|check|stop|clean}"
            echo ""
            echo "  deploy  - 部署FunASR GPU集群"
            echo "  test    - 运行性能测试"
            echo "  check   - 健康检查"
            echo "  stop    - 停止服务"
            echo "  clean   - 清理环境"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
