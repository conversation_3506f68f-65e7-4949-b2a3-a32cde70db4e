"""
AI Gateway V2 - 高性能API网关
支持百万级并发的AI服务统一接入
"""

import asyncio
import time
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from prometheus_fastapi_instrumentator import Instrumentator

from config.settings import settings
from middleware.auth import AuthMiddleware
from middleware.rate_limit import RateLimitMiddleware
from middleware.circuit_breaker import CircuitBreakerMiddleware
from middleware.tracing import TracingMiddleware
from routers import ocr, tts, asr, llm, health
from shared.cache import CacheManager
from shared.service_discovery import ServiceDiscovery
from shared.monitoring import MetricsCollector


# Prometheus指标
REQUEST_COUNT = Counter('gateway_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('gateway_request_duration_seconds', 'Request duration')
ACTIVE_CONNECTIONS = Gauge('gateway_active_connections', 'Active connections')
CACHE_HITS = Counter('gateway_cache_hits_total', 'Cache hits', ['service'])
CACHE_MISSES = Counter('gateway_cache_misses_total', 'Cache misses', ['service'])


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    print("🚀 AI Gateway V2 启动中...")
    
    # 初始化缓存
    await app.state.cache_manager.initialize()
    print("✅ 缓存系统初始化完成")
    
    # 初始化服务发现
    await app.state.service_discovery.initialize()
    print("✅ 服务发现初始化完成")
    
    # 启动健康检查
    asyncio.create_task(health_check_loop(app))
    print("✅ 健康检查启动完成")
    
    print("🎉 AI Gateway V2 启动完成!")
    
    yield
    
    # 关闭时清理
    print("🛑 AI Gateway V2 关闭中...")
    await app.state.cache_manager.close()
    await app.state.service_discovery.close()
    print("✅ 清理完成")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    app = FastAPI(
        title="AI Gateway V2",
        description="高性能AI服务网关，支持百万级并发",
        version="2.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan
    )
    
    # 初始化组件
    app.state.cache_manager = CacheManager()
    app.state.service_discovery = ServiceDiscovery()
    app.state.metrics_collector = MetricsCollector()
    
    # 添加中间件
    setup_middleware(app)
    
    # 注册路由
    setup_routers(app)
    
    # 设置监控
    setup_monitoring(app)
    
    return app


def setup_middleware(app: FastAPI):
    """设置中间件"""
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Gzip压缩
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # 自定义中间件
    app.add_middleware(TracingMiddleware)
    app.add_middleware(CircuitBreakerMiddleware)
    app.add_middleware(RateLimitMiddleware)
    app.add_middleware(AuthMiddleware)
    
    # 请求处理中间件
    @app.middleware("http")
    async def process_request(request: Request, call_next):
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录开始时间
        start_time = time.time()
        
        # 增加活跃连接数
        ACTIVE_CONNECTIONS.inc()
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 记录指标
            duration = time.time() - start_time
            REQUEST_DURATION.observe(duration)
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status=response.status_code
            ).inc()
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Processing-Time"] = f"{duration:.3f}s"
            
            return response
            
        except Exception as e:
            # 记录错误
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.url.path,
                status=500
            ).inc()
            
            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": str(e),
                    "request_id": request_id
                }
            )
        finally:
            # 减少活跃连接数
            ACTIVE_CONNECTIONS.dec()


def setup_routers(app: FastAPI):
    """设置路由"""
    
    # 健康检查
    app.include_router(health.router, prefix="/health", tags=["Health"])
    
    # AI服务路由
    app.include_router(ocr.router, prefix="/api/v1/ocr", tags=["OCR"])
    app.include_router(tts.router, prefix="/api/v1/tts", tags=["TTS"])
    app.include_router(asr.router, prefix="/api/v1/asr", tags=["ASR"])
    app.include_router(llm.router, prefix="/api/v1/llm", tags=["LLM"])
    
    # 监控指标端点
    @app.get("/metrics")
    async def metrics():
        """Prometheus指标端点"""
        return generate_latest()
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径信息"""
        return {
            "service": "AI Gateway V2",
            "version": "2.0.0",
            "status": "running",
            "docs": "/docs",
            "metrics": "/metrics"
        }


def setup_monitoring(app: FastAPI):
    """设置监控"""
    
    # Prometheus监控
    if settings.PROMETHEUS_ENABLED:
        instrumentator = Instrumentator()
        instrumentator.instrument(app)
        instrumentator.expose(app, endpoint="/prometheus")


async def health_check_loop(app: FastAPI):
    """健康检查循环"""
    while True:
        try:
            # 检查服务健康状态
            await app.state.service_discovery.health_check_all()
            await asyncio.sleep(30)  # 30秒检查一次
        except Exception as e:
            print(f"健康检查失败: {e}")
            await asyncio.sleep(10)  # 失败时10秒后重试


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        workers=1 if settings.DEBUG else 4,
        loop="uvloop",
        http="httptools",
        access_log=settings.DEBUG
    )
