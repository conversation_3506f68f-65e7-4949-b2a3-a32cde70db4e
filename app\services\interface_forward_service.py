import httpx
from fastapi.responses import StreamingResponse
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, Request
from app.utils.util import create_api_forward_url
from typing import Dict, Any
from app.services.abs_service import AbsService


class InterfaceForwardService(AbsService):
    def __init__(self):
        super().__init__()
        self.client = httpx.AsyncClient()
        self.route_config = self.load_config()


    def load_config(self):
        """从配置中心加载（可扩展为数据库读取）"""
        import app.core.forward_route_config as route_config
        return route_config.ROUTE_MAPPING

    async def route_request(self, request: Request):
        # 1. 匹配最长的前缀规则
        path = request.url.path
        print('full_path', request.path_params["full_path"])
        request_url_prefix = path.replace("/" + request.path_params["full_path"], '')
        print('request_url_prefix', request_url_prefix)
        existed_prefix = [each for each in self.route_config.keys() if each == request_url_prefix]

        if len(existed_prefix) == 0:
            raise HTTPException(404, "未配置的转发路径")
        
        print('existed_prefix', existed_prefix)

        method = request.method.upper()
        try:
            if method == "POST":
                client_body: Dict[str, Any] = await request.json()
            elif method == "GET":
                client_body = dict(request.query_params)
        except Exception as e:
            client_body = {}
                
        # 3. 构建转发URL
        target_url, config, model = create_api_forward_url(existed_prefix[0], path, client_body.get("model", None))
        print('target_url:', target_url)
        self.logger.info(f"Forwarding request to {target_url}, headers: {request.headers}")

        headers=self.filter_headers(request.headers, config)
        headers.update({
            'X-MODEL': model 
        })

        resp = await self.client.request(
            method=request.method,
            url=target_url,
            headers=headers,
            content=await request.body(),
            timeout=config.get("timeout", 30),
        )
        resp.raise_for_status()
        content_type = resp.headers.get("content-type", "")

        # 根据响应类型构建返回内容
        if "application/json" in content_type:
            # JSON响应保持原样
            self.logger.info(f"Response from {target_url}: {resp.json()}")
            return resp.json()
        # 如何返回文件流
        else:
            async def stream_generator():
                async for chunk in resp.aiter_bytes(chunk_size=65536):
                    yield chunk
            self.logger.info(f"Response from {target_url}: Streaming response")
            return StreamingResponse(
                content=stream_generator(),
                status_code=resp.status_code,
                headers={
                    'Content-Type': resp.headers.get('Content-Type', 'multipart/form-data'),
                    'Content-Disposition': resp.headers.get('Content-Disposition', '')
                }
            )

    def filter_headers(self, headers: dict, config: dict) -> dict:
        """根据配置过滤请求头"""
        filtered = {}
        for k, v in headers.items():
            # 全局过滤规则
            if k.lower() in ["", ""] and not config.get("auth_required"):
                continue
            filtered[k] = v
        return filtered
