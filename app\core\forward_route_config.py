ROUTE_MAPPING = {
    "/llm/ocr/docToMarkdown": {
        "timeout": 300,
        "allowed_methods": ["GET", "POST"],
        "default": "mineru",
        "model_server_dict": {
            "mineru": {
                "servers": [
                    {"url": "http://***************:20060", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            }
        }
    },
    "/llm/ocr/doc_to_md": {
        "timeout": 300,
        "allowed_methods": ["GET", "POST"],
        "default": "monkey_ocr",
        "model_server_dict": {
            "monkey_ocr": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 2, "status": "active"}
                ],
                "load_balance_strategy": "weighted"
            }
        }
    },
    "/llm/tts": {
        "timeout": 300,
        "allowed_methods": ["GET", "POST"],
        "default": "edge",
        "model_server_dict": {
            "edge": {
                "servers": [
                    {"url": "http://***************:20060", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            },
            "cosyvoice": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            }
        }
    },

    "/llm/asr": {
        "timeout": 300,
        "allowed_methods": ["GET", "POST"],
        "default": "fun",
        "model_server_dict": {
            "fun": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            }
        }
    },
    "/llm/face": {
        "timeout": 120,
        "allowed_methods": ["GET", "POST"],
        "default": "face_recognition",
        "model_server_dict": {
            "face_recognition": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            }
        }
    },
    "/llm/multimodal/image": {
        "timeout": 300,
        "allowed_methods": ["GET", "POST"],
        "default": "multimodal_image",
        "model_server_dict": {
            "multimodal_image": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            }
        }
    },
    "/llm/openai": {
        "timeout": 300,
        "allowed_methods": ["GET", "POST"],
        "default_business_model_dict": {
            "default": "Qwen3-8B",
            "matrix_coder": "Seed-Coder-8B-Instruct",
            "matrix_chat": "Qwen3-8B",
        },
        "model_server_dict": {
            "Qwen3-8B": {
                "servers": [
                    {"url": "http://***************:20060", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            },
            "Qwen3-32B": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 2, "status": "active"}
                ],
                "load_balance_strategy": "weighted"
            },
            "InternVL3-38B": {
                "servers": [
                    {"url": "http://***************:20060", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            },
            "Seed-Coder-8B-Instruct": {
                "servers": [
                    {"url": "http://**************:10099", "weight": 1, "status": "active"}
                ],
                "load_balance_strategy": "round_robin"
            }
        },
        "model_description_dict": {
            "Qwen3-8B": "通义千问3通用大语言模型，80亿参数，可切换是否使用思考模式",
            "Seed-Coder-8B-Instruct": "字节跳动纯编程模型，80亿参数，可生成代码",
            "Qwen3-32B": "通义千问320亿参数通用大语言模型，语言理解能力突出",
            "InternVL3-38B": "书生·万象3.0是上海人工智能实验室发布的多模态大模型，380亿参数，可输入图片和视频进行理解",
        }
    },
}

# 负载均衡配置
LOAD_BALANCE_CONFIG = {
    "health_check": {
        "enabled": True,
        "interval": 30,  # 秒
        "timeout": 5,
        "endpoint": "/health"
    },
    "retry": {
        "max_attempts": 3,
        "backoff_factor": 1.5
    },
    "circuit_breaker": {
        "failure_threshold": 5,
        "recovery_timeout": 60,
        "half_open_max_calls": 3
    }
}
