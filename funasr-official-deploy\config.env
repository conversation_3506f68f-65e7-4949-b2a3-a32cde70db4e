# FunASR官方部署配置文件

# 基础配置
FUNASR_IMAGE=registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
COMPOSE_PROJECT_NAME=funasr-official

# 服务端口配置
NGINX_PORT=8080
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# GPU实例配置
GPU_COUNT=4                    # GPU实例数量
BASE_PORT=10095               # 起始端口号 (10095, 10096, 10097, 10098)

# 性能配置
WORKERS_PER_INSTANCE=4        # 每个实例的工作进程数
MAX_REQUESTS=1000            # 每个进程处理的最大请求数
TIMEOUT=300                  # 请求超时时间(秒)

# 资源限制
MEMORY_LIMIT=16G             # 每个实例的内存限制
CPU_LIMIT=8.0               # 每个实例的CPU限制

# 负载均衡配置
LB_ALGORITHM=round_robin     # 负载均衡算法: round_robin, least_conn, hash
LB_WEIGHT=1                 # 每个实例的权重
LB_MAX_FAILS=3              # 最大失败次数
LB_FAIL_TIMEOUT=30s         # 失败超时时间

# 监控配置
ENABLE_PROMETHEUS=true       # 是否启用Prometheus监控
ENABLE_GRAFANA=true         # 是否启用Grafana监控

# 日志配置
LOG_LEVEL=info              # 日志级别: debug, info, warning, error
LOG_RETENTION_DAYS=7        # 日志保留天数

# 健康检查配置
HEALTH_CHECK_INTERVAL=30s   # 健康检查间隔
HEALTH_CHECK_TIMEOUT=10s    # 健康检查超时
HEALTH_CHECK_RETRIES=3      # 健康检查重试次数
HEALTH_CHECK_START_PERIOD=120s  # 启动等待时间
