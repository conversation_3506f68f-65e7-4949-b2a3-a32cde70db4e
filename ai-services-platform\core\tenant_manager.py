"""
租户管理器
管理多租户配置、认证、权限等
"""

import asyncio
import hashlib
import json
import time
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta

import jwt
import aioredis
from pydantic import BaseModel

from config.settings import settings


@dataclass
class TenantConfig:
    """租户配置"""
    tenant_id: str
    name: str
    api_key: str
    allowed_services: List[str]
    quota_limits: Dict[str, int]  # 每日配额限制
    priority_level: int = 1  # 优先级 1-10
    cost_model: str = "pay_per_use"  # 计费模式
    cache_ttl: Dict[str, int] = field(default_factory=dict)
    is_admin: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    status: str = "active"  # active, suspended, deleted


class TenantManager:
    """租户管理器"""
    
    def __init__(self):
        self.redis: Optional[aioredis.Redis] = None
        self.tenant_cache: Dict[str, TenantConfig] = {}
        self.token_cache: Dict[str, str] = {}  # token -> tenant_id
        
    async def initialize(self):
        """初始化"""
        # 连接Redis
        self.redis = aioredis.from_url(settings.REDIS_URL)
        
        # 加载租户配置
        await self._load_tenants()
        
        # 创建默认租户
        await self._create_default_tenants()
        
        print("✅ 租户管理器初始化完成")
    
    async def close(self):
        """关闭连接"""
        if self.redis:
            await self.redis.close()
    
    async def _load_tenants(self):
        """从Redis加载租户配置"""
        try:
            tenant_keys = await self.redis.keys("tenant:*")
            for key in tenant_keys:
                tenant_data = await self.redis.hgetall(key)
                if tenant_data:
                    tenant_config = self._deserialize_tenant(tenant_data)
                    self.tenant_cache[tenant_config.tenant_id] = tenant_config
            
            print(f"加载了 {len(self.tenant_cache)} 个租户配置")
        except Exception as e:
            print(f"加载租户配置失败: {e}")
    
    async def _create_default_tenants(self):
        """创建默认租户"""
        default_tenants = [
            {
                "tenant_id": "matrix_education",
                "name": "Matrix教育平台",
                "allowed_services": ["ocr", "tts", "asr", "llm", "translate"],
                "quota_limits": {
                    "ocr": 10000,
                    "tts": 5000,
                    "asr": 3000,
                    "llm": 1000,
                    "translate": 8000
                },
                "priority_level": 5,
                "cache_ttl": {
                    "ocr": 3600,
                    "tts": 7200,
                    "asr": 1800,
                    "llm": 1800,
                    "translate": 3600
                }
            },
            {
                "tenant_id": "medical_platform",
                "name": "医疗平台",
                "allowed_services": ["ocr", "tts", "asr", "llm", "translate"],
                "quota_limits": {
                    "ocr": 15000,
                    "tts": 3000,
                    "asr": 5000,
                    "llm": 2000,
                    "translate": 6000
                },
                "priority_level": 8,  # 医疗平台优先级更高
                "cache_ttl": {
                    "ocr": 1800,  # 医疗数据缓存时间较短
                    "tts": 3600,
                    "asr": 900,
                    "llm": 900,
                    "translate": 1800
                }
            },
            {
                "tenant_id": "admin",
                "name": "管理员",
                "allowed_services": ["ocr", "tts", "asr", "llm", "translate"],
                "quota_limits": {
                    "ocr": 100000,
                    "tts": 100000,
                    "asr": 100000,
                    "llm": 100000,
                    "translate": 100000
                },
                "priority_level": 10,
                "is_admin": True
            }
        ]
        
        for tenant_data in default_tenants:
            if tenant_data["tenant_id"] not in self.tenant_cache:
                await self.create_tenant(tenant_data)
    
    async def create_tenant(self, tenant_data: dict) -> TenantConfig:
        """创建租户"""
        tenant_id = tenant_data["tenant_id"]
        
        # 生成API密钥
        api_key = self._generate_api_key(tenant_id)
        
        # 创建租户配置
        tenant_config = TenantConfig(
            tenant_id=tenant_id,
            name=tenant_data["name"],
            api_key=api_key,
            allowed_services=tenant_data.get("allowed_services", []),
            quota_limits=tenant_data.get("quota_limits", {}),
            priority_level=tenant_data.get("priority_level", 1),
            cost_model=tenant_data.get("cost_model", "pay_per_use"),
            cache_ttl=tenant_data.get("cache_ttl", {}),
            is_admin=tenant_data.get("is_admin", False)
        )
        
        # 保存到Redis
        await self._save_tenant(tenant_config)
        
        # 更新缓存
        self.tenant_cache[tenant_id] = tenant_config
        
        print(f"创建租户: {tenant_id} - {tenant_config.name}")
        return tenant_config
    
    async def get_tenant_config(self, tenant_id: str) -> Optional[TenantConfig]:
        """获取租户配置"""
        # 先从缓存获取
        if tenant_id in self.tenant_cache:
            return self.tenant_cache[tenant_id]
        
        # 从Redis获取
        try:
            tenant_data = await self.redis.hgetall(f"tenant:{tenant_id}")
            if tenant_data:
                tenant_config = self._deserialize_tenant(tenant_data)
                self.tenant_cache[tenant_id] = tenant_config
                return tenant_config
        except Exception as e:
            print(f"获取租户配置失败: {e}")
        
        return None
    
    async def validate_token(self, token: str) -> Optional[str]:
        """验证令牌并返回租户ID"""
        # 检查缓存
        if token in self.token_cache:
            return self.token_cache[token]
        
        try:
            # 解码JWT令牌
            payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            tenant_id = payload.get("tenant_id")
            
            if tenant_id and tenant_id in self.tenant_cache:
                # 缓存令牌
                self.token_cache[token] = tenant_id
                return tenant_id
        except jwt.InvalidTokenError:
            pass
        
        # 尝试作为API密钥验证
        for tenant_id, config in self.tenant_cache.items():
            if config.api_key == token:
                self.token_cache[token] = tenant_id
                return tenant_id
        
        return None
    
    async def generate_token(self, tenant_id: str, expires_in: int = 3600) -> str:
        """生成访问令牌"""
        payload = {
            "tenant_id": tenant_id,
            "exp": datetime.utcnow() + timedelta(seconds=expires_in),
            "iat": datetime.utcnow()
        }
        
        token = jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
        
        # 缓存令牌
        self.token_cache[token] = tenant_id
        
        return token
    
    async def update_tenant(self, tenant_id: str, updates: dict) -> bool:
        """更新租户配置"""
        tenant_config = await self.get_tenant_config(tenant_id)
        if not tenant_config:
            return False
        
        # 更新配置
        for key, value in updates.items():
            if hasattr(tenant_config, key):
                setattr(tenant_config, key, value)
        
        tenant_config.updated_at = datetime.now()
        
        # 保存更新
        await self._save_tenant(tenant_config)
        
        # 更新缓存
        self.tenant_cache[tenant_id] = tenant_config
        
        return True
    
    async def get_all_tenants(self) -> List[TenantConfig]:
        """获取所有租户"""
        return list(self.tenant_cache.values())
    
    def _generate_api_key(self, tenant_id: str) -> str:
        """生成API密钥"""
        data = f"{tenant_id}:{time.time()}:{settings.JWT_SECRET_KEY}"
        return hashlib.sha256(data.encode()).hexdigest()
    
    def _serialize_tenant(self, tenant_config: TenantConfig) -> dict:
        """序列化租户配置"""
        return {
            "tenant_id": tenant_config.tenant_id,
            "name": tenant_config.name,
            "api_key": tenant_config.api_key,
            "allowed_services": json.dumps(tenant_config.allowed_services),
            "quota_limits": json.dumps(tenant_config.quota_limits),
            "priority_level": str(tenant_config.priority_level),
            "cost_model": tenant_config.cost_model,
            "cache_ttl": json.dumps(tenant_config.cache_ttl),
            "is_admin": str(tenant_config.is_admin),
            "created_at": tenant_config.created_at.isoformat(),
            "updated_at": tenant_config.updated_at.isoformat(),
            "status": tenant_config.status
        }
    
    def _deserialize_tenant(self, data: dict) -> TenantConfig:
        """反序列化租户配置"""
        return TenantConfig(
            tenant_id=data["tenant_id"],
            name=data["name"],
            api_key=data["api_key"],
            allowed_services=json.loads(data["allowed_services"]),
            quota_limits=json.loads(data["quota_limits"]),
            priority_level=int(data["priority_level"]),
            cost_model=data["cost_model"],
            cache_ttl=json.loads(data.get("cache_ttl", "{}")),
            is_admin=data.get("is_admin", "False").lower() == "true",
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            status=data.get("status", "active")
        )
    
    async def _save_tenant(self, tenant_config: TenantConfig):
        """保存租户配置到Redis"""
        key = f"tenant:{tenant_config.tenant_id}"
        data = self._serialize_tenant(tenant_config)
        await self.redis.hset(key, mapping=data)
