from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class APIKeyAuthMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.trust_paths = [
            "/docs",
            "/swagger", 
            "/redoc",
            "/openapi.json",
            "/health"
        ]

    async def dispatch(self, request: Request, call_next):
        # 如果未启用验证，直接通过
        if not settings.ENABLE_API_KEY_AUTH:
            return await call_next(request)
            
        # 检查是否为免验证路径
        if any(request.url.path.startswith(path) for path in self.trust_paths):
            return await call_next(request)
            
        # 获取API key
        api_key = request.headers.get("X-API-Key") or request.headers.get("Authorization")
        
        # 如果是Authorization header，提取Bearer token
        if api_key and api_key.startswith("Bearer "):
            api_key = api_key[7:]
            
        # 验证API key
        if not api_key or api_key not in settings.api_keys:
            logger.warning(f"Invalid API key attempt from {request.client.host}: {api_key}")
            return JSONResponse(
                status_code=401,
                content={"status": 401, "msg": "Invalid or missing API key", "data": {}}
            )
            
        logger.info(f"Valid API key used: {api_key[:10]}...")
        return await call_next(request)