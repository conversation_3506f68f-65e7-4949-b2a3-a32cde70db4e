from pydantic import BaseModel, Field
from typing import List, Literal, Optional

# 定义可用的翻译方向
LANGUAGE_PAIRS = Literal["zh_to_en", "en_to_zh"]

class TranslateTextToTextRequest(BaseModel):
    """
    翻译请求模型
    - text_list: 待翻译文本列表 (必填)
    """
    text_list: List[str] = Field(..., min_length=1, example=["这是文本"]),
    # 翻译方向必须是LANGUAGE_PAIRS中的一个
    language_pair: LANGUAGE_PAIRS = Field(..., example="zh_to_en", description="翻译方向")
    llm_params: dict = Field(
        {},
        example={
            "model": "Qwen2-7B-Instruct",
            "stream": False,
            "max_tokens": 1000,
            "temperature": 0.0,
            "top_p": 1.0,
        },
        description="llm模型参数"
    )


# 评分响应模型
class TranslateTextToTextResponse(BaseModel):
    translate_list: List[str] = Field(..., example=["这是翻译后的文本"])


class TranslateAudioToTextResponse(BaseModel):
    translate_text: str = Field(..., example=["这是翻译后的文本"])


class TranslateTextToAudioRequest(BaseModel):
    text: str = Field(..., example=["这是文本"])
    speaker: str = Field("zh-CN-XiaoxiaoNeural", example=["xiaoyan"])
    language_pair: LANGUAGE_PAIRS = Field(..., example="zh_to_en", description="翻译方向")


