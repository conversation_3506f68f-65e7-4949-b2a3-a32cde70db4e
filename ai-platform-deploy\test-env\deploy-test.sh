#!/bin/bash

# AI服务平台测试环境部署脚本
# 适用于3台服务器的小规模测试

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧪 AI服务平台测试环境部署${NC}"
echo "适用于3台服务器 (1台普通 + 2台算力)"
echo "=================================="

# 检查环境
check_environment() {
    echo -e "${YELLOW}🔍 检查部署环境...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 配置服务器地址
configure_servers() {
    echo -e "${YELLOW}⚙️ 配置服务器地址...${NC}"
    
    # 创建环境配置文件
    cat > .env.test << 'EOF'
# 测试环境配置

# 算力服务器地址 (请修改为实际IP)
COMPUTE_SERVER_1=*************
COMPUTE_SERVER_2=*************

# TTS服务端点
TTS_BACKENDS=http://*************:8080,http://*************:8080

# LLM服务端点
LLM_BACKENDS=http://*************:8081,http://*************:8081

# OCR服务端点
OCR_BACKENDS=http://*************:8082

# ASR服务端点
ASR_BACKENDS=http://*************:8082

# Redis配置
REDIS_URL=redis://redis:6379

# 调试模式
DEBUG=true
LOG_LEVEL=DEBUG
EOF

    echo -e "${YELLOW}📝 请编辑 .env.test 文件，配置您的算力服务器IP地址${NC}"
    echo -e "${YELLOW}当前配置的算力服务器地址：${NC}"
    echo "  算力服务器1: *************"
    echo "  算力服务器2: *************"
    
    read -p "是否需要修改服务器地址？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}请手动编辑 .env.test 文件后重新运行脚本${NC}"
        exit 0
    fi
    
    echo -e "${GREEN}✅ 服务器配置完成${NC}"
}

# 创建监控配置
create_monitoring_config() {
    echo -e "${YELLOW}📊 创建监控配置...${NC}"
    
    mkdir -p monitoring
    
    # Prometheus配置
    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-gateway'
    static_configs:
      - targets: ['gateway:8000']
  
  - job_name: 'tts-service'
    static_configs:
      - targets: ['tts-service:8002']
  
  - job_name: 'education-app'
    static_configs:
      - targets: ['education-app:8100']
  
  - job_name: 'medical-app'
    static_configs:
      - targets: ['medical-app:8200']
  
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # 算力服务器监控 (需要在算力服务器上部署node_exporter)
  - job_name: 'compute-servers'
    static_configs:
      - targets: ['*************:9100', '*************:9100']
EOF

    # Nginx配置
    mkdir -p nginx
    cat > nginx/nginx.test.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream ai_gateway {
        server gateway:8000;
    }
    
    upstream education_app {
        server education-app:8100;
    }
    
    upstream medical_app {
        server medical-app:8200;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # AI网关
        location /api/ {
            proxy_pass http://ai_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        
        # 教育平台
        location /education/ {
            proxy_pass http://education_app/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # 医疗平台
        location /medical/ {
            proxy_pass http://medical_app/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # 监控面板
        location /grafana/ {
            proxy_pass http://grafana:3000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
EOF

    echo -e "${GREEN}✅ 监控配置创建完成${NC}"
}

# 部署服务
deploy_services() {
    echo -e "${YELLOW}🚀 部署测试环境服务...${NC}"
    
    # 使用测试环境配置
    export COMPOSE_FILE=test-env/docker-compose.test.yml
    
    # 拉取最新镜像
    echo -e "${YELLOW}📥 拉取最新镜像...${NC}"
    docker-compose pull
    
    # 启动服务
    echo -e "${YELLOW}🔄 启动服务...${NC}"
    docker-compose up -d
    
    echo -e "${GREEN}✅ 服务部署完成${NC}"
}

# 等待服务启动
wait_for_services() {
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    
    # 等待基础服务
    sleep 30
    
    # 检查服务状态
    services=("gateway:8000" "tts-service:8002" "education-app:8100" "medical-app:8200")
    
    for service in "${services[@]}"; do
        container=$(echo $service | cut -d: -f1)
        port=$(echo $service | cut -d: -f2)
        
        echo -n "等待 $container 启动..."
        for i in {1..30}; do
            if docker-compose exec -T $container curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
                echo -e " ${GREEN}✅${NC}"
                break
            fi
            echo -n "."
            sleep 2
        done
    done
    
    echo -e "${GREEN}✅ 服务启动完成${NC}"
}

# 健康检查
health_check() {
    echo -e "${YELLOW}🔍 执行健康检查...${NC}"
    
    # 检查容器状态
    echo "容器状态："
    docker-compose ps
    
    echo -e "\n服务健康检查："
    
    services=(
        "http://localhost:8000/health:AI网关"
        "http://localhost:8002/health:TTS服务"
        "http://localhost:8100/health:教育平台"
        "http://localhost:8200/health:医疗平台"
    )
    
    for service in "${services[@]}"; do
        url=$(echo $service | cut -d: -f1-2)
        name=$(echo $service | cut -d: -f3)
        
        if curl -f -s $url > /dev/null 2>&1; then
            echo -e "  ${GREEN}✅ $name${NC}"
        else
            echo -e "  ${RED}❌ $name${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ 健康检查完成${NC}"
}

# 运行测试
run_tests() {
    echo -e "${YELLOW}🧪 运行基础功能测试...${NC}"
    
    echo "1. 测试AI网关服务列表..."
    curl -s http://localhost:8000/api/v1/services | jq . || echo "服务列表获取失败"
    
    echo -e "\n2. 测试TTS服务..."
    curl -X POST http://localhost:8000/api/v1/tts \
        -H "Content-Type: application/json" \
        -H "X-Tenant-ID: test" \
        -d '{"text": "测试语音合成", "voice": "zh-CN-XiaoxiaoNeural"}' \
        -s | jq . || echo "TTS测试失败"
    
    echo -e "\n3. 测试教育平台..."
    curl -s http://localhost:8100/health | jq . || echo "教育平台测试失败"
    
    echo -e "\n${GREEN}✅ 基础测试完成${NC}"
}

# 显示访问信息
show_access_info() {
    echo -e "${GREEN}🎉 测试环境部署成功！${NC}"
    echo ""
    echo "📋 服务访问地址："
    echo "  🌐 统一入口:       http://localhost"
    echo "  🔗 AI网关:         http://localhost:8000"
    echo "  🔧 TTS服务:        http://localhost:8002"
    echo "  📚 教育平台:       http://localhost:8100"
    echo "  🏥 医疗平台:       http://localhost:8200"
    echo "  📊 监控面板:       http://localhost:3000 (admin/admin123)"
    echo "  📈 Prometheus:     http://localhost:9090"
    echo ""
    echo "📖 API文档："
    echo "  🔗 AI网关API:      http://localhost:8000/docs"
    echo "  🔗 TTS服务API:     http://localhost:8002/docs"
    echo "  🔗 教育平台API:    http://localhost:8100/docs"
    echo "  🔗 医疗平台API:    http://localhost:8200/docs"
    echo ""
    echo "🧪 测试命令："
    echo "  curl http://localhost:8000/health"
    echo "  curl http://localhost:8000/api/v1/services"
    echo ""
    echo "🔧 管理命令："
    echo "  查看状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f gateway"
    echo "  停止服务: docker-compose down"
    echo ""
    echo "⚠️  注意事项："
    echo "  1. 确保算力服务器已启动AI模型服务"
    echo "  2. 检查网络连通性到算力服务器"
    echo "  3. 监控资源使用情况"
    echo ""
    echo -e "${GREEN}✨ 测试环境已就绪，开始您的AI服务测试吧！${NC}"
}

# 主函数
main() {
    case $1 in
        "deploy")
            check_environment
            configure_servers
            create_monitoring_config
            deploy_services
            wait_for_services
            health_check
            run_tests
            show_access_info
            ;;
        "deploy-multi")
            check_environment
            configure_servers
            create_monitoring_config
            echo -e "${YELLOW}🚀 部署多实例版本...${NC}"
            export COMPOSE_PROFILES=multi-instance
            deploy_services
            wait_for_services
            health_check
            run_tests
            show_access_info
            ;;
        "performance")
            echo -e "${YELLOW}🧪 运行性能测试...${NC}"
            python3 test-env/performance-test.py --concurrent 100 --duration 60
            ;;
        "test")
            run_tests
            ;;
        "check")
            health_check
            ;;
        "stop")
            docker-compose down
            echo -e "${GREEN}✅ 测试环境已停止${NC}"
            ;;
        "clean")
            docker-compose down -v
            docker system prune -f
            echo -e "${GREEN}✅ 测试环境已清理${NC}"
            ;;
        *)
            echo "用法: $0 {deploy|deploy-multi|performance|test|check|stop|clean}"
            echo ""
            echo "  deploy       - 部署测试环境 (单实例)"
            echo "  deploy-multi - 部署测试环境 (多实例)"
            echo "  performance  - 运行性能测试"
            echo "  test         - 运行功能测试"
            echo "  check        - 健康检查"
            echo "  stop         - 停止服务"
            echo "  clean        - 清理环境"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
