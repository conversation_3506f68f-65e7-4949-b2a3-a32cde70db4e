#!/bin/bash

# AI服务平台多节点多进程部署脚本
# TTS服务使用多节点多进程架构

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🚀 AI服务平台多节点多进程部署${NC}"
echo "TTS服务: 3个节点 × 多进程 = 超高并发"
echo "========================================"

# 检查环境
check_environment() {
    echo -e "${YELLOW}🔍 检查部署环境...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose未安装${NC}"
        exit 1
    fi
    
    # 检查系统资源
    total_memory=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    cpu_cores=$(nproc)
    
    echo -e "${CYAN}💻 系统资源:${NC}"
    echo "  内存: ${total_memory}GB"
    echo "  CPU核心: ${cpu_cores}个"
    
    if [ "$total_memory" -lt 8 ]; then
        echo -e "${YELLOW}⚠️ 建议至少8GB内存用于多节点部署${NC}"
    fi
    
    if [ "$cpu_cores" -lt 4 ]; then
        echo -e "${YELLOW}⚠️ 建议至少4个CPU核心用于多进程部署${NC}"
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 配置多节点参数
configure_multi_node() {
    echo -e "${YELLOW}⚙️ 配置多节点参数...${NC}"
    
    # 创建环境配置文件
    cat > .env.multi-node << 'EOF'
# 多节点多进程TTS服务配置

# 算力服务器地址
COMPUTE_SERVER_1=192.168.1.100
COMPUTE_SERVER_2=192.168.1.101

# TTS后端服务
TTS_BACKENDS=http://192.168.1.100:8080,http://192.168.1.101:8080

# LLM后端服务
LLM_BACKENDS=http://192.168.1.100:8081,http://192.168.1.101:8081

# OCR和ASR后端
OCR_BACKENDS=http://192.168.1.100:8082
ASR_BACKENDS=http://192.168.1.101:8082

# Redis配置
REDIS_URL=redis://redis:6379

# 环境配置
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=info

# TTS节点1配置 (高性能)
TTS_NODE_1_WORKERS=4
TTS_NODE_1_CONNECTIONS=1000
TTS_NODE_1_MEMORY=6G
TTS_NODE_1_CPU=3.0

# TTS节点2配置 (标准)
TTS_NODE_2_WORKERS=3
TTS_NODE_2_CONNECTIONS=800
TTS_NODE_2_MEMORY=5G
TTS_NODE_2_CPU=2.5

# TTS节点3配置 (轻量)
TTS_NODE_3_WORKERS=2
TTS_NODE_3_CONNECTIONS=600
TTS_NODE_3_MEMORY=4G
TTS_NODE_3_CPU=2.0
EOF

    echo -e "${CYAN}📋 多节点配置:${NC}"
    echo "  TTS节点1: 4进程 × 1000连接 = 4000并发"
    echo "  TTS节点2: 3进程 × 800连接  = 2400并发"
    echo "  TTS节点3: 2进程 × 600连接  = 1200并发"
    echo "  总并发能力: 7600+ 连接"
    
    read -p "是否需要修改算力服务器地址？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}请手动编辑 .env.multi-node 文件后重新运行脚本${NC}"
        exit 0
    fi
    
    echo -e "${GREEN}✅ 多节点配置完成${NC}"
}

# 创建必要的目录和配置
create_configs() {
    echo -e "${YELLOW}📁 创建配置文件...${NC}"
    
    # 确保目录存在
    mkdir -p nginx monitoring/grafana/{dashboards,datasources}
    
    # 检查配置文件
    if [ ! -f "nginx/tts-lb.conf" ]; then
        echo -e "${RED}❌ 缺少TTS负载均衡配置文件${NC}"
        exit 1
    fi
    
    if [ ! -f "monitoring/prometheus-multi-node.yml" ]; then
        echo -e "${RED}❌ 缺少Prometheus多节点配置文件${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 配置文件检查完成${NC}"
}

# 部署多节点服务
deploy_multi_node_services() {
    echo -e "${YELLOW}🚀 部署多节点多进程服务...${NC}"
    
    # 使用多节点配置文件
    export COMPOSE_FILE=test-env/docker-compose.multi-node.yml
    
    # 拉取最新镜像
    echo -e "${YELLOW}📥 拉取最新镜像...${NC}"
    docker-compose pull
    
    # 启动基础服务
    echo -e "${YELLOW}🔄 启动基础服务 (Redis)...${NC}"
    docker-compose up -d redis
    sleep 10
    
    # 启动TTS节点 (按优先级顺序)
    echo -e "${YELLOW}🔄 启动TTS节点1 (主节点)...${NC}"
    docker-compose up -d tts-node-1
    sleep 15
    
    echo -e "${YELLOW}🔄 启动TTS节点2 (次节点)...${NC}"
    docker-compose up -d tts-node-2
    sleep 15
    
    echo -e "${YELLOW}🔄 启动TTS节点3 (备用节点)...${NC}"
    docker-compose up -d tts-node-3
    sleep 15
    
    # 启动ASR服务
    echo -e "${YELLOW}🔄 启动ASR语音识别服务...${NC}"
    docker-compose up -d asr-service
    sleep 15

    # 启动负载均衡器
    echo -e "${YELLOW}🔄 启动TTS负载均衡器...${NC}"
    docker-compose up -d nginx-tts-lb
    sleep 10

    # 启动网关和业务应用
    echo -e "${YELLOW}🔄 启动网关和业务应用...${NC}"
    docker-compose up -d gateway education-app medical-app
    sleep 20
    
    # 启动监控服务
    echo -e "${YELLOW}🔄 启动监控服务...${NC}"
    docker-compose up -d prometheus grafana
    sleep 15
    
    echo -e "${GREEN}✅ 多节点服务部署完成${NC}"
}

# 等待服务就绪
wait_for_multi_node_services() {
    echo -e "${YELLOW}⏳ 等待多节点服务启动...${NC}"
    
    # TTS节点健康检查
    tts_nodes=("tts-node-1:8002" "tts-node-2:8002" "tts-node-3:8002")

    for node in "${tts_nodes[@]}"; do
        container=$(echo $node | cut -d: -f1)
        port=$(echo $node | cut -d: -f2)

        echo -n "等待 $container 启动..."
        for i in {1..60}; do
            if docker-compose exec -T $container curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
                echo -e " ${GREEN}✅${NC}"
                break
            fi
            echo -n "."
            sleep 2
        done
    done

    # ASR服务健康检查
    echo -n "等待 asr-service 启动..."
    for i in {1..60}; do
        if docker-compose exec -T asr-service curl -f -s http://localhost:8003/health > /dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    # 负载均衡器检查
    echo -n "等待负载均衡器启动..."
    for i in {1..30}; do
        if curl -f -s http://localhost:8080/health > /dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    # 网关检查
    echo -n "等待AI网关启动..."
    for i in {1..30}; do
        if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
            echo -e " ${GREEN}✅${NC}"
            break
        fi
        echo -n "."
        sleep 2
    done
    
    echo -e "${GREEN}✅ 多节点服务启动完成${NC}"
}

# 多节点健康检查
multi_node_health_check() {
    echo -e "${YELLOW}🔍 执行多节点健康检查...${NC}"
    
    # 容器状态
    echo -e "\n${CYAN}📦 容器状态:${NC}"
    docker-compose ps
    
    echo -e "\n${CYAN}🏥 服务健康检查:${NC}"
    
    # TTS节点检查
    echo "TTS节点状态:"
    for i in {1..3}; do
        port=$((8001 + i))
        node_name="tts-node-$i"
        
        if curl -f -s http://localhost:$port/health > /dev/null 2>&1; then
            # 获取节点详细信息
            node_info=$(curl -s http://localhost:$port/health | jq -r '.node_id + " (" + .node_role + ") - " + (.workers|tostring) + " workers"' 2>/dev/null || echo "节点$i")
            echo -e "  ${GREEN}✅ $node_name: $node_info${NC}"
        else
            echo -e "  ${RED}❌ $node_name: 不可用${NC}"
        fi
    done
    
    # 负载均衡器检查
    echo -e "\nTTS负载均衡器:"
    if curl -f -s http://localhost:8080/health > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ 负载均衡器: 正常${NC}"
    else
        echo -e "  ${RED}❌ 负载均衡器: 异常${NC}"
    fi
    
    # 其他服务检查
    services=(
        "http://localhost:8000/health:AI网关"
        "http://localhost:8100/health:教育平台"
        "http://localhost:8200/health:医疗平台"
        "http://localhost:9090/-/healthy:Prometheus"
        "http://localhost:3000/api/health:Grafana"
    )
    
    # ASR服务检查
    echo -e "\nASR语音识别服务:"
    if curl -f -s http://localhost:8005/health > /dev/null 2>&1; then
        asr_info=$(curl -s http://localhost:8005/health | jq -r '.node_id + " (" + .node_role + ") - " + (.workers|tostring) + " workers"' 2>/dev/null || echo "ASR服务")
        echo -e "  ${GREEN}✅ $asr_info${NC}"
    else
        echo -e "  ${RED}❌ ASR服务: 不可用${NC}"
    fi

    echo -e "\n其他服务:"
    for service in "${services[@]}"; do
        url=$(echo $service | cut -d: -f1-2)
        name=$(echo $service | cut -d: -f3)

        if curl -f -s $url > /dev/null 2>&1; then
            echo -e "  ${GREEN}✅ $name${NC}"
        else
            echo -e "  ${RED}❌ $name${NC}"
        fi
    done
    
    echo -e "${GREEN}✅ 多节点健康检查完成${NC}"
}

# 运行性能测试
run_multi_node_performance_test() {
    echo -e "${YELLOW}🧪 运行多节点性能测试...${NC}"
    
    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3未安装，跳过性能测试${NC}"
        return
    fi
    
    # 安装依赖
    pip3 install aiohttp asyncio statistics > /dev/null 2>&1 || true
    
    echo -e "${CYAN}测试配置:${NC}"
    echo "  并发用户: 200"
    echo "  测试时长: 60秒"
    echo "  目标: TTS负载均衡器"
    
    # 运行性能测试
    python3 test-env/performance-test.py \
        --url http://localhost:8080 \
        --concurrent 200 \
        --duration 60
}

# 显示多节点访问信息
show_multi_node_access_info() {
    echo -e "${GREEN}🎉 多节点多进程TTS服务部署成功！${NC}"
    echo ""
    echo -e "${CYAN}🌐 服务访问地址:${NC}"
    echo "  🔗 TTS负载均衡器:  http://localhost:8080"
    echo "  🌐 AI服务网关:     http://localhost:8000"
    echo "  🎤 ASR语音识别:    http://localhost:8005"
    echo "  📚 教育平台:       http://localhost:8100"
    echo "  🏥 医疗平台:       http://localhost:8200"
    echo ""
    echo -e "${CYAN}🔧 TTS节点直接访问:${NC}"
    echo "  📍 TTS节点1 (主):   http://localhost:8002"
    echo "  📍 TTS节点2 (次):   http://localhost:8003"
    echo "  📍 TTS节点3 (备):   http://localhost:8004"
    echo ""
    echo -e "${CYAN}📊 监控面板:${NC}"
    echo "  📈 Prometheus:     http://localhost:9090"
    echo "  📊 Grafana:        http://localhost:3000 (admin/admin123)"
    echo ""
    echo -e "${CYAN}📖 API文档:${NC}"
    echo "  🔗 TTS负载均衡API: http://localhost:8080/docs"
    echo "  🔗 ASR服务API:     http://localhost:8005/docs"
    echo "  🔗 AI网关API:      http://localhost:8000/docs"
    echo ""
    echo -e "${CYAN}🧪 测试命令:${NC}"
    echo "  # 通过负载均衡器测试TTS"
    echo "  curl -X POST http://localhost:8080/text_to_speech \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"text\": \"多节点多进程测试\", \"speaker\": \"zh-CN-XiaoxiaoNeural\"}'"
    echo ""
    echo "  # 测试ASR语音识别"
    echo "  curl -X POST http://localhost:8005/recognition \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"audio_url\": \"https://example.com/test.wav\", \"language\": \"zh\"}'"
    echo ""
    echo "  # 查看节点状态"
    echo "  curl http://localhost:8080/nodes/status"
    echo ""
    echo "  # 直接访问特定节点"
    echo "  curl http://localhost:8002/health  # 节点1"
    echo "  curl http://localhost:8003/health  # 节点2"
    echo "  curl http://localhost:8004/health  # 节点3"
    echo ""
    echo -e "${CYAN}🔧 管理命令:${NC}"
    echo "  查看状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f tts-node-1"
    echo "  重启节点: docker-compose restart tts-node-1"
    echo "  停止服务: docker-compose down"
    echo ""
    echo -e "${CYAN}📊 架构总结:${NC}"
    echo "  🏗️ 架构: 3节点 × 多进程 = 超高并发"
    echo "  ⚡ 总进程数: 9个Gunicorn工作进程"
    echo "  🔄 总并发数: 7600+ 连接"
    echo "  🎯 负载均衡: Nginx加权轮询"
    echo "  🛡️ 故障隔离: 节点级别隔离"
    echo ""
    echo -e "${GREEN}✨ 多节点多进程TTS服务已就绪，开始体验超高并发性能吧！${NC}"
}

# 主函数
main() {
    case $1 in
        "deploy")
            check_environment
            configure_multi_node
            create_configs
            deploy_multi_node_services
            wait_for_multi_node_services
            multi_node_health_check
            show_multi_node_access_info
            ;;
        "asr")
            echo -e "${YELLOW}🎤 运行ASR服务测试...${NC}"
            python3 test-env/test-asr-service.py
            ;;
        "test")
            run_multi_node_performance_test
            ;;
        "check")
            multi_node_health_check
            ;;
        "stop")
            docker-compose -f test-env/docker-compose.multi-node.yml down
            echo -e "${GREEN}✅ 多节点服务已停止${NC}"
            ;;
        "clean")
            docker-compose -f test-env/docker-compose.multi-node.yml down -v
            docker system prune -f
            echo -e "${GREEN}✅ 多节点环境已清理${NC}"
            ;;
        *)
            echo "用法: $0 {deploy|asr|test|check|stop|clean}"
            echo ""
            echo "  deploy  - 部署多节点多进程服务"
            echo "  asr     - 测试ASR语音识别服务"
            echo "  test    - 运行性能测试"
            echo "  check   - 健康检查"
            echo "  stop    - 停止服务"
            echo "  clean   - 清理环境"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
