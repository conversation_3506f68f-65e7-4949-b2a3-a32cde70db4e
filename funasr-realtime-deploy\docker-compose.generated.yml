version: '3.8'

# FunASR实时转写集群 - 动态生成配置
# 实例数量: 2个 (HTTP + WebSocket)

services:
  # HTTP服务 - funasr-gpu-0
  funasr-http-funasr-gpu-0:
    image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
    container_name: funasr-http-funasr-gpu-0
    ports:
      - "50300:50300"
    environment:
      - CUDA_VISIBLE_DEVICES=3
      - WORKERS=4
      - MAX_REQUESTS=1000
      - TIMEOUT=300
      - LOG_LEVEL=info
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR HTTP服务 funasr-gpu-0...' &&
        python server.py --host 0.0.0.0 --port 50300
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['3']
              capabilities: [gpu]
        limits:
          memory: 16G
          cpus: '8.0'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50300/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - funasr-network

  # WebSocket服务 - funasr-gpu-0
  funasr-wss-funasr-gpu-0:
    image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
    container_name: funasr-wss-funasr-gpu-0
    ports:
      - "50301:50301"
    environment:
      - CUDA_VISIBLE_DEVICES=3
      - WORKERS=4
      - MAX_CONNECTIONS=50
      - CHUNK_SIZE=WSS_CHUNK_INTERVAL=10        # WebSocket音频块间隔(ms)
WSS_MAX_CONNECTIONS=50       # 每个WebSocket实例最大连接数
MEMORY_LIMIT=16G             # 每个实例的内存限制
CPU_LIMIT=8.0               # 每个实例的CPU限制
LB_ALGORITHM=round_robin     # 负载均衡算法: round_robin, least_conn, hash
LB_WEIGHT=1                 # 每个实例的权重
LB_MAX_FAILS=3              # 最大失败次数
LB_FAIL_TIMEOUT=30s         # 失败超时时间
ENABLE_PROMETHEUS=true       # 是否启用Prometheus监控
ENABLE_GRAFANA=true         # 是否启用Grafana监控
LOG_LEVEL=info              # 日志级别: debug, info, warning, error
LOG_RETENTION_DAYS=7        # 日志保留天数
HEALTH_CHECK_INTERVAL=30s   # 健康检查间隔
HEALTH_CHECK_TIMEOUT=10s    # 健康检查超时
HEALTH_CHECK_RETRIES=3      # 健康检查重试次数
HEALTH_CHECK_START_PERIOD=120s  # 启动等待时间
ENABLE_SSL=false            # 是否启用SSL
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
      - CHUNK_INTERVAL=10
      - LOG_LEVEL=info
    working_dir: /workspace/FunASR/runtime/python/websocket
    command: >
      bash -c "
        echo '🚀 启动FunASR WebSocket服务 funasr-gpu-0...' &&
        python funasr_wss_server.py --host 0.0.0.0 --port 50301
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['3']
              capabilities: [gpu]
        limits:
          memory: 16G
          cpus: '8.0'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50301/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - funasr-network

  # HTTP服务 - funasr-gpu-1
  funasr-http-funasr-gpu-1:
    image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
    container_name: funasr-http-funasr-gpu-1
    ports:
      - "50302:50302"
    environment:
      - CUDA_VISIBLE_DEVICES=7
      - WORKERS=4
      - MAX_REQUESTS=1000
      - TIMEOUT=300
      - LOG_LEVEL=info
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR HTTP服务 funasr-gpu-1...' &&
        python server.py --host 0.0.0.0 --port 50302
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['7']
              capabilities: [gpu]
        limits:
          memory: 16G
          cpus: '8.0'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50302/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - funasr-network

  # WebSocket服务 - funasr-gpu-1
  funasr-wss-funasr-gpu-1:
    image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
    container_name: funasr-wss-funasr-gpu-1
    ports:
      - "50303:50303"
    environment:
      - CUDA_VISIBLE_DEVICES=7
      - WORKERS=4
      - MAX_CONNECTIONS=50
      - CHUNK_SIZE=WSS_CHUNK_INTERVAL=10        # WebSocket音频块间隔(ms)
WSS_MAX_CONNECTIONS=50       # 每个WebSocket实例最大连接数
MEMORY_LIMIT=16G             # 每个实例的内存限制
CPU_LIMIT=8.0               # 每个实例的CPU限制
LB_ALGORITHM=round_robin     # 负载均衡算法: round_robin, least_conn, hash
LB_WEIGHT=1                 # 每个实例的权重
LB_MAX_FAILS=3              # 最大失败次数
LB_FAIL_TIMEOUT=30s         # 失败超时时间
ENABLE_PROMETHEUS=true       # 是否启用Prometheus监控
ENABLE_GRAFANA=true         # 是否启用Grafana监控
LOG_LEVEL=info              # 日志级别: debug, info, warning, error
LOG_RETENTION_DAYS=7        # 日志保留天数
HEALTH_CHECK_INTERVAL=30s   # 健康检查间隔
HEALTH_CHECK_TIMEOUT=10s    # 健康检查超时
HEALTH_CHECK_RETRIES=3      # 健康检查重试次数
HEALTH_CHECK_START_PERIOD=120s  # 启动等待时间
ENABLE_SSL=false            # 是否启用SSL
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
      - CHUNK_INTERVAL=10
      - LOG_LEVEL=info
    working_dir: /workspace/FunASR/runtime/python/websocket
    command: >
      bash -c "
        echo '🚀 启动FunASR WebSocket服务 funasr-gpu-1...' &&
        python funasr_wss_server.py --host 0.0.0.0 --port 50303
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['7']
              capabilities: [gpu]
        limits:
          memory: 16G
          cpus: '8.0'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50303/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - funasr-network

  # Nginx负载均衡器 (支持HTTP + WebSocket)
  funasr-loadbalancer:
    image: nginx:alpine
    container_name: funasr-realtime-lb
    ports:
      - "8080:80"
    volumes:
      - ./nginx.generated.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - funasr-http-funasr-gpu-0
      - funasr-http-funasr-gpu-1
      - funasr-wss-funasr-gpu-0
      - funasr-wss-funasr-gpu-1
    restart: unless-stopped
    networks:
      - funasr-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: funasr-realtime-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.generated.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
    networks:
      - funasr-network
    profiles:
      - monitoring

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: funasr-realtime-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - funasr-network
    profiles:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  funasr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
