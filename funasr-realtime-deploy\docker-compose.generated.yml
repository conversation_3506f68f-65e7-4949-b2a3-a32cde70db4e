version: '3.8'

# FunASR实时转写集群 - 动态生成配置
# 实例数量: 1个 (HTTP + WebSocket)

services:
  # HTTP服务 - funasr-gpu-1
  funasr-http-funasr-gpu-1:
    image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
    container_name: funasr-http-funasr-gpu-1
    ports:
      - "50302:50302"
    environment:
      - CUDA_VISIBLE_DEVICES=7
      - WORKERS=4
      - MAX_REQUESTS=1000
      - TIMEOUT=300
      - LOG_LEVEL=info
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR HTTP服务 funasr-gpu-1...' &&
        python server.py --host 0.0.0.0 --port 50302
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['7']
              capabilities: [gpu]
        limits:
          memory: 24G
          cpus: '8.0'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50302/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - funasr-network

  # WebSocket服务 - funasr-gpu-1
  # funasr-wss-funasr-gpu-1:
  #   image: registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1
  #   container_name: funasr-wss-funasr-gpu-1
  #   ports:
  #     - "50303:50303"
  #   environment:
  #     - CUDA_VISIBLE_DEVICES=7
  #     - WORKERS=4
  #     - MAX_CONNECTIONS=50
  #     - CHUNK_SIZE=[5, 10, 5]
  #     - CHUNK_INTERVAL=10
  #     - LOG_LEVEL=info
  #   working_dir: /workspace/FunASR/runtime/python/websocket
  #   command: >
  #     bash -c "
  #       echo '🚀 启动FunASR WebSocket服务 funasr-gpu-1...' &&
  #       python funasr_wss_server.py --host 0.0.0.0 --port 50303
  #     "
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             device_ids: ['7']
  #             capabilities: [gpu]
  #       limits:
  #         memory: 24G
  #         cpus: '8.0'
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:50303/"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 120s
  #   networks:
  #     - funasr-network

  # Nginx负载均衡器 (支持HTTP + WebSocket)
#   funasr-loadbalancer:
#     image: nginx:alpine
#     container_name: funasr-realtime-lb
#     ports:
#       - "8080:80"
#     volumes:
#       - ./nginx.generated.conf:/etc/nginx/nginx.conf:ro
#     depends_on:
#       - funasr-http-funasr-gpu-1
#       - funasr-wss-funasr-gpu-1
#     restart: unless-stopped
#     networks:
#       - funasr-network

#   # Prometheus监控
#   prometheus:
#     image: prom/prometheus:latest
#     container_name: funasr-realtime-prometheus
#     ports:
#       - "9090:9090"
#     volumes:
#       - ./prometheus.generated.yml:/etc/prometheus/prometheus.yml:ro
#       - prometheus_data:/prometheus
#     command:
#       - '--config.file=/etc/prometheus/prometheus.yml'
#       - '--storage.tsdb.path=/prometheus'
#       - '--web.console.libraries=/etc/prometheus/console_libraries'
#       - '--web.console.templates=/etc/prometheus/consoles'
#       - '--storage.tsdb.retention.time=7d'
#     networks:
#       - funasr-network
#     profiles:
#       - monitoring

#   # Grafana监控面板
#   grafana:
#     image: grafana/grafana:latest
#     container_name: funasr-realtime-grafana
#     ports:
#       - "3000:3000"
#     environment:
#       - GF_SECURITY_ADMIN_PASSWORD=admin123
#     volumes:
#       - grafana_data:/var/lib/grafana
#     networks:
#       - funasr-network
#     profiles:
#       - monitoring

# volumes:
#   prometheus_data:
#   grafana_data:

networks:
  funasr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
