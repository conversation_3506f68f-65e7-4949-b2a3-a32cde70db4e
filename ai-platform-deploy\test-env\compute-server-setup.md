# 算力服务器配置指南

## 🖥️ 算力服务器配置

### 算力服务器1 (*************)
**主要AI推理服务**

#### 端口分配
- **8080**: TTS模型服务
- **8081**: LLM模型服务  
- **8082**: OCR模型服务
- **9100**: Node Exporter (监控)

#### 服务配置
```bash
# 1. TTS服务 (端口8080)
docker run -d --name tts-model \
  --gpus all \
  -p 8080:8080 \
  -e MODEL_PATH=/models/tts \
  -e API_KEY=your-api-key \
  -v /data/models:/models \
  your-tts-model:latest

# 2. LLM服务 (端口8081)
docker run -d --name llm-model \
  --gpus all \
  -p 8081:8081 \
  -e MODEL_NAME=qwen-7b \
  -e MAX_TOKENS=4096 \
  -v /data/models:/models \
  your-llm-model:latest

# 3. OCR服务 (端口8082)
docker run -d --name ocr-model \
  --gpus all \
  -p 8082:8082 \
  -e MODEL_PATH=/models/ocr \
  -v /data/models:/models \
  your-ocr-model:latest

# 4. 监控服务 (端口9100)
docker run -d --name node-exporter \
  -p 9100:9100 \
  -v /proc:/host/proc:ro \
  -v /sys:/host/sys:ro \
  -v /:/rootfs:ro \
  prom/node-exporter:latest \
  --path.procfs=/host/proc \
  --path.rootfs=/rootfs \
  --path.sysfs=/host/sys \
  --collector.filesystem.mount-points-exclude='^/(sys|proc|dev|host|etc)($$|/)'
```

### 算力服务器2 (*************)
**备用AI推理服务**

#### 端口分配
- **8080**: TTS备用服务
- **8081**: LLM备用服务
- **8082**: ASR模型服务
- **9100**: Node Exporter (监控)

#### 服务配置
```bash
# 1. TTS备用服务 (端口8080)
docker run -d --name tts-model-backup \
  --gpus all \
  -p 8080:8080 \
  -e MODEL_PATH=/models/tts \
  -e API_KEY=your-api-key \
  -v /data/models:/models \
  your-tts-model:latest

# 2. LLM备用服务 (端口8081)
docker run -d --name llm-model-backup \
  --gpus all \
  -p 8081:8081 \
  -e MODEL_NAME=qwen-7b \
  -e MAX_TOKENS=4096 \
  -v /data/models:/models \
  your-llm-model:latest

# 3. ASR服务 (端口8082)
docker run -d --name asr-model \
  --gpus all \
  -p 8082:8082 \
  -e MODEL_PATH=/models/asr \
  -v /data/models:/models \
  your-asr-model:latest

# 4. 监控服务 (端口9100)
docker run -d --name node-exporter \
  -p 9100:9100 \
  -v /proc:/host/proc:ro \
  -v /sys:/host/sys:ro \
  -v /:/rootfs:ro \
  prom/node-exporter:latest \
  --path.procfs=/host/proc \
  --path.rootfs=/rootfs \
  --path.sysfs=/host/sys \
  --collector.filesystem.mount-points-exclude='^/(sys|proc|dev|host|etc)($$|/)'
```

## 🔧 快速部署脚本

### 算力服务器1部署脚本
```bash
#!/bin/bash
# compute-server-1-setup.sh

echo "🚀 配置算力服务器1..."

# 拉取镜像
docker pull your-tts-model:latest
docker pull your-llm-model:latest
docker pull your-ocr-model:latest
docker pull prom/node-exporter:latest

# 创建数据目录
mkdir -p /data/models/{tts,llm,ocr}

# 启动TTS服务
docker run -d --name tts-model \
  --restart unless-stopped \
  --gpus all \
  -p 8080:8080 \
  -e MODEL_PATH=/models/tts \
  -v /data/models:/models \
  your-tts-model:latest

# 启动LLM服务
docker run -d --name llm-model \
  --restart unless-stopped \
  --gpus all \
  -p 8081:8081 \
  -e MODEL_NAME=qwen-7b \
  -v /data/models:/models \
  your-llm-model:latest

# 启动OCR服务
docker run -d --name ocr-model \
  --restart unless-stopped \
  --gpus all \
  -p 8082:8082 \
  -e MODEL_PATH=/models/ocr \
  -v /data/models:/models \
  your-ocr-model:latest

# 启动监控
docker run -d --name node-exporter \
  --restart unless-stopped \
  -p 9100:9100 \
  -v /proc:/host/proc:ro \
  -v /sys:/host/sys:ro \
  -v /:/rootfs:ro \
  prom/node-exporter:latest \
  --path.procfs=/host/proc \
  --path.rootfs=/rootfs \
  --path.sysfs=/host/sys

echo "✅ 算力服务器1配置完成"
echo "服务地址："
echo "  TTS: http://$(hostname -I | awk '{print $1}'):8080"
echo "  LLM: http://$(hostname -I | awk '{print $1}'):8081"
echo "  OCR: http://$(hostname -I | awk '{print $1}'):8082"
```

### 算力服务器2部署脚本
```bash
#!/bin/bash
# compute-server-2-setup.sh

echo "🚀 配置算力服务器2..."

# 拉取镜像
docker pull your-tts-model:latest
docker pull your-llm-model:latest
docker pull your-asr-model:latest
docker pull prom/node-exporter:latest

# 创建数据目录
mkdir -p /data/models/{tts,llm,asr}

# 启动TTS备用服务
docker run -d --name tts-model-backup \
  --restart unless-stopped \
  --gpus all \
  -p 8080:8080 \
  -e MODEL_PATH=/models/tts \
  -v /data/models:/models \
  your-tts-model:latest

# 启动LLM备用服务
docker run -d --name llm-model-backup \
  --restart unless-stopped \
  --gpus all \
  -p 8081:8081 \
  -e MODEL_NAME=qwen-7b \
  -v /data/models:/models \
  your-llm-model:latest

# 启动ASR服务
docker run -d --name asr-model \
  --restart unless-stopped \
  --gpus all \
  -p 8082:8082 \
  -e MODEL_PATH=/models/asr \
  -v /data/models:/models \
  your-asr-model:latest

# 启动监控
docker run -d --name node-exporter \
  --restart unless-stopped \
  -p 9100:9100 \
  -v /proc:/host/proc:ro \
  -v /sys:/host/sys:ro \
  -v /:/rootfs:ro \
  prom/node-exporter:latest \
  --path.procfs=/host/proc \
  --path.rootfs=/rootfs \
  --path.sysfs=/host/sys

echo "✅ 算力服务器2配置完成"
echo "服务地址："
echo "  TTS备用: http://$(hostname -I | awk '{print $1}'):8080"
echo "  LLM备用: http://$(hostname -I | awk '{print $1}'):8081"
echo "  ASR: http://$(hostname -I | awk '{print $1}'):8082"
```

## 🧪 测试验证

### 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

echo "🔍 算力服务器健康检查..."

servers=("*************" "*************")
ports=("8080" "8081" "8082")

for server in "${servers[@]}"; do
    echo "检查服务器: $server"
    for port in "${ports[@]}"; do
        if curl -f -s http://$server:$port/health > /dev/null; then
            echo "  ✅ 端口 $port: 正常"
        else
            echo "  ❌ 端口 $port: 异常"
        fi
    done
    echo ""
done
```

## 📊 性能监控

### GPU使用率监控
```bash
# 安装nvidia-ml-py
pip install nvidia-ml-py3

# GPU监控脚本
python3 << 'EOF'
import pynvml
import time

pynvml.nvmlInit()
device_count = pynvml.nvmlDeviceGetCount()

while True:
    for i in range(device_count):
        handle = pynvml.nvmlDeviceGetHandleByIndex(i)
        info = pynvml.nvmlDeviceGetMemoryInfo(handle)
        util = pynvml.nvmlDeviceGetUtilizationRates(handle)
        
        print(f"GPU {i}: 使用率 {util.gpu}%, 内存 {info.used/1024**3:.1f}GB/{info.total/1024**3:.1f}GB")
    
    time.sleep(5)
    print("-" * 50)
EOF
```

## 🔧 故障排除

### 常见问题
1. **GPU不可用**: 检查NVIDIA驱动和Docker GPU支持
2. **端口冲突**: 确保端口未被占用
3. **内存不足**: 调整模型参数或增加内存
4. **网络连通性**: 检查防火墙和网络配置

### 日志查看
```bash
# 查看容器日志
docker logs tts-model
docker logs llm-model
docker logs ocr-model

# 查看GPU使用情况
nvidia-smi

# 查看系统资源
htop
```
