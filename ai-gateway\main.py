"""
AI服务网关 - 独立版本
统一的AI服务入口，支持负载均衡和多租户
"""

import asyncio
import time
import uuid
import random
from typing import Dict, Any, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx
import aioredis


# 请求响应模型
class AIRequest(BaseModel):
    """AI服务请求"""
    image_url: Optional[str] = None
    image_data: Optional[str] = None  # base64编码
    text: Optional[str] = None
    audio_url: Optional[str] = None
    audio_data: Optional[str] = None  # base64编码
    messages: Optional[List[Dict[str, str]]] = None
    voice: str = "zh-CN-XiaoxiaoNeural"
    model: str = "auto"
    options: Dict[str, Any] = Field(default_factory=dict)


class AIResponse(BaseModel):
    """AI服务响应"""
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any]


# 创建应用
app = FastAPI(
    title="AI Gateway",
    description="AI服务网关 - 统一AI服务接入点",
    version="1.0.0"
)

# 添加CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
redis_client = None
http_client = httpx.AsyncClient(timeout=30)

# 后端服务配置 - 请根据实际情况修改
BACKEND_SERVICES = {
    "ocr": [
        "http://***************:20060",
        "http://**************:10099"
    ],
    "tts": [
        "http://nginx-tts-lb:80",       # TTS负载均衡器 (推荐)
        "http://tts-node-1:8002",       # TTS节点1 (直接访问)
        "http://tts-node-2:8002",       # TTS节点2 (直接访问)
        "http://tts-node-3:8002"        # TTS节点3 (直接访问)
    ],
    "asr": [
        "http://asr-service:8003",      # ASR微服务
        "http://**************:10099"   # 备用ASR后端
    ],
    "llm": [
        "http://***************:20060",
        "http://**************:10099"
    ]
}


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    global redis_client
    try:
        redis_client = aioredis.from_url("redis://redis:6379")
        await redis_client.ping()
        print("✅ Redis连接成功")
    except:
        print("⚠️ Redis连接失败，缓存功能不可用")
        redis_client = None
    
    print("🚀 AI网关启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    await http_client.aclose()
    if redis_client:
        await redis_client.close()
    print("✅ AI网关关闭完成")


def select_backend(service_type: str) -> str:
    """选择后端服务"""
    backends = BACKEND_SERVICES.get(service_type, [])
    if not backends:
        raise HTTPException(status_code=503, detail=f"No {service_type} service available")
    
    # 简单轮询选择
    return random.choice(backends)


async def get_cache(key: str) -> Optional[Dict]:
    """获取缓存"""
    if not redis_client:
        return None
    
    try:
        data = await redis_client.get(key)
        if data:
            import json
            return json.loads(data)
    except:
        pass
    return None


async def set_cache(key: str, data: Dict, ttl: int = 3600):
    """设置缓存"""
    if not redis_client:
        return
    
    try:
        import json
        await redis_client.setex(key, ttl, json.dumps(data))
    except:
        pass


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "ai-gateway",
        "version": "1.0.0",
        "timestamp": time.time(),
        "redis_connected": redis_client is not None
    }


@app.post("/api/v1/ocr", response_model=AIResponse)
async def ocr_service(
    request: AIRequest,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """OCR服务"""
    try:
        cache_key = f"ocr:{hash(str(request.dict()))}"
        
        # 检查缓存
        cached = await get_cache(cache_key)
        if cached:
            return AIResponse(
                success=True,
                data=cached,
                metadata={"cache_hit": True, "tenant_id": x_tenant_id}
            )
        
        # 选择后端
        backend_url = select_backend("ocr")
        
        # 构建请求
        payload = {
            "image_url": request.image_url,
            "image_data": request.image_data,
            "options": request.options
        }
        
        # 调用后端
        response = await http_client.post(f"{backend_url}/ocr", json=payload)
        response.raise_for_status()
        
        result = response.json()
        
        # 缓存结果
        await set_cache(cache_key, result)
        
        return AIResponse(
            success=True,
            data=result,
            metadata={"cache_hit": False, "backend": backend_url, "tenant_id": x_tenant_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/tts", response_model=AIResponse)
async def tts_service(
    request: AIRequest,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """TTS服务"""
    try:
        cache_key = f"tts:{hash(str(request.dict()))}"
        
        # 检查缓存
        cached = await get_cache(cache_key)
        if cached:
            return AIResponse(
                success=True,
                data=cached,
                metadata={"cache_hit": True, "tenant_id": x_tenant_id}
            )
        
        # 选择后端
        backend_url = select_backend("tts")
        
        # 构建请求
        payload = {
            "text": request.text,
            "speaker": request.voice,
            "options": request.options
        }
        
        # 调用TTS微服务
        response = await http_client.post(f"{backend_url}/process", json=payload)
        response.raise_for_status()
        
        result = response.json()
        
        # 缓存结果
        await set_cache(cache_key, result.get("data", result), ttl=7200)
        
        return AIResponse(
            success=True,
            data=result.get("data", result),
            metadata={"cache_hit": False, "backend": backend_url, "tenant_id": x_tenant_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/asr", response_model=AIResponse)
async def asr_service(
    request: AIRequest,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """ASR服务"""
    try:
        backend_url = select_backend("asr")
        
        payload = {
            "audio_url": request.audio_url,
            "audio_data": request.audio_data,
            "options": request.options
        }
        
        response = await http_client.post(f"{backend_url}/asr", json=payload)
        response.raise_for_status()
        
        result = response.json()
        
        return AIResponse(
            success=True,
            data=result,
            metadata={"backend": backend_url, "tenant_id": x_tenant_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/llm", response_model=AIResponse)
async def llm_service(
    request: AIRequest,
    x_tenant_id: Optional[str] = Header(None, alias="X-Tenant-ID")
):
    """LLM服务"""
    try:
        backend_url = select_backend("llm")
        
        payload = {
            "messages": request.messages,
            "model": request.model,
            "options": request.options
        }
        
        response = await http_client.post(f"{backend_url}/llm", json=payload)
        response.raise_for_status()
        
        result = response.json()
        
        return AIResponse(
            success=True,
            data=result,
            metadata={"backend": backend_url, "tenant_id": x_tenant_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/services")
async def get_services():
    """获取可用服务"""
    return {
        "services": {
            "ocr": {"description": "图像文字识别", "backends": len(BACKEND_SERVICES["ocr"])},
            "tts": {"description": "文本转语音", "backends": len(BACKEND_SERVICES["tts"])},
            "asr": {"description": "语音识别", "backends": len(BACKEND_SERVICES["asr"])},
            "llm": {"description": "大语言模型", "backends": len(BACKEND_SERVICES["llm"])}
        }
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
