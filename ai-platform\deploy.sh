#!/bin/bash

# AI服务平台一键部署脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 AI服务平台部署脚本${NC}"
echo "================================"

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker环境检查通过${NC}"

# 创建.env文件
echo -e "${YELLOW}📝 创建配置文件...${NC}"
cat > .env << 'EOF'
# 后端AI服务器地址 (请修改为您的实际地址)
OCR_BACKENDS=http://***************:20060,http://**************:10099
TTS_BACKENDS=http://***************:20060
ASR_BACKENDS=http://**************:10099
LLM_BACKENDS=http://***************:20060,http://**************:10099

# Redis配置
REDIS_URL=redis://redis:6379
EOF

echo -e "${GREEN}✅ 配置文件创建完成${NC}"

# 构建和启动服务
echo -e "${YELLOW}🔨 构建Docker镜像...${NC}"
docker-compose build

echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose up -d

echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"

services=(
    "http://localhost:8000/health:AI网关"
    "http://localhost:8100/health:教育平台"
    "http://localhost:8200/health:医疗平台"
)

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-2)
    name=$(echo $service | cut -d: -f3)
    
    if curl -f -s $url > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $name 健康检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  $name 健康检查失败，可能还在启动中${NC}"
    fi
done

# 显示访问信息
echo ""
echo -e "${GREEN}🎉 AI服务平台部署完成！${NC}"
echo ""
echo "📋 服务地址："
echo "  🌐 AI服务网关:     http://localhost:8000"
echo "  📚 教育平台:       http://localhost:8100"
echo "  🏥 医疗平台:       http://localhost:8200"
echo "  🔧 TTS微服务:      http://localhost:8002"
echo ""
echo "📖 API文档："
echo "  🔗 AI网关API:      http://localhost:8000/docs"
echo "  🔗 教育平台API:    http://localhost:8100/docs"
echo "  🔗 医疗平台API:    http://localhost:8200/docs"
echo "  🔗 TTS服务API:     http://localhost:8002/docs"
echo ""
echo "🧪 快速测试："
echo "  curl http://localhost:8000/health"
echo "  curl http://localhost:8000/api/v1/services"
echo "  curl -X POST http://localhost:8000/api/v1/tts \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -d '{\"text\": \"Hello World\", \"voice\": \"zh-CN-XiaoxiaoNeural\"}'"
echo ""
echo "🔧 管理命令："
echo "  查看状态: docker-compose ps"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo ""
echo -e "${GREEN}✨ AI服务平台已就绪，可以通过API调用各种AI服务！${NC}"
