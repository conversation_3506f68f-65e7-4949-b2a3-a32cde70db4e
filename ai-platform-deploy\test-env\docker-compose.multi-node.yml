version: '3.8'

# 多节点多进程TTS服务部署配置
# 每个节点运行独立的TTS服务，每个服务内部使用Gunicorn多进程

services:
  # AI服务网关 (普通服务器)
  gateway:
    image: yourorg/ai-gateway:latest
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DEBUG=true
      - LOG_LEVEL=DEBUG
      # 配置多个TTS节点
      - TTS_BACKENDS=http://tts-node-1:8002,http://tts-node-2:8002,http://tts-node-3:8002
      # 配置ASR服务
      - ASR_BACKENDS=http://asr-service:8003,http://*************:8082
      - LLM_BACKENDS=http://*************:8081,http://*************:8081
      - OCR_BACKENDS=http://*************:8082
    depends_on:
      - redis
      - tts-node-1
      - tts-node-2
      - tts-node-3
      - asr-service
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G

  # TTS节点1 - 高性能配置
  tts-node-1:
    image: yourorg/ai-tts-service:latest
    container_name: tts-node-1
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - TTS_BACKENDS=http://*************:8080,http://*************:8080
      - ENVIRONMENT=production
      - NODE_ID=tts-node-1
      - NODE_ROLE=primary
      # Gunicorn多进程配置
      - WORKERS=4
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=1000
      - MAX_REQUESTS=1000
      - MAX_REQUESTS_JITTER=100
      - TIMEOUT=300
      - KEEPALIVE=5
      - LOG_LEVEL=info
      - PRELOAD=true
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    volumes:
      - tts_node_1_logs:/app/logs
    deploy:
      resources:
        limits:
          cpus: '3.0'
          memory: 6G
        reservations:
          cpus: '2.0'
          memory: 4G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # TTS节点2 - 标准配置
  tts-node-2:
    image: yourorg/ai-tts-service:latest
    container_name: tts-node-2
    ports:
      - "8003:8002"  # 映射到不同的外部端口
    environment:
      - REDIS_URL=redis://redis:6379
      - TTS_BACKENDS=http://*************:8080,http://*************:8080
      - ENVIRONMENT=production
      - NODE_ID=tts-node-2
      - NODE_ROLE=secondary
      # Gunicorn多进程配置
      - WORKERS=3
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=800
      - MAX_REQUESTS=800
      - MAX_REQUESTS_JITTER=80
      - TIMEOUT=300
      - KEEPALIVE=5
      - LOG_LEVEL=info
      - PRELOAD=true
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    volumes:
      - tts_node_2_logs:/app/logs
    deploy:
      resources:
        limits:
          cpus: '2.5'
          memory: 5G
        reservations:
          cpus: '1.5'
          memory: 3G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # TTS节点3 - 轻量配置
  tts-node-3:
    image: yourorg/ai-tts-service:latest
    container_name: tts-node-3
    ports:
      - "8004:8002"  # 映射到不同的外部端口
    environment:
      - REDIS_URL=redis://redis:6379
      - TTS_BACKENDS=http://*************:8080,http://*************:8080
      - ENVIRONMENT=production
      - NODE_ID=tts-node-3
      - NODE_ROLE=backup
      # Gunicorn多进程配置
      - WORKERS=2
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=600
      - MAX_REQUESTS=600
      - MAX_REQUESTS_JITTER=60
      - TIMEOUT=300
      - KEEPALIVE=5
      - LOG_LEVEL=info
      - PRELOAD=true
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    volumes:
      - tts_node_3_logs:/app/logs
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ASR语音识别服务 (普通服务器)
  asr-service:
    image: yourorg/ai-asr-service:latest
    container_name: asr-service
    ports:
      - "8005:8003"  # 映射到外部8005端口
    environment:
      - REDIS_URL=redis://redis:6379
      - ASR_BACKENDS=http://*************:8082
      - ENVIRONMENT=production
      - NODE_ID=asr-service
      - NODE_ROLE=primary
      # Gunicorn多进程配置
      - WORKERS=3
      - WORKER_CLASS=uvicorn.workers.UvicornWorker
      - WORKER_CONNECTIONS=800
      - MAX_REQUESTS=800
      - MAX_REQUESTS_JITTER=80
      - TIMEOUT=600  # ASR需要更长超时时间
      - KEEPALIVE=5
      - LOG_LEVEL=info
      - PRELOAD=true
    depends_on:
      - redis
    networks:
      - ai-platform
    restart: unless-stopped
    volumes:
      - asr_logs:/app/logs
    deploy:
      resources:
        limits:
          cpus: '2.5'
          memory: 5G
        reservations:
          cpus: '1.5'
          memory: 3G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s

  # 教育平台 (普通服务器)
  education-app:
    image: yourorg/ai-education-app:latest
    ports:
      - "8100:8100"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
      - DEBUG=true
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # 医疗平台 (普通服务器)
  medical-app:
    image: yourorg/ai-medical-app:latest
    ports:
      - "8200:8200"
    environment:
      - AI_GATEWAY_URL=http://gateway:8000
      - DEBUG=true
    depends_on:
      - gateway
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Redis缓存 (普通服务器)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Nginx负载均衡器 (直接访问TTS节点)
  nginx-tts-lb:
    image: nginx:alpine
    ports:
      - "8080:80"  # TTS负载均衡入口
    volumes:
      - ./nginx/tts-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - tts-node-1
      - tts-node-2
      - tts-node-3
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # Prometheus监控 (普通服务器)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-multi-node.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=7d'
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Grafana监控面板 (普通服务器)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-platform
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
  tts_node_1_logs:
  tts_node_2_logs:
  tts_node_3_logs:
  asr_logs:

networks:
  ai-platform:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
