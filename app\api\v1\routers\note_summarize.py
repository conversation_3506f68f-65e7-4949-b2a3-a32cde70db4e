from fastapi import APIRouter
from app.services.note_summarize_service import NoteSummarizeService
from app.models.note_summarize_model import NoteSummarizeRequest, NoteSummarizeResponse

router = APIRouter()
service = NoteSummarizeService()


@router.post("/llm/matrix/note/summarize", response_model=NoteSummarizeResponse)
async def note_summarize(request: NoteSummarizeRequest):
    return await service.note_summarize(request.note_list, request.llm_params)
