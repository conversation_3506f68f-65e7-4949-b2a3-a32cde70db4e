from pydantic import BaseModel, Field
from typing import List, Literal, Optional

class NoteSummarizeRequest(BaseModel):
    """
    笔记整理请求模型
    - note_list: 笔记文本列表 (必填)
    """
    note_list: List[str] = Field(..., min_length=1, example=["这是一条笔记文本"])
    llm_params: dict = Field(
        {},
        example={
            "model": "Qwen2-7B-Instruct",
            "stream": False,
            "max_tokens": 1000,
            "temperature": 0.0,
            "top_p": 1.0,
        },
        description="llm模型参数"
    )


# 评分响应模型
class NoteSummarizeResponse(BaseModel):
    """
    笔记整理响应模型
    - note_markdown: 整理好的笔记文本的markdown格式
    """
    summary_markdown: str = Field(..., example="这是整理好的笔记文本")


