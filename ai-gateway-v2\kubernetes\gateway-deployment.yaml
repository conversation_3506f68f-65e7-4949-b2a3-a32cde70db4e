apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gateway
  namespace: ai-gateway
  labels:
    app: ai-gateway
    version: v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-gateway
  template:
    metadata:
      labels:
        app: ai-gateway
        version: v2
    spec:
      containers:
      - name: gateway
        image: ai-gateway:v2.0.0
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: REDIS_URL
          value: "redis://redis-cluster:6379"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: CONSUL_HOST
          value: "consul"
        - name: PROMETHEUS_ENABLED
          value: "true"
        - name: JAEGER_ENABLED
          value: "true"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: gateway-config

---
apiVersion: v1
kind: Service
metadata:
  name: ai-gateway-service
  namespace: ai-gateway
  labels:
    app: ai-gateway
spec:
  selector:
    app: ai-gateway
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-config
  namespace: ai-gateway
data:
  settings.yaml: |
    environment: production
    debug: false
    log_level: INFO
    workers: 4
    max_concurrent_requests: 100000
    rate_limit_requests: 10000
    cache_enabled: true
    auth_enabled: true
    prometheus_enabled: true
    jaeger_enabled: true

---
apiVersion: v1
kind: Secret
metadata:
  name: database-secret
  namespace: ai-gateway
type: Opaque
data:
  url: ****************************************************************************

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ai-gateway-hpa
  namespace: ai-gateway
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-gateway
  minReplicas: 3
  maxReplicas: 50
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-gateway-ingress
  namespace: ai-gateway
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "1000"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.ai-gateway.com
    secretName: ai-gateway-tls
  rules:
  - host: api.ai-gateway.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-gateway-service
            port:
              number: 80
