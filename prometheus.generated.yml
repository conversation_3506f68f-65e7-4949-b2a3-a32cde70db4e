global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # FunASR HTTP实例监控
  - job_name: 'funasr-http-instances'
    static_configs:
      - targets: 
          - 'funasr-http-funasr-gpu-0:10095'
          - 'funasr-http-funasr-gpu-1:10095'
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # FunASR WebSocket实例监控
  - job_name: 'funasr-wss-instances'
    static_configs:
      - targets: 
          - 'funasr-wss-funasr-gpu-0:10095'
          - 'funasr-wss-funasr-gpu-1:10095'
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Nginx负载均衡器监控
  - job_name: 'funasr-loadbalancer'
    static_configs:
      - targets: ['funasr-loadbalancer:80']
    metrics_path: '/nginx_status'
    scrape_interval: 15s

  # 系统监控 (如果有node_exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s
