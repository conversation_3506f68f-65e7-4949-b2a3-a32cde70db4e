# 测试环境服务器分配方案

## 🖥️ 服务器配置 (3台)

### 普通服务器 (1台)
**角色**: 网关 + 微服务 + 基础设施
- **AI网关**: 统一入口 (8000端口)
- **TTS微服务**: 业务逻辑处理 (8002端口)
- **教育平台**: 业务应用 (8100端口)
- **医疗平台**: 业务应用 (8200端口)
- **Redis**: 缓存服务 (6379端口)
- **监控**: Prometheus + Grafana

### 算力服务器1 (GPU/高性能)
**角色**: 主要AI推理服务
- **TTS后端**: 语音合成模型
- **LLM后端**: 大语言模型
- **OCR后端**: 图像识别模型

### 算力服务器2 (GPU/高性能)
**角色**: 备用AI推理服务
- **TTS后端**: 备用语音合成
- **LLM后端**: 备用大语言模型
- **ASR后端**: 语音识别模型

## 🏗️ 架构图

```
┌─────────────────────────────────────┐
│           普通服务器                 │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │AI网关   │ │教育平台 │ │医疗平台 │ │
│  │ :8000   │ │ :8100   │ │ :8200   │ │
│  └─────────┘ └─────────┘ └─────────┘ │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │TTS微服务│ │ Redis   │ │监控系统 │ │
│  │ :8002   │ │ :6379   │ │ :3000   │ │
│  └─────────┘ └─────────┘ └─────────┘ │
└─────────────┬───────────────────────┘
              │
    ┌─────────┴─────────┐
    │                   │
┌───▼────┐         ┌───▼────┐
│算力服务器1│         │算力服务器2│
│TTS模型  │         │TTS备用  │
│LLM模型  │         │LLM备用  │
│OCR模型  │         │ASR模型  │
└────────┘         └────────┘
```

## 📊 性能预期

### 并发能力
- **总QPS**: 1万+ (测试环境)
- **单服务QPS**: 2000-5000
- **响应时间**: P99 < 500ms

### 可用性
- **服务可用性**: 99.9%
- **故障恢复**: < 2分钟
- **负载均衡**: 算力服务器间自动切换

## 🔧 部署特点

### 资源优化
- **单机多服务**: 最大化资源利用
- **智能调度**: 根据负载分配请求
- **缓存优化**: Redis缓存减少算力压力

### 扩展性
- **水平扩展**: 可随时添加更多算力服务器
- **垂直扩展**: 可升级单台服务器配置
- **模块化**: 每个服务独立，便于调试

## 🧪 测试场景

### 功能测试
- **OCR识别**: 图片文字识别
- **TTS合成**: 文本转语音
- **LLM对话**: AI智能对话
- **业务流程**: 教育/医疗平台完整流程

### 性能测试
- **并发测试**: 模拟1000并发用户
- **压力测试**: 持续高负载测试
- **故障测试**: 单台算力服务器故障恢复

### 扩展测试
- **动态扩容**: 添加新的算力服务器
- **负载均衡**: 验证请求分配策略
- **缓存效果**: 验证Redis缓存命中率
