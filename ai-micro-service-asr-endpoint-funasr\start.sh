#!/bin/bash

# FunASR优化启动脚本

set -e

echo "🚀 启动FunASR高性能服务器..."

# 安装额外依赖（如果需要）
if [ -f "/workspace/requirements.txt" ]; then
    echo "📦 安装额外依赖..."
    pip install -r /workspace/requirements.txt --no-cache-dir --quiet
fi

# 环境变量配置
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-"0"}
export OMP_NUM_THREADS=${OMP_NUM_THREADS:-"4"}
export MKL_NUM_THREADS=${MKL_NUM_THREADS:-"4"}

# 服务配置
PORT=${PORT:-10312}
HOST=${HOST:-"0.0.0.0"}
MODEL_POOL_SIZE=${MODEL_POOL_SIZE:-2}
MAX_MEMORY_GB=${MAX_MEMORY_GB:-8.0}
MAX_FILE_SIZE=${MAX_FILE_SIZE:-52428800}  # 50MB

# GPU优化
if command -v nvidia-smi &> /dev/null; then
    echo "🔧 GPU信息:"
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits
    
    # 设置GPU内存增长
    export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
fi

# 内存优化
echo "💾 系统内存信息:"
free -h

# 启动服务
echo "✅ 启动参数:"
echo "  端口: $PORT"
echo "  主机: $HOST"
echo "  模型池大小: $MODEL_POOL_SIZE"
echo "  最大内存: ${MAX_MEMORY_GB}GB"
echo "  最大文件大小: $MAX_FILE_SIZE bytes"

# 进入FunASR HTTP服务目录
cd /workspace/FunASR/runtime/python/http

# 启动优化的server.py (已通过volume挂载)
exec python server.py \
    --host $HOST \
    --port $PORT \
    --model_pool_size $MODEL_POOL_SIZE \
    --max_memory_gb $MAX_MEMORY_GB \
    --max_file_size $MAX_FILE_SIZE
