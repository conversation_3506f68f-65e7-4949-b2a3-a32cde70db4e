"""
教育平台 - 独立版本
基于AI服务平台的教育应用
"""

import os
import sys
import time
from typing import Dict, Any, List

import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import httpx


# 请求模型
class GenerateQuestionRequest(BaseModel):
    """出题请求"""
    topic: str = Field(..., description="知识点")
    subject: str = Field(..., description="学科")
    difficulty: str = Field(default="medium", description="难度")
    count: int = Field(default=5, description="题目数量")


# 创建应用
app = FastAPI(
    title="AI Education Platform",
    description="教育平台 - 基于AI服务平台",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# AI网关地址
ai_gateway_url = os.getenv("AI_GATEWAY_URL", "http://ai-gateway:8000")
http_client = httpx.AsyncClient(timeout=30)


@app.on_event("startup")
async def startup_event():
    """启动事件"""
    print("🚀 教育平台启动中...")
    
    # 测试AI服务连接
    try:
        response = await http_client.get(f"{ai_gateway_url}/health")
        if response.status_code == 200:
            print(f"✅ AI服务连接成功: {ai_gateway_url}")
        else:
            print(f"⚠️ AI服务连接异常: {response.status_code}")
    except Exception as e:
        print(f"⚠️ AI服务连接失败: {e}")
    
    print("✅ 教育平台启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """关闭事件"""
    await http_client.aclose()
    print("✅ 教育平台关闭完成")


async def call_ai_service(endpoint: str, payload: dict, headers: dict = None) -> dict:
    """调用AI服务"""
    default_headers = {
        "Content-Type": "application/json",
        "X-Tenant-ID": "education"
    }
    if headers:
        default_headers.update(headers)
    
    try:
        response = await http_client.post(
            f"{ai_gateway_url}{endpoint}",
            json=payload,
            headers=default_headers
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI服务调用失败: {str(e)}")


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "ai-education-app",
        "version": "1.0.0",
        "timestamp": time.time(),
        "ai_gateway": ai_gateway_url
    }


@app.post("/api/homework/ocr")
async def process_homework(file: UploadFile = File(...)):
    """作业图片识别"""
    try:
        # 读取图片
        image_data = await file.read()
        
        # 转换为base64
        import base64
        image_base64 = base64.b64encode(image_data).decode()
        
        # 调用OCR服务
        result = await call_ai_service(
            "/api/v1/ocr",
            {"image_data": image_base64}
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=500, detail=f"OCR失败: {result.get('error')}")
        
        return {
            "success": True,
            "text": result["data"].get("text", ""),
            "metadata": result.get("metadata", {})
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/questions/generate")
async def generate_questions(request: GenerateQuestionRequest):
    """智能出题"""
    try:
        # 构建提示
        prompt = f"""
        请生成{request.count}道关于"{request.topic}"的{request.subject}题目，难度为{request.difficulty}。
        
        要求：
        1. 题目内容准确
        2. 难度适中
        3. 包含答案
        4. 格式规范
        
        请以JSON格式返回题目列表，每个题目包含：
        - question: 题目内容
        - options: 选项（如果是选择题）
        - answer: 正确答案
        - explanation: 解析
        """
        
        # 调用LLM服务
        result = await call_ai_service(
            "/api/v1/llm",
            {
                "messages": [
                    {"role": "system", "content": "你是一个专业的教育AI助手，擅长生成高质量的教学题目。"},
                    {"role": "user", "content": prompt}
                ]
            }
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=500, detail=f"生成失败: {result.get('error')}")
        
        return {
            "success": True,
            "questions": result["data"].get("content", ""),
            "metadata": {
                **result.get("metadata", {}),
                "topic": request.topic,
                "subject": request.subject,
                "difficulty": request.difficulty,
                "count": request.count
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/voice/generate")
async def generate_voice(text: str = Form(...)):
    """生成语音解释"""
    try:
        # 调用TTS服务
        result = await call_ai_service(
            "/api/v1/tts",
            {
                "text": text,
                "voice": "zh-CN-XiaoxiaoNeural"
            }
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=500, detail=f"TTS失败: {result.get('error')}")
        
        return {
            "success": True,
            "audio_url": result["data"].get("audio_url", ""),
            "metadata": result.get("metadata", {})
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/chat")
async def chat_with_ai(message: str = Form(...)):
    """AI助教对话"""
    try:
        messages = [
            {"role": "system", "content": "你是一个专业的教育AI助手，帮助学生学习。请用简洁易懂的语言回答问题。"},
            {"role": "user", "content": message}
        ]
        
        # 调用LLM服务
        result = await call_ai_service(
            "/api/v1/llm",
            {"messages": messages}
        )
        
        if not result.get("success"):
            raise HTTPException(status_code=500, detail=f"对话失败: {result.get('error')}")
        
        return {
            "success": True,
            "reply": result["data"].get("content", ""),
            "metadata": result.get("metadata", {})
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/stats")
async def get_stats():
    """获取统计信息"""
    try:
        # 获取AI服务状态
        response = await http_client.get(f"{ai_gateway_url}/api/v1/services")
        ai_services = response.json() if response.status_code == 200 else {}
        
        return {
            "platform": "education",
            "version": "1.0.0",
            "ai_gateway": ai_gateway_url,
            "ai_services": ai_services,
            "uptime": time.time() - getattr(app.state, 'start_time', time.time()),
            "features": [
                "homework_ocr",
                "question_generation",
                "voice_explanation",
                "ai_tutoring"
            ]
        }
    except Exception as e:
        return {
            "platform": "education",
            "version": "1.0.0",
            "error": str(e)
        }


# 记录启动时间
@app.middleware("http")
async def add_startup_time(request, call_next):
    if not hasattr(app.state, 'start_time'):
        app.state.start_time = time.time()
    response = await call_next(request)
    return response


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8100,
        reload=True
    )
