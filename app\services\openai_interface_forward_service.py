import httpx
from fastapi.responses import <PERSON>ing<PERSON><PERSON>po<PERSON>, Response
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Header
from urllib.parse import urljoin
from typing import AsyncGenerator, Dict, Any

from app.services.abs_service import AbsService
from app.utils.util import create_forward_url



class OpenaiInterfaceForwardService(AbsService):
    def __init__(self):
        super().__init__()


    async def route_request(self, request: Request):
        print('request.url.path:', request.url.path)
        # 1. 匹配最长的前缀规则
        path = request.url.path


        method = request.method.upper()
        try:
            if method == "POST":
                client_body: Dict[str, Any] = await request.json()
            elif method == "GET":
                client_body = dict(request.query_params)
        except Exception as e:
            client_body = {}
                
        model = client_body.get("model", None)
        # 3. 构建转发URL
        target_url, model = create_forward_url(model, path, client_body.get("business_type", None))
        print('target_url:', target_url)
        self.logger.info(f"Forwarding request to {target_url}, headers: {request.headers}")
        timeout = 120
        if method == "GET":
            headers = {}
            headers['X-MODEL'] = model
            async with httpx.AsyncClient(timeout=timeout) as client:
                target_response = await client.get(
                    url=target_url,
                    headers=headers
                )
                target_response.raise_for_status()
                print("target_response:", target_response)
                try:
                    return target_response.json()
                except Exception as e:
                    return Response(content=target_response.content, media_type=target_response.headers.get("Content-Type"))
        else:

            if model.startswith("Qwen3") and "chat_template_kwargs" not in client_body:
                client_body["chat_template_kwargs"] = {
                    "enable_thinking": False
                }

            try:
                # 为了让nginx根据不同的模型做不同的转发，这里将模型信息放到header中
                headers = {}
                headers['X-MODEL'] = model
                headers['Content-Type'] = "application/json"
                
                print("modified headers:", headers)
                print("modified client_body:", client_body)

                
                is_stream = client_body.get("stream", False)
                

                # 3. 处理流式响应（关键部分）
                if is_stream:
                    async def stream_generator() -> AsyncGenerator[str, None]:
                        async with httpx.AsyncClient(timeout=timeout) as client:
                            # 发送流式POST请求到目标服务
                            async with client.stream("POST", target_url, json=client_body, headers=headers) as target_response:
                                # 逐块读取目标服务的响应并透传给客户端
                                target_response.raise_for_status()
                                async for chunk in target_response.aiter_raw():
                                    yield chunk.decode()  # 转换为字符串流（假设目标返回text/event-stream格式）
                    
                    return StreamingResponse(
                        stream_generator(),
                        media_type="text/event-stream"
                    )
                else:
                    # 4. 处理非流式请求
                    async with httpx.AsyncClient(timeout=timeout) as client:
                        target_response = await client.post(
                            target_url,
                            json=client_body,
                            headers=headers
                        )
                        target_response.raise_for_status()
                        return target_response.json()  # 直接返回目标服务的JSON响应

            except httpx.ConnectError:
                raise HTTPException(status_code=503, detail="Target service unreachable")
            except httpx.TimeoutException:
                raise HTTPException(status_code=504, detail="Target service timeout")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

