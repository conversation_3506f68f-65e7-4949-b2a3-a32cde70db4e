version: '3.8'

# FunASR官方部署 - Docker Compose配置
# 使用官方镜像和官方HTTP服务

services:
  # FunASR官方实例1 - GPU 0
  funasr-gpu-0:
    image: ${FUNASR_IMAGE:-registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1}
    container_name: funasr-official-gpu-0
    ports:
      - "${BASE_PORT:-10095}:10095"
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - WORKERS=${WORKERS_PER_INSTANCE:-4}
      - MAX_REQUESTS=${MAX_REQUESTS:-1000}
      - TIMEOUT=${TIMEOUT:-300}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR官方服务 GPU-0...' &&
        python server.py --host 0.0.0.0 --port 10095
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['0']
              capabilities: [gpu]
        limits:
          memory: ${MEMORY_LIMIT:-16G}
          cpus: '${CPU_LIMIT:-8.0}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10095/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
      start_period: ${HEALTH_CHECK_START_PERIOD:-120s}
    networks:
      - funasr-network

  # FunASR官方实例2 - GPU 1
  funasr-gpu-1:
    image: ${FUNASR_IMAGE:-registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1}
    container_name: funasr-official-gpu-1
    ports:
      - "10096:10095"
    environment:
      - CUDA_VISIBLE_DEVICES=1
      - WORKERS=${WORKERS_PER_INSTANCE:-4}
      - MAX_REQUESTS=${MAX_REQUESTS:-1000}
      - TIMEOUT=${TIMEOUT:-300}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR官方服务 GPU-1...' &&
        python server.py --host 0.0.0.0 --port 10095
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['1']
              capabilities: [gpu]
        limits:
          memory: ${MEMORY_LIMIT:-16G}
          cpus: '${CPU_LIMIT:-8.0}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10095/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
      start_period: ${HEALTH_CHECK_START_PERIOD:-120s}
    networks:
      - funasr-network

  # FunASR官方实例3 - GPU 2
  funasr-gpu-2:
    image: ${FUNASR_IMAGE:-registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1}
    container_name: funasr-official-gpu-2
    ports:
      - "10097:10095"
    environment:
      - CUDA_VISIBLE_DEVICES=2
      - WORKERS=${WORKERS_PER_INSTANCE:-4}
      - MAX_REQUESTS=${MAX_REQUESTS:-1000}
      - TIMEOUT=${TIMEOUT:-300}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR官方服务 GPU-2...' &&
        python server.py --host 0.0.0.0 --port 10095
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['2']
              capabilities: [gpu]
        limits:
          memory: ${MEMORY_LIMIT:-16G}
          cpus: '${CPU_LIMIT:-8.0}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10095/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
      start_period: ${HEALTH_CHECK_START_PERIOD:-120s}
    networks:
      - funasr-network

  # FunASR官方实例4 - GPU 3
  funasr-gpu-3:
    image: ${FUNASR_IMAGE:-registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-gpu-0.2.1}
    container_name: funasr-official-gpu-3
    ports:
      - "10098:10095"
    environment:
      - CUDA_VISIBLE_DEVICES=3
      - WORKERS=${WORKERS_PER_INSTANCE:-4}
      - MAX_REQUESTS=${MAX_REQUESTS:-1000}
      - TIMEOUT=${TIMEOUT:-300}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    working_dir: /workspace/FunASR/runtime/python/http
    command: >
      bash -c "
        echo '🚀 启动FunASR官方服务 GPU-3...' &&
        python server.py --host 0.0.0.0 --port 10095
      "
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['3']
              capabilities: [gpu]
        limits:
          memory: ${MEMORY_LIMIT:-16G}
          cpus: '${CPU_LIMIT:-8.0}'
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10095/health"]
      interval: ${HEALTH_CHECK_INTERVAL:-30s}
      timeout: ${HEALTH_CHECK_TIMEOUT:-10s}
      retries: ${HEALTH_CHECK_RETRIES:-3}
      start_period: ${HEALTH_CHECK_START_PERIOD:-120s}
    networks:
      - funasr-network

  # Nginx负载均衡器
  funasr-loadbalancer:
    image: nginx:alpine
    container_name: funasr-official-lb
    ports:
      - "${NGINX_PORT:-8080}:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - funasr-gpu-0
      - funasr-gpu-1
      - funasr-gpu-2
      - funasr-gpu-3
    restart: unless-stopped
    networks:
      - funasr-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: funasr-official-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=${LOG_RETENTION_DAYS:-7}d'
    networks:
      - funasr-network
    profiles:
      - monitoring

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    container_name: funasr-official-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - funasr-network
    profiles:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  funasr-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
